{"version": 3, "sources": ["../../../../../node_modules/@trpc/server/src/observable/observable.ts", "../../../../../node_modules/@trpc/server/src/observable/operators.ts", "../../../../../node_modules/@trpc/server/src/observable/behaviorSubject.ts", "../../../../../node_modules/@trpc/client/src/links/internals/createChain.ts", "../../../../../node_modules/@trpc/client/src/links/splitLink.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/rpc/codes.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/utils.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/createProxy.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/http/getHTTPStatusCode.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/error/getErrorShape.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/error/formatter.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/error/TRPCError.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/transformer.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/router.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/stream/tracked.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/http/parseConnectionParams.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/http/contentType.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/http/abortError.ts", "../../../../../node_modules/@trpc/server/src/vendor/unpromise/unpromise.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/stream/utils/disposable.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/stream/utils/timerResource.ts", "../../../../../node_modules/node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/usingCtx.js", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/stream/utils/asyncIterable.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/stream/utils/createDeferred.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/stream/utils/mergeAsyncIterables.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/stream/utils/readableStreamFrom.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/stream/utils/withPing.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/stream/jsonl.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/stream/sse.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/http/resolveResponse.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/middleware.ts", "../../../../../node_modules/@trpc/server/src/vendor/standard-schema-v1/error.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/parser.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/procedureBuilder.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/rootConfig.ts", "../../../../../node_modules/@trpc/server/src/unstable-core-do-not-import/initTRPC.ts", "../../../../../node_modules/@trpc/client/src/TRPCClientError.ts", "../../../../../node_modules/@trpc/client/src/internals/transformer.ts", "../../../../../node_modules/@trpc/client/src/getFetch.ts", "../../../../../node_modules/@trpc/client/src/links/internals/httpUtils.ts", "../../../../../node_modules/@trpc/client/src/links/internals/contentTypes.ts", "../../../../../node_modules/@trpc/client/src/links/httpLink.ts", "../../../../../node_modules/@trpc/client/src/internals/dataLoader.ts", "../../../../../node_modules/@trpc/client/src/internals/signals.ts", "../../../../../node_modules/@trpc/client/src/links/httpBatchLink.ts", "../../../../../node_modules/@trpc/client/src/links/loggerLink.ts", "../../../../../node_modules/@trpc/client/src/links/wsLink/wsClient/options.ts", "../../../../../node_modules/@trpc/client/src/links/internals/urlWithConnectionParams.ts", "../../../../../node_modules/@trpc/client/src/links/wsLink/wsClient/utils.ts", "../../../../../node_modules/@trpc/client/src/links/wsLink/wsClient/requestManager.ts", "../../../../../node_modules/@trpc/client/src/links/wsLink/wsClient/wsConnection.ts", "../../../../../node_modules/@trpc/client/src/links/wsLink/wsClient/wsClient.ts", "../../../../../node_modules/@trpc/client/src/links/wsLink/createWsClient.ts", "../../../../../node_modules/@trpc/client/src/links/wsLink/wsLink.ts", "../../../../../node_modules/@trpc/client/src/internals/TRPCUntypedClient.ts", "../../../../../node_modules/@trpc/client/src/createTRPCUntypedClient.ts", "../../../../../node_modules/@trpc/client/src/createTRPCClient.ts", "../../../../../node_modules/@trpc/client/src/links/httpBatchStreamLink.ts", "../../../../../node_modules/@trpc/client/src/internals/inputWithTrackedEventId.ts", "../../../../../node_modules/@trpc/client/src/links/httpSubscriptionLink.ts", "../../../../../node_modules/@trpc/client/src/links/retryLink.ts", "../../../../../node_modules/node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/usingCtx.js", "../../../../../node_modules/@trpc/client/src/links/localLink.ts"], "sourcesContent": ["import type { Result } from '../unstable-core-do-not-import';\nimport type {\n  Observable,\n  Observer,\n  OperatorFunction,\n  TeardownLogic,\n  UnaryFunction,\n  Unsubscribable,\n} from './types';\n\n/** @public */\nexport type inferObservableValue<TObservable> =\n  TObservable extends Observable<infer TValue, unknown> ? TValue : never;\n\n/** @public */\nexport function isObservable(x: unknown): x is Observable<unknown, unknown> {\n  return typeof x === 'object' && x !== null && 'subscribe' in x;\n}\n\n/** @public */\nexport function observable<TValue, TError = unknown>(\n  subscribe: (observer: Observer<TValue, TError>) => TeardownLogic,\n): Observable<TValue, TError> {\n  const self: Observable<TValue, TError> = {\n    subscribe(observer) {\n      let teardownRef: TeardownLogic | null = null;\n      let isDone = false;\n      let unsubscribed = false;\n      let teardownImmediately = false;\n      function unsubscribe() {\n        if (teardownRef === null) {\n          teardownImmediately = true;\n          return;\n        }\n        if (unsubscribed) {\n          return;\n        }\n        unsubscribed = true;\n\n        if (typeof teardownRef === 'function') {\n          teardownRef();\n        } else if (teardownRef) {\n          teardownRef.unsubscribe();\n        }\n      }\n      teardownRef = subscribe({\n        next(value) {\n          if (isDone) {\n            return;\n          }\n          observer.next?.(value);\n        },\n        error(err) {\n          if (isDone) {\n            return;\n          }\n          isDone = true;\n          observer.error?.(err);\n          unsubscribe();\n        },\n        complete() {\n          if (isDone) {\n            return;\n          }\n          isDone = true;\n          observer.complete?.();\n          unsubscribe();\n        },\n      });\n      if (teardownImmediately) {\n        unsubscribe();\n      }\n      return {\n        unsubscribe,\n      };\n    },\n    pipe(\n      ...operations: OperatorFunction<any, any, any, any>[]\n    ): Observable<any, any> {\n      return operations.reduce(pipeReducer, self);\n    },\n  };\n  return self;\n}\n\nfunction pipeReducer(prev: any, fn: UnaryFunction<any, any>) {\n  return fn(prev);\n}\n\n/** @internal */\nexport function observableToPromise<TValue>(\n  observable: Observable<TValue, unknown>,\n) {\n  const ac = new AbortController();\n  const promise = new Promise<TValue>((resolve, reject) => {\n    let isDone = false;\n    function onDone() {\n      if (isDone) {\n        return;\n      }\n      isDone = true;\n      obs$.unsubscribe();\n    }\n    ac.signal.addEventListener('abort', () => {\n      reject(ac.signal.reason);\n    });\n    const obs$ = observable.subscribe({\n      next(data) {\n        isDone = true;\n        resolve(data);\n        onDone();\n      },\n      error(data) {\n        reject(data);\n      },\n      complete() {\n        ac.abort();\n        onDone();\n      },\n    });\n  });\n  return promise;\n}\n\n/**\n * @internal\n */\nfunction observableToReadableStream<TValue>(\n  observable: Observable<TValue, unknown>,\n  signal: AbortSignal,\n): ReadableStream<Result<TValue>> {\n  let unsub: Unsubscribable | null = null;\n\n  const onAbort = () => {\n    unsub?.unsubscribe();\n    unsub = null;\n    signal.removeEventListener('abort', onAbort);\n  };\n\n  return new ReadableStream<Result<TValue>>({\n    start(controller) {\n      unsub = observable.subscribe({\n        next(data) {\n          controller.enqueue({ ok: true, value: data });\n        },\n        error(error) {\n          controller.enqueue({ ok: false, error });\n          controller.close();\n        },\n        complete() {\n          controller.close();\n        },\n      });\n\n      if (signal.aborted) {\n        onAbort();\n      } else {\n        signal.addEventListener('abort', onAbort, { once: true });\n      }\n    },\n    cancel() {\n      onAbort();\n    },\n  });\n}\n\n/** @internal */\nexport function observableToAsyncIterable<TValue>(\n  observable: Observable<TValue, unknown>,\n  signal: AbortSignal,\n): AsyncIterable<TValue> {\n  const stream = observableToReadableStream(observable, signal);\n\n  const reader = stream.getReader();\n  const iterator: AsyncIterator<TValue> = {\n    async next() {\n      const value = await reader.read();\n      if (value.done) {\n        return {\n          value: undefined,\n          done: true,\n        };\n      }\n      const { value: result } = value;\n      if (!result.ok) {\n        throw result.error;\n      }\n      return {\n        value: result.value,\n        done: false,\n      };\n    },\n    async return() {\n      await reader.cancel();\n      return {\n        value: undefined,\n        done: true,\n      };\n    },\n  };\n  return {\n    [Symbol.asyncIterator]() {\n      return iterator;\n    },\n  };\n}\n", "import { observable } from './observable';\nimport type {\n  MonoTypeOperatorFunction,\n  Observer,\n  OperatorFunction,\n  Unsubscribable,\n} from './types';\n\nexport function map<TValueBefore, TError, TValueAfter>(\n  project: (value: TValueBefore, index: number) => TValueAfter,\n): OperatorFunction<TValueBefore, TError, TValueAfter, TError> {\n  return (source) => {\n    return observable((destination) => {\n      let index = 0;\n      const subscription = source.subscribe({\n        next(value) {\n          destination.next(project(value, index++));\n        },\n        error(error) {\n          destination.error(error);\n        },\n        complete() {\n          destination.complete();\n        },\n      });\n      return subscription;\n    });\n  };\n}\n\ninterface ShareConfig {}\nexport function share<TValue, TError>(\n  _opts?: ShareConfig,\n): MonoTypeOperatorFunction<TValue, TError> {\n  return (source) => {\n    let refCount = 0;\n\n    let subscription: Unsubscribable | null = null;\n    const observers: Partial<Observer<TValue, TError>>[] = [];\n\n    function startIfNeeded() {\n      if (subscription) {\n        return;\n      }\n      subscription = source.subscribe({\n        next(value) {\n          for (const observer of observers) {\n            observer.next?.(value);\n          }\n        },\n        error(error) {\n          for (const observer of observers) {\n            observer.error?.(error);\n          }\n        },\n        complete() {\n          for (const observer of observers) {\n            observer.complete?.();\n          }\n        },\n      });\n    }\n    function resetIfNeeded() {\n      // \"resetOnRefCountZero\"\n      if (refCount === 0 && subscription) {\n        const _sub = subscription;\n        subscription = null;\n        _sub.unsubscribe();\n      }\n    }\n\n    return observable((subscriber) => {\n      refCount++;\n\n      observers.push(subscriber);\n      startIfNeeded();\n      return {\n        unsubscribe() {\n          refCount--;\n          resetIfNeeded();\n\n          const index = observers.findIndex((v) => v === subscriber);\n\n          if (index > -1) {\n            observers.splice(index, 1);\n          }\n        },\n      };\n    });\n  };\n}\n\nexport function tap<TValue, TError>(\n  observer: Partial<Observer<TValue, TError>>,\n): MonoTypeOperatorFunction<TValue, TError> {\n  return (source) => {\n    return observable((destination) => {\n      return source.subscribe({\n        next(value) {\n          observer.next?.(value);\n          destination.next(value);\n        },\n        error(error) {\n          observer.error?.(error);\n          destination.error(error);\n        },\n        complete() {\n          observer.complete?.();\n          destination.complete();\n        },\n      });\n    });\n  };\n}\n\nconst distinctUnsetMarker = Symbol();\nexport function distinctUntilChanged<TValue, TError>(\n  compare: (a: TValue, b: TValue) => boolean = (a, b) => a === b,\n): MonoTypeOperatorFunction<TValue, TError> {\n  return (source) => {\n    return observable((destination) => {\n      let lastValue: TValue | typeof distinctUnsetMarker = distinctUnsetMarker;\n\n      return source.subscribe({\n        next(value) {\n          if (lastValue !== distinctUnsetMarker && compare(lastValue, value)) {\n            return;\n          }\n          lastValue = value;\n          destination.next(value);\n        },\n        error(error) {\n          destination.error(error);\n        },\n        complete() {\n          destination.complete();\n        },\n      });\n    });\n  };\n}\n\nconst isDeepEqual = <T>(a: T, b: T): boolean => {\n  if (a === b) {\n    return true;\n  }\n  const bothAreObjects =\n    a && b && typeof a === 'object' && typeof b === 'object';\n\n  return (\n    !!bothAreObjects &&\n    Object.keys(a).length === Object.keys(b).length &&\n    Object.entries(a).every(([k, v]) => isDeepEqual(v, b[k as keyof T]))\n  );\n};\nexport function distinctUntilDeepChanged<\n  TValue,\n  TError,\n>(): MonoTypeOperatorFunction<TValue, TError> {\n  return distinctUntilChanged(isDeepEqual);\n}\n", "import { observable } from './observable';\nimport type { Observable, Observer } from './types';\n\nexport interface BehaviorSubject<TValue> extends Observable<TValue, never> {\n  observable: Observable<TValue, never>;\n  next: (value: TValue) => void;\n  get: () => TValue;\n}\n\nexport interface ReadonlyBehaviorSubject<TValue>\n  extends Omit<BehaviorSubject<TValue>, 'next'> {}\n\n/**\n * @internal\n * An observable that maintains and provides a \"current value\" to subscribers\n * @see https://www.learnrxjs.io/learn-rxjs/subjects/behaviorsubject\n */\nexport function behaviorSubject<TValue>(\n  initialValue: TValue,\n): BehaviorSubject<TValue> {\n  let value: TValue = initialValue;\n\n  const observerList: Observer<TValue, never>[] = [];\n\n  const addObserver = (observer: Observer<TValue, never>) => {\n    if (value !== undefined) {\n      observer.next(value);\n    }\n    observerList.push(observer);\n  };\n  const removeObserver = (observer: Observer<TValue, never>) => {\n    observerList.splice(observerList.indexOf(observer), 1);\n  };\n\n  const obs = observable<TValue, never>((observer) => {\n    addObserver(observer);\n    return () => {\n      removeObserver(observer);\n    };\n  }) as BehaviorSubject<TValue>;\n\n  obs.next = (nextValue: TValue) => {\n    if (value === nextValue) {\n      return;\n    }\n    value = nextValue;\n    for (const observer of observerList) {\n      observer.next(nextValue);\n    }\n  };\n\n  obs.get = () => value;\n\n  return obs;\n}\n", "import { observable } from '@trpc/server/observable';\nimport type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport type {\n  Operation,\n  OperationLink,\n  OperationResultObservable,\n} from '../types';\n\n/** @internal */\nexport function create<PERSON>hain<\n  TRouter extends AnyRouter,\n  TInput = unknown,\n  TOutput = unknown,\n>(opts: {\n  links: OperationLink<TRouter, TInput, TOutput>[];\n  op: Operation<TInput>;\n}): OperationResultObservable<TRouter, TOutput> {\n  return observable((observer) => {\n    function execute(index = 0, op = opts.op) {\n      const next = opts.links[index];\n      if (!next) {\n        throw new Error(\n          'No more links to execute - did you forget to add an ending link?',\n        );\n      }\n      const subscription = next({\n        op,\n        next(nextOp) {\n          const nextObserver = execute(index + 1, nextOp);\n\n          return nextObserver;\n        },\n      });\n      return subscription;\n    }\n\n    const obs$ = execute();\n    return obs$.subscribe(observer);\n  });\n}\n", "import { observable } from '@trpc/server/observable';\nimport type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport { create<PERSON>hain } from './internals/createChain';\nimport type { Operation, TRPCLink } from './types';\n\nfunction asArray<TType>(value: TType | TType[]) {\n  return Array.isArray(value) ? value : [value];\n}\nexport function splitLink<TRouter extends AnyRouter = AnyRouter>(opts: {\n  condition: (op: Operation) => boolean;\n  /**\n   * The link to execute next if the test function returns `true`.\n   */\n  true: TRPCLink<TRouter> | TRPCLink<TRouter>[];\n  /**\n   * The link to execute next if the test function returns `false`.\n   */\n  false: TRPCLink<TRouter> | TRPCLink<TRouter>[];\n}): TRPCLink<TRouter> {\n  return (runtime) => {\n    const yes = asArray(opts.true).map((link) => link(runtime));\n    const no = asArray(opts.false).map((link) => link(runtime));\n    return (props) => {\n      return observable((observer) => {\n        const links = opts.condition(props.op) ? yes : no;\n        return createChain({ op: props.op, links }).subscribe(observer);\n      });\n    };\n  };\n}\n", "import type { InvertKeyValue, ValueOf } from '../types';\n\n// reference: https://www.jsonrpc.org/specification\n\n/**\n * JSON-RPC 2.0 Error codes\n *\n * `-32000` to `-32099` are reserved for implementation-defined server-errors.\n * For tRPC we're copying the last digits of HTTP 4XX errors.\n */\nexport const TRPC_ERROR_CODES_BY_KEY = {\n  /**\n   * Invalid JSON was received by the server.\n   * An error occurred on the server while parsing the JSON text.\n   */\n  PARSE_ERROR: -32700,\n  /**\n   * The JSON sent is not a valid Request object.\n   */\n  BAD_REQUEST: -32600, // 400\n\n  // Internal JSON-RPC error\n  INTERNAL_SERVER_ERROR: -32603, // 500\n  NOT_IMPLEMENTED: -32603, // 501\n  BAD_GATEWAY: -32603, // 502\n  SERVICE_UNAVAILABLE: -32603, // 503\n  GATEWAY_TIMEOUT: -32603, // 504\n\n  // Implementation specific errors\n  UNAUTHORIZED: -32001, // 401\n  PAYMENT_REQUIRED: -32002, // 402\n  FORBIDDEN: -32003, // 403\n  NOT_FOUND: -32004, // 404\n  METHOD_NOT_SUPPORTED: -32005, // 405\n  TIMEOUT: -32008, // 408\n  CONFLICT: -32009, // 409\n  PRECONDITION_FAILED: -32012, // 412\n  PAYLOAD_TOO_LARGE: -32013, // 413\n  UNSUPPORTED_MEDIA_TYPE: -32015, // 415\n  UNPROCESSABLE_CONTENT: -32022, // 422\n  TOO_MANY_REQUESTS: -32029, // 429\n  CLIENT_CLOSED_REQUEST: -32099, // 499\n} as const;\n\n// pure\nexport const TRPC_ERROR_CODES_BY_NUMBER: InvertKeyValue<\n  typeof TRPC_ERROR_CODES_BY_KEY\n> = {\n  [-32700]: 'PARSE_ERROR',\n  [-32600]: 'BAD_REQUEST',\n  [-32603]: 'INTERNAL_SERVER_ERROR',\n  [-32001]: 'UNAUTHORIZED',\n  [-32002]: 'PAYMENT_REQUIRED',\n  [-32003]: 'FORBIDDEN',\n  [-32004]: 'NOT_FOUND',\n  [-32005]: 'METHOD_NOT_SUPPORTED',\n  [-32008]: 'TIMEOUT',\n  [-32009]: 'CONFLICT',\n  [-32012]: 'PRECONDITION_FAILED',\n  [-32013]: 'PAYLOAD_TOO_LARGE',\n  [-32015]: 'UNSUPPORTED_MEDIA_TYPE',\n  [-32022]: 'UNPROCESSABLE_CONTENT',\n  [-32029]: 'TOO_MANY_REQUESTS',\n  [-32099]: 'CLIENT_CLOSED_REQUEST',\n};\n\nexport type TRPC_ERROR_CODE_NUMBER = ValueOf<typeof TRPC_ERROR_CODES_BY_KEY>;\nexport type TRPC_ERROR_CODE_KEY = keyof typeof TRPC_ERROR_CODES_BY_KEY;\n\n/**\n * tRPC error codes that are considered retryable\n * With out of the box SSE, the client will reconnect when these errors are encountered\n */\nexport const retryableRpcCodes: TRPC_ERROR_CODE_NUMBER[] = [\n  TRPC_ERROR_CODES_BY_KEY.BAD_GATEWAY,\n  TRPC_ERROR_CODES_BY_KEY.SERVICE_UNAVAILABLE,\n  TRPC_ERROR_CODES_BY_KEY.GATEWAY_TIMEOUT,\n  TRPC_ERROR_CODES_BY_KEY.INTERNAL_SERVER_ERROR,\n];\n", "/** @internal */\nexport type UnsetMarker = 'unsetMarker' & {\n  __brand: 'unsetMarker';\n};\n\n/**\n * Ensures there are no duplicate keys when building a procedure.\n * @internal\n */\nexport function mergeWithoutOverrides<TType extends Record<string, unknown>>(\n  obj1: TType,\n  ...objs: Partial<TType>[]\n): TType {\n  const newObj: TType = Object.assign(Object.create(null), obj1);\n\n  for (const overrides of objs) {\n    for (const key in overrides) {\n      if (key in newObj && newObj[key] !== overrides[key]) {\n        throw new Error(`Duplicate key ${key}`);\n      }\n      newObj[key as keyof TType] = overrides[key] as TType[keyof TType];\n    }\n  }\n  return newObj;\n}\n\n/**\n * Check that value is object\n * @internal\n */\nexport function isObject(value: unknown): value is Record<string, unknown> {\n  return !!value && !Array.isArray(value) && typeof value === 'object';\n}\n\ntype AnyFn = ((...args: any[]) => unknown) & Record<keyof any, unknown>;\nexport function isFunction(fn: unknown): fn is AnyFn {\n  return typeof fn === 'function';\n}\n\n/**\n * Create an object without inheriting anything from `Object.prototype`\n * @internal\n */\nexport function omitPrototype<TObj extends Record<string, unknown>>(\n  obj: TObj,\n): TObj {\n  return Object.assign(Object.create(null), obj);\n}\n\nconst asyncIteratorsSupported =\n  typeof Symbol === 'function' && !!Symbol.asyncIterator;\n\nexport function isAsyncIterable<TValue>(\n  value: unknown,\n): value is AsyncIterable<TValue> {\n  return (\n    asyncIteratorsSupported && isObject(value) && Symbol.asyncIterator in value\n  );\n}\n\n/**\n * Run an IIFE\n */\nexport const run = <TValue>(fn: () => TValue): TValue => fn();\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nexport function noop(): void {}\n\nexport function identity<T>(it: T): T {\n  return it;\n}\n\n/**\n * Generic runtime assertion function. Throws, if the condition is not `true`.\n *\n * Can be used as a slightly less dangerous variant of type assertions. Code\n * mistakes would be revealed at runtime then (hopefully during testing).\n */\nexport function assert(\n  condition: boolean,\n  msg = 'no additional info',\n): asserts condition {\n  if (!condition) {\n    throw new Error(`AssertionError: ${msg}`);\n  }\n}\n\nexport function sleep(ms = 0): Promise<void> {\n  return new Promise<void>((res) => setTimeout(res, ms));\n}\n\n/**\n * Ponyfill for\n * [`AbortSignal.any`](https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/any_static).\n */\nexport function abortSignalsAnyPonyfill(signals: AbortSignal[]): AbortSignal {\n  if (typeof AbortSignal.any === 'function') {\n    return AbortSignal.any(signals);\n  }\n\n  const ac = new AbortController();\n\n  for (const signal of signals) {\n    if (signal.aborted) {\n      trigger();\n      break;\n    }\n    signal.addEventListener('abort', trigger, { once: true });\n  }\n\n  return ac.signal;\n\n  function trigger() {\n    ac.abort();\n    for (const signal of signals) {\n      signal.removeEventListener('abort', trigger);\n    }\n  }\n}\n", "interface ProxyCallbackOptions {\n  path: readonly string[];\n  args: readonly unknown[];\n}\ntype ProxyCallback = (opts: ProxyCallbackOptions) => unknown;\n\nconst noop = () => {\n  // noop\n};\n\nconst freezeIfAvailable = (obj: object) => {\n  if (Object.freeze) {\n    Object.freeze(obj);\n  }\n};\n\nfunction createInnerProxy(\n  callback: ProxyCallback,\n  path: readonly string[],\n  memo: Record<string, unknown>,\n) {\n  const cacheKey = path.join('.');\n\n  memo[cacheKey] ??= new Proxy(noop, {\n    get(_obj, key) {\n      if (typeof key !== 'string' || key === 'then') {\n        // special case for if the proxy is accidentally treated\n        // like a PromiseLike (like in `Promise.resolve(proxy)`)\n        return undefined;\n      }\n      return createInnerProxy(callback, [...path, key], memo);\n    },\n    apply(_1, _2, args) {\n      const lastOfPath = path[path.length - 1];\n\n      let opts = { args, path };\n      // special handling for e.g. `trpc.hello.call(this, 'there')` and `trpc.hello.apply(this, ['there'])\n      if (lastOfPath === 'call') {\n        opts = {\n          args: args.length >= 2 ? [args[1]] : [],\n          path: path.slice(0, -1),\n        };\n      } else if (lastOfPath === 'apply') {\n        opts = {\n          args: args.length >= 2 ? args[1] : [],\n          path: path.slice(0, -1),\n        };\n      }\n      freezeIfAvailable(opts.args);\n      freezeIfAvailable(opts.path);\n      return callback(opts);\n    },\n  });\n\n  return memo[cacheKey];\n}\n\n/**\n * Creates a proxy that calls the callback with the path and arguments\n *\n * @internal\n */\nexport const createRecursiveProxy = <TFaux = unknown>(\n  callback: ProxyCallback,\n): TFaux => createInnerProxy(callback, [], Object.create(null)) as TFaux;\n\n/**\n * Used in place of `new Proxy` where each handler will map 1 level deep to another value.\n *\n * @internal\n */\nexport const createFlatProxy = <TFaux>(\n  callback: (path: keyof TFaux) => any,\n): TFaux => {\n  return new Proxy(noop, {\n    get(_obj, name) {\n      if (name === 'then') {\n        // special case for if the proxy is accidentally treated\n        // like a PromiseLike (like in `Promise.resolve(proxy)`)\n        return undefined;\n      }\n      return callback(name as any);\n    },\n  }) as TFaux;\n};\n", "import type { TRPCError } from '../error/TRPCError';\nimport type { TRPC_ERROR_CODES_BY_KEY, TRPCResponse } from '../rpc';\nimport { TRPC_ERROR_CODES_BY_NUMBER } from '../rpc';\nimport type { InvertKeyValue, ValueOf } from '../types';\nimport { isObject } from '../utils';\n\nexport const JSONRPC2_TO_HTTP_CODE: Record<\n  keyof typeof TRPC_ERROR_CODES_BY_KEY,\n  number\n> = {\n  PARSE_ERROR: 400,\n  BAD_REQUEST: 400,\n  UNAUTHORIZED: 401,\n  PAYMENT_REQUIRED: 402,\n  FORBIDDEN: 403,\n  NOT_FOUND: 404,\n  METHOD_NOT_SUPPORTED: 405,\n  TIMEOUT: 408,\n  CONFLICT: 409,\n  PRECONDITION_FAILED: 412,\n  PAYLOAD_TOO_LARGE: 413,\n  UNSUPPORTED_MEDIA_TYPE: 415,\n  UNPROCESSABLE_CONTENT: 422,\n  TOO_MANY_REQUESTS: 429,\n  CLIENT_CLOSED_REQUEST: 499,\n  INTERNAL_SERVER_ERROR: 500,\n  NOT_IMPLEMENTED: 501,\n  BAD_GATEWAY: 502,\n  SERVICE_UNAVAILABLE: 503,\n  GATEWAY_TIMEOUT: 504,\n};\n\nexport const HTTP_CODE_TO_JSONRPC2: InvertKeyValue<\n  typeof JSONRPC2_TO_HTTP_CODE\n> = {\n  400: 'BAD_REQUEST',\n  401: 'UNAUTHORIZED',\n  402: 'PAYMENT_REQUIRED',\n  403: 'FORBIDDEN',\n  404: 'NOT_FOUND',\n  405: 'METHOD_NOT_SUPPORTED',\n  408: 'TIMEOUT',\n  409: 'CONFLICT',\n  412: 'PRECONDITION_FAILED',\n  413: 'PAYLOAD_TOO_LARGE',\n  415: 'UNSUPPORTED_MEDIA_TYPE',\n  422: 'UNPROCESSABLE_CONTENT',\n  429: 'TOO_MANY_REQUESTS',\n  499: 'CLIENT_CLOSED_REQUEST',\n  500: 'INTERNAL_SERVER_ERROR',\n  501: 'NOT_IMPLEMENTED',\n  502: 'BAD_GATEWAY',\n  503: 'SERVICE_UNAVAILABLE',\n  504: 'GATEWAY_TIMEOUT',\n} as const;\n\nexport function getStatusCodeFromKey(\n  code: keyof typeof TRPC_ERROR_CODES_BY_KEY,\n) {\n  return JSONRPC2_TO_HTTP_CODE[code] ?? 500;\n}\n\nexport function getStatusKeyFromCode(\n  code: keyof typeof HTTP_CODE_TO_JSONRPC2,\n): ValueOf<typeof HTTP_CODE_TO_JSONRPC2> {\n  return HTTP_CODE_TO_JSONRPC2[code] ?? 'INTERNAL_SERVER_ERROR';\n}\n\nexport function getHTTPStatusCode(json: TRPCResponse | TRPCResponse[]) {\n  const arr = Array.isArray(json) ? json : [json];\n  const httpStatuses = new Set<number>(\n    arr.map((res) => {\n      if ('error' in res && isObject(res.error.data)) {\n        if (typeof res.error.data?.['httpStatus'] === 'number') {\n          return res.error.data['httpStatus'];\n        }\n        const code = TRPC_ERROR_CODES_BY_NUMBER[res.error.code];\n        return getStatusCodeFromKey(code);\n      }\n      return 200;\n    }),\n  );\n\n  if (httpStatuses.size !== 1) {\n    return 207;\n  }\n\n  const httpStatus = httpStatuses.values().next().value;\n\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  return httpStatus!;\n}\n\nexport function getHTTPStatusCodeFromError(error: TRPCError) {\n  return getStatusCodeFromKey(error.code);\n}\n", "import { getHTTPStatusCodeFromError } from '../http/getHTTPStatusCode';\nimport type { ProcedureType } from '../procedure';\nimport type { AnyRootTypes, RootConfig } from '../rootConfig';\nimport { TRPC_ERROR_CODES_BY_KEY } from '../rpc';\nimport type { DefaultErrorShape } from './formatter';\nimport type { TRPCError } from './TRPCError';\n\n/**\n * @internal\n */\nexport function getErrorShape<TRoot extends AnyRootTypes>(opts: {\n  config: RootConfig<TRoot>;\n  error: TRPCError;\n  type: ProcedureType | 'unknown';\n  path: string | undefined;\n  input: unknown;\n  ctx: TRoot['ctx'] | undefined;\n}): TRoot['errorShape'] {\n  const { path, error, config } = opts;\n  const { code } = opts.error;\n  const shape: DefaultErrorShape = {\n    message: error.message,\n    code: TRPC_ERROR_CODES_BY_KEY[code],\n    data: {\n      code,\n      httpStatus: getHTTPStatusCodeFromError(error),\n    },\n  };\n  if (config.isDev && typeof opts.error.stack === 'string') {\n    shape.data.stack = opts.error.stack;\n  }\n  if (typeof path === 'string') {\n    shape.data.path = path;\n  }\n  return config.errorFormatter({ ...opts, shape });\n}\n", "import type { ProcedureType } from '../procedure';\nimport type {\n  TRPC_ERROR_CODE_KEY,\n  TRPC_ERROR_CODE_NUMBER,\n  TRPCErrorShape,\n} from '../rpc';\nimport type { TRPCError } from './TRPCError';\n\n/**\n * @internal\n */\nexport type ErrorFormatter<TContext, T<PERSON><PERSON><PERSON> extends TRPCErrorShape> = (opts: {\n  error: TRPCError;\n  type: ProcedureType | 'unknown';\n  path: string | undefined;\n  input: unknown;\n  ctx: TContext | undefined;\n  shape: DefaultErrorShape;\n}) => TShape;\n\n/**\n * @internal\n */\nexport type DefaultErrorData = {\n  code: TRPC_ERROR_CODE_KEY;\n  httpStatus: number;\n  /**\n   * Path to the procedure that threw the error\n   */\n  path?: string;\n  /**\n   * Stack trace of the error (only in development)\n   */\n  stack?: string;\n};\n\n/**\n * @internal\n */\nexport interface DefaultErrorShape extends TRPCErrorShape<DefaultErrorData> {\n  message: string;\n  code: TRPC_ERROR_CODE_NUMBER;\n}\n\nexport const defaultFormatter: ErrorFormatter<any, any> = ({ shape }) => {\n  return shape;\n};\n", "import type { TRPC_ERROR_CODE_KEY } from '../rpc/codes';\nimport { isObject } from '../utils';\n\nclass UnknownCauseError extends Error {\n  [key: string]: unknown;\n}\nexport function getCauseFromUnknown(cause: unknown): Error | undefined {\n  if (cause instanceof Error) {\n    return cause;\n  }\n\n  const type = typeof cause;\n  if (type === 'undefined' || type === 'function' || cause === null) {\n    return undefined;\n  }\n\n  // Primitive types just get wrapped in an error\n  if (type !== 'object') {\n    // eslint-disable-next-line @typescript-eslint/no-base-to-string\n    return new Error(String(cause));\n  }\n\n  // If it's an object, we'll create a synthetic error\n  if (isObject(cause)) {\n    return Object.assign(new UnknownCauseError(), cause);\n  }\n\n  return undefined;\n}\n\nexport function getTRPCErrorFromUnknown(cause: unknown): TRPCError {\n  if (cause instanceof TRPCError) {\n    return cause;\n  }\n  if (cause instanceof Error && cause.name === 'TRPCError') {\n    // https://github.com/trpc/trpc/pull/4848\n    return cause as TRPCError;\n  }\n\n  const trpcError = new TRPCError({\n    code: 'INTERNAL_SERVER_ERROR',\n    cause,\n  });\n\n  // Inherit stack from error\n  if (cause instanceof Error && cause.stack) {\n    trpcError.stack = cause.stack;\n  }\n\n  return trpcError;\n}\n\nexport class TRPCError extends Error {\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore override doesn't work in all environments due to \"This member cannot have an 'override' modifier because it is not declared in the base class 'Error'\"\n  public override readonly cause?: Error;\n  public readonly code;\n\n  constructor(opts: {\n    message?: string;\n    code: TRPC_ERROR_CODE_KEY;\n    cause?: unknown;\n  }) {\n    const cause = getCauseFromUnknown(opts.cause);\n    const message = opts.message ?? cause?.message ?? opts.code;\n\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore https://github.com/tc39/proposal-error-cause\n    super(message, { cause });\n\n    this.code = opts.code;\n    this.name = 'TRPCError';\n    this.cause ??= cause;\n  }\n}\n", "import type { AnyRootTypes, RootConfig } from './rootConfig';\nimport type { AnyRouter, inferRouterError } from './router';\nimport type {\n  TRPCResponse,\n  TRPCResponseMessage,\n  TRPCResultMessage,\n} from './rpc';\nimport { isObject } from './utils';\n\n/**\n * @public\n */\nexport interface DataTransformer {\n  serialize: (object: any) => any;\n  deserialize: (object: any) => any;\n}\n\ninterface InputDataTransformer extends DataTransformer {\n  /**\n   * This function runs **on the client** before sending the data to the server.\n   */\n  serialize: (object: any) => any;\n  /**\n   * This function runs **on the server** to transform the data before it is passed to the resolver\n   */\n  deserialize: (object: any) => any;\n}\n\ninterface OutputDataTransformer extends DataTransformer {\n  /**\n   * This function runs **on the server** before sending the data to the client.\n   */\n  serialize: (object: any) => any;\n  /**\n   * This function runs **only on the client** to transform the data sent from the server.\n   */\n  deserialize: (object: any) => any;\n}\n\n/**\n * @public\n */\nexport interface CombinedDataTransformer {\n  /**\n   * Specify how the data sent from the client to the server should be transformed.\n   */\n  input: InputDataTransformer;\n  /**\n   * Specify how the data sent from the server to the client should be transformed.\n   */\n  output: OutputDataTransformer;\n}\n\n/**\n * @public\n */\nexport type CombinedDataTransformerClient = {\n  input: Pick<CombinedDataTransformer['input'], 'serialize'>;\n  output: Pick<CombinedDataTransformer['output'], 'deserialize'>;\n};\n\n/**\n * @public\n */\nexport type DataTransformerOptions = CombinedDataTransformer | DataTransformer;\n\n/**\n * @internal\n */\nexport function getDataTransformer(\n  transformer: DataTransformerOptions,\n): CombinedDataTransformer {\n  if ('input' in transformer) {\n    return transformer;\n  }\n  return { input: transformer, output: transformer };\n}\n\n/**\n * @internal\n */\nexport const defaultTransformer: CombinedDataTransformer = {\n  input: { serialize: (obj) => obj, deserialize: (obj) => obj },\n  output: { serialize: (obj) => obj, deserialize: (obj) => obj },\n};\n\nfunction transformTRPCResponseItem<\n  TResponseItem extends TRPCResponse | TRPCResponseMessage,\n>(config: RootConfig<AnyRootTypes>, item: TResponseItem): TResponseItem {\n  if ('error' in item) {\n    return {\n      ...item,\n      error: config.transformer.output.serialize(item.error),\n    };\n  }\n\n  if ('data' in item.result) {\n    return {\n      ...item,\n      result: {\n        ...item.result,\n        data: config.transformer.output.serialize(item.result.data),\n      },\n    };\n  }\n\n  return item;\n}\n\n/**\n * Takes a unserialized `TRPCResponse` and serializes it with the router's transformers\n **/\nexport function transformTRPCResponse<\n  TResponse extends\n    | TRPCResponse\n    | TRPCResponse[]\n    | TRPCResponseMessage\n    | TRPCResponseMessage[],\n>(config: RootConfig<AnyRootTypes>, itemOrItems: TResponse) {\n  return Array.isArray(itemOrItems)\n    ? itemOrItems.map((item) => transformTRPCResponseItem(config, item))\n    : transformTRPCResponseItem(config, itemOrItems);\n}\n\n// FIXME:\n// - the generics here are probably unnecessary\n// - the RPC-spec could probably be simplified to combine HTTP + WS\n/** @internal */\nfunction transformResultInner<TRouter extends AnyRouter, TOutput>(\n  response:\n    | TRPCResponse<TOutput, inferRouterError<TRouter>>\n    | TRPCResponseMessage<TOutput, inferRouterError<TRouter>>,\n  transformer: DataTransformer,\n) {\n  if ('error' in response) {\n    const error = transformer.deserialize(\n      response.error,\n    ) as inferRouterError<TRouter>;\n    return {\n      ok: false,\n      error: {\n        ...response,\n        error,\n      },\n    } as const;\n  }\n\n  const result = {\n    ...response.result,\n    ...((!response.result.type || response.result.type === 'data') && {\n      type: 'data',\n      data: transformer.deserialize(response.result.data),\n    }),\n  } as TRPCResultMessage<TOutput>['result'];\n  return { ok: true, result } as const;\n}\n\nclass TransformResultError extends Error {\n  constructor() {\n    super('Unable to transform response from server');\n  }\n}\n\n/**\n * Transforms and validates that the result is a valid TRPCResponse\n * @internal\n */\nexport function transformResult<TRouter extends AnyRouter, TOutput>(\n  response:\n    | TRPCResponse<TOutput, inferRouterError<TRouter>>\n    | TRPCResponseMessage<TOutput, inferRouterError<TRouter>>,\n  transformer: DataTransformer,\n): ReturnType<typeof transformResultInner> {\n  let result: ReturnType<typeof transformResultInner>;\n  try {\n    // Use the data transformers on the JSON-response\n    result = transformResultInner(response, transformer);\n  } catch {\n    throw new TransformResultError();\n  }\n\n  // check that output of the transformers is a valid TRPCResponse\n  if (\n    !result.ok &&\n    (!isObject(result.error.error) ||\n      typeof result.error.error['code'] !== 'number')\n  ) {\n    throw new TransformResultError();\n  }\n  if (result.ok && !isObject(result.result)) {\n    throw new TransformResultError();\n  }\n  return result;\n}\n", "import type { Observable } from '../observable';\nimport { createRecursiveProxy } from './createProxy';\nimport { defaultFormatter } from './error/formatter';\nimport { getTRPCErrorFromUnknown, TRPCError } from './error/TRPCError';\nimport type {\n  AnyProcedure,\n  ErrorHandlerOptions,\n  inferProcedureInput,\n  inferProcedureOutput,\n  LegacyObservableSubscriptionProcedure,\n} from './procedure';\nimport type { ProcedureCallOptions } from './procedureBuilder';\nimport type { AnyRootTypes, RootConfig } from './rootConfig';\nimport { defaultTransformer } from './transformer';\nimport type { MaybePromise, ValueOf } from './types';\nimport {\n  isFunction,\n  isObject,\n  mergeWithoutOverrides,\n  omitPrototype,\n} from './utils';\n\nexport interface RouterRecord {\n  [key: string]: AnyProcedure | RouterRecord;\n}\n\ntype DecorateProcedure<TProcedure extends AnyProcedure> = (\n  input: inferProcedureInput<TProcedure>,\n) => Promise<\n  TProcedure['_def']['type'] extends 'subscription'\n    ? TProcedure extends LegacyObservableSubscriptionProcedure<any>\n      ? Observable<inferProcedureOutput<TProcedure>, TRPCError>\n      : inferProcedureOutput<TProcedure>\n    : inferProcedureOutput<TProcedure>\n>;\n\n/**\n * @internal\n */\nexport type DecorateRouterRecord<TRecord extends RouterRecord> = {\n  [TKey in keyof TRecord]: TRecord[TKey] extends infer $Value\n    ? $Value extends AnyProcedure\n      ? DecorateProcedure<$Value>\n      : $Value extends RouterRecord\n        ? DecorateRouterRecord<$Value>\n        : never\n    : never;\n};\n\n/**\n * @internal\n */\n\nexport type RouterCallerErrorHandler<TContext> = (\n  opts: ErrorHandlerOptions<TContext>,\n) => void;\n\n/**\n * @internal\n */\nexport type RouterCaller<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> = (\n  /**\n   * @note\n   * If passing a function, we recommend it's a cached function\n   * e.g. wrapped in `React.cache` to avoid unnecessary computations\n   */\n  ctx: TRoot['ctx'] | (() => MaybePromise<TRoot['ctx']>),\n  options?: {\n    onError?: RouterCallerErrorHandler<TRoot['ctx']>;\n    signal?: AbortSignal;\n  },\n) => DecorateRouterRecord<TRecord>;\n\nconst lazySymbol = Symbol('lazy');\nexport type Lazy<TAny> = (() => Promise<TAny>) & { [lazySymbol]: true };\n\ntype LazyLoader<TAny> = {\n  load: () => Promise<void>;\n  ref: Lazy<TAny>;\n};\n\nfunction once<T>(fn: () => T): () => T {\n  const uncalled = Symbol();\n  let result: T | typeof uncalled = uncalled;\n  return (): T => {\n    if (result === uncalled) {\n      result = fn();\n    }\n    return result;\n  };\n}\n\n/**\n * Lazy load a router\n * @see https://trpc.io/docs/server/merging-routers#lazy-load\n */\nexport function lazy<TRouter extends AnyRouter>(\n  importRouter: () => Promise<\n    | TRouter\n    | {\n        [key: string]: TRouter;\n      }\n  >,\n): Lazy<NoInfer<TRouter>> {\n  async function resolve(): Promise<TRouter> {\n    const mod = await importRouter();\n\n    // if the module is a router, return it\n    if (isRouter(mod)) {\n      return mod;\n    }\n\n    const routers = Object.values(mod);\n\n    if (routers.length !== 1 || !isRouter(routers[0])) {\n      throw new Error(\n        \"Invalid router module - either define exactly 1 export or return the router directly.\\nExample: `lazy(() => import('./slow.js').then((m) => m.slowRouter))`\",\n      );\n    }\n\n    return routers[0];\n  }\n  resolve[lazySymbol] = true as const;\n\n  return resolve;\n}\n\nfunction isLazy<TAny>(input: unknown): input is Lazy<TAny> {\n  return typeof input === 'function' && lazySymbol in input;\n}\n\n/**\n * @internal\n */\nexport interface RouterDef<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> {\n  _config: RootConfig<TRoot>;\n  router: true;\n  procedure?: never;\n  procedures: TRecord;\n  record: TRecord;\n  lazy: Record<string, LazyLoader<AnyRouter>>;\n}\n\nexport interface Router<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> {\n  _def: RouterDef<TRoot, TRecord>;\n  /**\n   * @see https://trpc.io/docs/v11/server/server-side-calls\n   */\n  createCaller: RouterCaller<TRoot, TRecord>;\n}\n\nexport type BuiltRouter<\n  TRoot extends AnyRootTypes,\n  TRecord extends RouterRecord,\n> = Router<TRoot, TRecord> & TRecord;\n\nexport interface RouterBuilder<TRoot extends AnyRootTypes> {\n  <TIn extends CreateRouterOptions>(\n    _: TIn,\n  ): BuiltRouter<TRoot, DecorateCreateRouterOptions<TIn>>;\n}\n\nexport type AnyRouter = Router<any, any>;\n\nexport type inferRouterRootTypes<TRouter extends AnyRouter> =\n  TRouter['_def']['_config']['$types'];\n\nexport type inferRouterContext<TRouter extends AnyRouter> =\n  inferRouterRootTypes<TRouter>['ctx'];\nexport type inferRouterError<TRouter extends AnyRouter> =\n  inferRouterRootTypes<TRouter>['errorShape'];\nexport type inferRouterMeta<TRouter extends AnyRouter> =\n  inferRouterRootTypes<TRouter>['meta'];\n\nfunction isRouter(value: unknown): value is AnyRouter {\n  return (\n    isObject(value) && isObject(value['_def']) && 'router' in value['_def']\n  );\n}\n\nconst emptyRouter = {\n  _ctx: null as any,\n  _errorShape: null as any,\n  _meta: null as any,\n  queries: {},\n  mutations: {},\n  subscriptions: {},\n  errorFormatter: defaultFormatter,\n  transformer: defaultTransformer,\n};\n\n/**\n * Reserved words that can't be used as router or procedure names\n */\nconst reservedWords = [\n  /**\n   * Then is a reserved word because otherwise we can't return a promise that returns a Proxy\n   * since JS will think that `.then` is something that exists\n   */\n  'then',\n  /**\n   * `fn.call()` and `fn.apply()` are reserved words because otherwise we can't call a function using `.call` or `.apply`\n   */\n  'call',\n  'apply',\n];\n\n/** @internal */\nexport type CreateRouterOptions = {\n  [key: string]:\n    | AnyProcedure\n    | AnyRouter\n    | CreateRouterOptions\n    | Lazy<AnyRouter>;\n};\n\n/** @internal */\nexport type DecorateCreateRouterOptions<\n  TRouterOptions extends CreateRouterOptions,\n> = {\n  [K in keyof TRouterOptions]: TRouterOptions[K] extends infer $Value\n    ? $Value extends AnyProcedure\n      ? $Value\n      : $Value extends Router<any, infer TRecord>\n        ? TRecord\n        : $Value extends Lazy<Router<any, infer TRecord>>\n          ? TRecord\n          : $Value extends CreateRouterOptions\n            ? DecorateCreateRouterOptions<$Value>\n            : never\n    : never;\n};\n\n/**\n * @internal\n */\nexport function createRouterFactory<TRoot extends AnyRootTypes>(\n  config: RootConfig<TRoot>,\n) {\n  function createRouterInner<TInput extends CreateRouterOptions>(\n    input: TInput,\n  ): BuiltRouter<TRoot, DecorateCreateRouterOptions<TInput>> {\n    const reservedWordsUsed = new Set(\n      Object.keys(input).filter((v) => reservedWords.includes(v)),\n    );\n    if (reservedWordsUsed.size > 0) {\n      throw new Error(\n        'Reserved words used in `router({})` call: ' +\n          Array.from(reservedWordsUsed).join(', '),\n      );\n    }\n\n    const procedures: Record<string, AnyProcedure> = omitPrototype({});\n    const lazy: Record<string, LazyLoader<AnyRouter>> = omitPrototype({});\n\n    function createLazyLoader(opts: {\n      ref: Lazy<AnyRouter>;\n      path: readonly string[];\n      key: string;\n      aggregate: RouterRecord;\n    }): LazyLoader<AnyRouter> {\n      return {\n        ref: opts.ref,\n        load: once(async () => {\n          const router = await opts.ref();\n          const lazyPath = [...opts.path, opts.key];\n          const lazyKey = lazyPath.join('.');\n\n          opts.aggregate[opts.key] = step(router._def.record, lazyPath);\n\n          delete lazy[lazyKey];\n\n          // add lazy loaders for nested routers\n          for (const [nestedKey, nestedItem] of Object.entries(\n            router._def.lazy,\n          )) {\n            const nestedRouterKey = [...lazyPath, nestedKey].join('.');\n\n            // console.log('adding lazy', nestedRouterKey);\n            lazy[nestedRouterKey] = createLazyLoader({\n              ref: nestedItem.ref,\n              path: lazyPath,\n              key: nestedKey,\n              aggregate: opts.aggregate[opts.key] as RouterRecord,\n            });\n          }\n        }),\n      };\n    }\n\n    function step(from: CreateRouterOptions, path: readonly string[] = []) {\n      const aggregate: RouterRecord = omitPrototype({});\n      for (const [key, item] of Object.entries(from ?? {})) {\n        if (isLazy(item)) {\n          lazy[[...path, key].join('.')] = createLazyLoader({\n            path,\n            ref: item,\n            key,\n            aggregate,\n          });\n          continue;\n        }\n        if (isRouter(item)) {\n          aggregate[key] = step(item._def.record, [...path, key]);\n          continue;\n        }\n        if (!isProcedure(item)) {\n          // RouterRecord\n          aggregate[key] = step(item, [...path, key]);\n          continue;\n        }\n\n        const newPath = [...path, key].join('.');\n\n        if (procedures[newPath]) {\n          throw new Error(`Duplicate key: ${newPath}`);\n        }\n\n        procedures[newPath] = item;\n        aggregate[key] = item;\n      }\n\n      return aggregate;\n    }\n    const record = step(input);\n\n    const _def: AnyRouter['_def'] = {\n      _config: config,\n      router: true,\n      procedures,\n      lazy,\n      ...emptyRouter,\n      record,\n    };\n\n    const router: BuiltRouter<TRoot, {}> = {\n      ...(record as {}),\n      _def,\n      createCaller: createCallerFactory<TRoot>()({\n        _def,\n      }),\n    };\n    return router as BuiltRouter<TRoot, DecorateCreateRouterOptions<TInput>>;\n  }\n\n  return createRouterInner;\n}\n\nfunction isProcedure(\n  procedureOrRouter: ValueOf<CreateRouterOptions>,\n): procedureOrRouter is AnyProcedure {\n  return typeof procedureOrRouter === 'function';\n}\n\n/**\n * @internal\n */\nexport async function getProcedureAtPath(\n  router: Pick<Router<any, any>, '_def'>,\n  path: string,\n): Promise<AnyProcedure | null> {\n  const { _def } = router;\n  let procedure = _def.procedures[path];\n\n  while (!procedure) {\n    const key = Object.keys(_def.lazy).find((key) => path.startsWith(key));\n    // console.log(`found lazy: ${key ?? 'NOPE'} (fullPath: ${fullPath})`);\n\n    if (!key) {\n      return null;\n    }\n    // console.log('loading', key, '.......');\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const lazyRouter = _def.lazy[key]!;\n    await lazyRouter.load();\n\n    procedure = _def.procedures[path];\n  }\n\n  return procedure;\n}\n\n/**\n * @internal\n */\nexport async function callProcedure(\n  opts: ProcedureCallOptions<unknown> & {\n    router: AnyRouter;\n    allowMethodOverride?: boolean;\n  },\n) {\n  const { type, path } = opts;\n  const proc = await getProcedureAtPath(opts.router, path);\n  if (\n    !proc ||\n    !isProcedure(proc) ||\n    (proc._def.type !== type && !opts.allowMethodOverride)\n  ) {\n    throw new TRPCError({\n      code: 'NOT_FOUND',\n      message: `No \"${type}\"-procedure on path \"${path}\"`,\n    });\n  }\n\n  /* istanbul ignore if -- @preserve */\n  if (\n    proc._def.type !== type &&\n    opts.allowMethodOverride &&\n    proc._def.type === 'subscription'\n  ) {\n    throw new TRPCError({\n      code: 'METHOD_NOT_SUPPORTED',\n      message: `Method override is not supported for subscriptions`,\n    });\n  }\n\n  return proc(opts);\n}\n\nexport interface RouterCallerFactory<TRoot extends AnyRootTypes> {\n  <TRecord extends RouterRecord>(\n    router: Pick<Router<TRoot, TRecord>, '_def'>,\n  ): RouterCaller<TRoot, TRecord>;\n}\n\nexport function createCallerFactory<\n  TRoot extends AnyRootTypes,\n>(): RouterCallerFactory<TRoot> {\n  return function createCallerInner<TRecord extends RouterRecord>(\n    router: Pick<Router<TRoot, TRecord>, '_def'>,\n  ): RouterCaller<TRoot, TRecord> {\n    const { _def } = router;\n    type Context = TRoot['ctx'];\n\n    return function createCaller(ctxOrCallback, opts) {\n      return createRecursiveProxy<ReturnType<RouterCaller<any, any>>>(\n        async ({ path, args }) => {\n          const fullPath = path.join('.');\n\n          if (path.length === 1 && path[0] === '_def') {\n            return _def;\n          }\n\n          const procedure = await getProcedureAtPath(router, fullPath);\n\n          let ctx: Context | undefined = undefined;\n          try {\n            if (!procedure) {\n              throw new TRPCError({\n                code: 'NOT_FOUND',\n                message: `No procedure found on path \"${path}\"`,\n              });\n            }\n            ctx = isFunction(ctxOrCallback)\n              ? await Promise.resolve(ctxOrCallback())\n              : ctxOrCallback;\n\n            return await procedure({\n              path: fullPath,\n              getRawInput: async () => args[0],\n              ctx,\n              type: procedure._def.type,\n              signal: opts?.signal,\n            });\n          } catch (cause) {\n            opts?.onError?.({\n              ctx,\n              error: getTRPCErrorFromUnknown(cause),\n              input: args[0],\n              path: fullPath,\n              type: procedure?._def.type ?? 'unknown',\n            });\n            throw cause;\n          }\n        },\n      );\n    };\n  };\n}\n\n/** @internal */\nexport type MergeRouters<\n  TRouters extends AnyRouter[],\n  TRoot extends AnyRootTypes = TRouters[0]['_def']['_config']['$types'],\n  TRecord extends RouterRecord = {},\n> = TRouters extends [\n  infer Head extends AnyRouter,\n  ...infer Tail extends AnyRouter[],\n]\n  ? MergeRouters<Tail, TRoot, Head['_def']['record'] & TRecord>\n  : BuiltRouter<TRoot, TRecord>;\n\nexport function mergeRouters<TRouters extends AnyRouter[]>(\n  ...routerList: [...TRouters]\n): MergeRouters<TRouters> {\n  const record = mergeWithoutOverrides(\n    {},\n    ...routerList.map((r) => r._def.record),\n  );\n  const errorFormatter = routerList.reduce(\n    (currentErrorFormatter, nextRouter) => {\n      if (\n        nextRouter._def._config.errorFormatter &&\n        nextRouter._def._config.errorFormatter !== defaultFormatter\n      ) {\n        if (\n          currentErrorFormatter !== defaultFormatter &&\n          currentErrorFormatter !== nextRouter._def._config.errorFormatter\n        ) {\n          throw new Error('You seem to have several error formatters');\n        }\n        return nextRouter._def._config.errorFormatter;\n      }\n      return currentErrorFormatter;\n    },\n    defaultFormatter,\n  );\n\n  const transformer = routerList.reduce((prev, current) => {\n    if (\n      current._def._config.transformer &&\n      current._def._config.transformer !== defaultTransformer\n    ) {\n      if (\n        prev !== defaultTransformer &&\n        prev !== current._def._config.transformer\n      ) {\n        throw new Error('You seem to have several transformers');\n      }\n      return current._def._config.transformer;\n    }\n    return prev;\n  }, defaultTransformer);\n\n  const router = createRouterFactory({\n    errorFormatter,\n    transformer,\n    isDev: routerList.every((r) => r._def._config.isDev),\n    allowOutsideOfServer: routerList.every(\n      (r) => r._def._config.allowOutsideOfServer,\n    ),\n    isServer: routerList.every((r) => r._def._config.isServer),\n    $types: routerList[0]?._def._config.$types,\n  })(record);\n\n  return router as MergeRouters<TRouters>;\n}\n", "const trackedSymbol = Symbol();\n\ntype TrackedId = string & {\n  __brand: 'TrackedId';\n};\nexport type TrackedEnvelope<TData> = [TrackedId, TData, typeof trackedSymbol];\n\ntype TrackedData<TData> = {\n  /**\n   * The id of the message to keep track of in case the connection gets lost\n   */\n  id: string;\n  /**\n   * The data field of the message - this can be anything\n   */\n  data: TData;\n};\n/**\n * Produce a typed server-sent event message\n * @deprecated use `tracked(id, data)` instead\n */\nexport function sse<TData>(event: { id: string; data: TData }) {\n  return tracked(event.id, event.data);\n}\n\nexport function isTrackedEnvelope<TData>(\n  value: unknown,\n): value is TrackedEnvelope<TData> {\n  return Array.isArray(value) && value[2] === trackedSymbol;\n}\n\n/**\n * Automatically track an event so that it can be resumed from a given id if the connection is lost\n */\nexport function tracked<TData>(\n  id: string,\n  data: TData,\n): TrackedEnvelope<TData> {\n  if (id === '') {\n    // This limitation could be removed by using different SSE event names / channels for tracked event and non-tracked event\n    throw new Error(\n      '`id` must not be an empty string as empty string is the same as not setting the id at all',\n    );\n  }\n  return [id as TrackedId, data, trackedSymbol];\n}\n\nexport type inferTrackedOutput<TData> =\n  TData extends TrackedEnvelope<infer $Data> ? TrackedData<$Data> : TData;\n", "import { TRPCError } from '../error/TRPCError';\nimport { isObject } from '../utils';\nimport type { TRPCRequestInfo } from './types';\n\nexport function parseConnectionParamsFromUnknown(\n  parsed: unknown,\n): TRPCRequestInfo['connectionParams'] {\n  try {\n    if (parsed === null) {\n      return null;\n    }\n    if (!isObject(parsed)) {\n      throw new Error('Expected object');\n    }\n    const nonStringValues = Object.entries(parsed).filter(\n      ([_key, value]) => typeof value !== 'string',\n    );\n\n    if (nonStringValues.length > 0) {\n      throw new Error(\n        `Expected connectionParams to be string values. Got ${nonStringValues\n          .map(([key, value]) => `${key}: ${typeof value}`)\n          .join(', ')}`,\n      );\n    }\n    return parsed as Record<string, string>;\n  } catch (cause) {\n    throw new TRPCError({\n      code: 'PARSE_ERROR',\n      message: 'Invalid connection params shape',\n      cause,\n    });\n  }\n}\nexport function parseConnectionParamsFromString(\n  str: string,\n): TRPCRequestInfo['connectionParams'] {\n  let parsed: unknown;\n  try {\n    parsed = JSON.parse(str);\n  } catch (cause) {\n    throw new TRPCError({\n      code: 'PARSE_ERROR',\n      message: 'Not JSON-parsable query params',\n      cause,\n    });\n  }\n  return parseConnectionParamsFromUnknown(parsed);\n}\n", "import { TRPCError } from '../error/TRPCError';\nimport type { ProcedureType } from '../procedure';\nimport { getProcedureAtPath, type AnyRouter } from '../router';\nimport { isObject } from '../utils';\nimport { parseConnectionParamsFromString } from './parseConnectionParams';\nimport type { TRPCAcceptHeader, TRPCRequestInfo } from './types';\n\ntype GetRequestInfoOptions = {\n  path: string;\n  req: Request;\n  url: URL | null;\n  searchParams: URLSearchParams;\n  headers: Headers;\n  router: AnyRouter;\n};\n\ntype ContentTypeHandler = {\n  isMatch: (opts: Request) => boolean;\n  parse: (opts: GetRequestInfoOptions) => Promise<TRPCRequestInfo>;\n};\n\n/**\n * Memoize a function that takes no arguments\n * @internal\n */\nfunction memo<TReturn>(fn: () => Promise<TReturn>) {\n  let promise: Promise<TReturn> | null = null;\n  const sym = Symbol.for('@trpc/server/http/memo');\n  let value: TReturn | typeof sym = sym;\n  return {\n    /**\n     * Lazily read the value\n     */\n    read: async (): Promise<TReturn> => {\n      if (value !== sym) {\n        return value;\n      }\n\n      // dedupes promises and catches errors\n      promise ??= fn().catch((cause) => {\n        if (cause instanceof TRPCError) {\n          throw cause;\n        }\n        throw new TRPCError({\n          code: 'BAD_REQUEST',\n          message: cause instanceof Error ? cause.message : 'Invalid input',\n          cause,\n        });\n      });\n\n      value = await promise;\n      promise = null;\n\n      return value;\n    },\n    /**\n     * Get an already stored result\n     */\n    result: (): TReturn | undefined => {\n      return value !== sym ? value : undefined;\n    },\n  };\n}\n\nconst jsonContentTypeHandler: ContentTypeHandler = {\n  isMatch(req) {\n    return !!req.headers.get('content-type')?.startsWith('application/json');\n  },\n  async parse(opts) {\n    const { req } = opts;\n    const isBatchCall = opts.searchParams.get('batch') === '1';\n    const paths = isBatchCall ? opts.path.split(',') : [opts.path];\n\n    type InputRecord = Record<number, unknown>;\n    const getInputs = memo(async (): Promise<InputRecord> => {\n      let inputs: unknown = undefined;\n      if (req.method === 'GET') {\n        const queryInput = opts.searchParams.get('input');\n        if (queryInput) {\n          inputs = JSON.parse(queryInput);\n        }\n      } else {\n        inputs = await req.json();\n      }\n      if (inputs === undefined) {\n        return {};\n      }\n\n      if (!isBatchCall) {\n        return {\n          0: opts.router._def._config.transformer.input.deserialize(inputs),\n        };\n      }\n\n      if (!isObject(inputs)) {\n        throw new TRPCError({\n          code: 'BAD_REQUEST',\n          message: '\"input\" needs to be an object when doing a batch call',\n        });\n      }\n      const acc: InputRecord = {};\n      for (const index of paths.keys()) {\n        const input = inputs[index];\n        if (input !== undefined) {\n          acc[index] =\n            opts.router._def._config.transformer.input.deserialize(input);\n        }\n      }\n\n      return acc;\n    });\n\n    const calls = await Promise.all(\n      paths.map(\n        async (path, index): Promise<TRPCRequestInfo['calls'][number]> => {\n          const procedure = await getProcedureAtPath(opts.router, path);\n          return {\n            path,\n            procedure,\n            getRawInput: async () => {\n              const inputs = await getInputs.read();\n              let input = inputs[index];\n\n              if (procedure?._def.type === 'subscription') {\n                const lastEventId =\n                  opts.headers.get('last-event-id') ??\n                  opts.searchParams.get('lastEventId') ??\n                  opts.searchParams.get('Last-Event-Id');\n\n                if (lastEventId) {\n                  if (isObject(input)) {\n                    input = {\n                      ...input,\n                      lastEventId: lastEventId,\n                    };\n                  } else {\n                    input ??= {\n                      lastEventId: lastEventId,\n                    };\n                  }\n                }\n              }\n              return input;\n            },\n            result: () => {\n              return getInputs.result()?.[index];\n            },\n          };\n        },\n      ),\n    );\n\n    const types = new Set(\n      calls.map((call) => call.procedure?._def.type).filter(Boolean),\n    );\n\n    /* istanbul ignore if -- @preserve */\n    if (types.size > 1) {\n      throw new TRPCError({\n        code: 'BAD_REQUEST',\n        message: `Cannot mix procedure types in call: ${Array.from(types).join(\n          ', ',\n        )}`,\n      });\n    }\n    const type: ProcedureType | 'unknown' =\n      types.values().next().value ?? 'unknown';\n\n    const connectionParamsStr = opts.searchParams.get('connectionParams');\n\n    const info: TRPCRequestInfo = {\n      isBatchCall,\n      accept: req.headers.get('trpc-accept') as TRPCAcceptHeader | null,\n      calls,\n      type,\n      connectionParams:\n        connectionParamsStr === null\n          ? null\n          : parseConnectionParamsFromString(connectionParamsStr),\n      signal: req.signal,\n      url: opts.url,\n    };\n    return info;\n  },\n};\n\nconst formDataContentTypeHandler: ContentTypeHandler = {\n  isMatch(req) {\n    return !!req.headers.get('content-type')?.startsWith('multipart/form-data');\n  },\n  async parse(opts) {\n    const { req } = opts;\n    if (req.method !== 'POST') {\n      throw new TRPCError({\n        code: 'METHOD_NOT_SUPPORTED',\n        message:\n          'Only POST requests are supported for multipart/form-data requests',\n      });\n    }\n    const getInputs = memo(async () => {\n      const fd = await req.formData();\n      return fd;\n    });\n    const procedure = await getProcedureAtPath(opts.router, opts.path);\n    return {\n      accept: null,\n      calls: [\n        {\n          path: opts.path,\n          getRawInput: getInputs.read,\n          result: getInputs.result,\n          procedure,\n        },\n      ],\n      isBatchCall: false,\n      type: 'mutation',\n      connectionParams: null,\n      signal: req.signal,\n      url: opts.url,\n    };\n  },\n};\n\nconst octetStreamContentTypeHandler: ContentTypeHandler = {\n  isMatch(req) {\n    return !!req.headers\n      .get('content-type')\n      ?.startsWith('application/octet-stream');\n  },\n  async parse(opts) {\n    const { req } = opts;\n    if (req.method !== 'POST') {\n      throw new TRPCError({\n        code: 'METHOD_NOT_SUPPORTED',\n        message:\n          'Only POST requests are supported for application/octet-stream requests',\n      });\n    }\n    const getInputs = memo(async () => {\n      return req.body;\n    });\n    return {\n      calls: [\n        {\n          path: opts.path,\n          getRawInput: getInputs.read,\n          result: getInputs.result,\n          procedure: await getProcedureAtPath(opts.router, opts.path),\n        },\n      ],\n      isBatchCall: false,\n      accept: null,\n      type: 'mutation',\n      connectionParams: null,\n      signal: req.signal,\n      url: opts.url,\n    };\n  },\n};\n\nconst handlers = [\n  jsonContentTypeHandler,\n  formDataContentTypeHandler,\n  octetStreamContentTypeHandler,\n];\n\nfunction getContentTypeHandler(req: Request): ContentTypeHandler {\n  const handler = handlers.find((handler) => handler.isMatch(req));\n  if (handler) {\n    return handler;\n  }\n\n  if (!handler && req.method === 'GET') {\n    // fallback to JSON for get requests so GET-requests can be opened in browser easily\n    return jsonContentTypeHandler;\n  }\n\n  throw new TRPCError({\n    code: 'UNSUPPORTED_MEDIA_TYPE',\n    message: req.headers.has('content-type')\n      ? `Unsupported content-type \"${req.headers.get('content-type')}`\n      : 'Missing content-type header',\n  });\n}\n\nexport async function getRequestInfo(\n  opts: GetRequestInfoOptions,\n): Promise<TRPCRequestInfo> {\n  const handler = getContentTypeHandler(opts.req);\n  return await handler.parse(opts);\n}\n", "import { isObject } from '../utils';\n\nexport function isAbortError(\n  error: unknown,\n): error is DOMException | Error | { name: 'AbortError' } {\n  return isObject(error) && error['name'] === 'AbortError';\n}\n\nexport function throwAbortError(message = 'AbortError'): never {\n  throw new DOMException(message, 'AbortError');\n}\n", "/* eslint-disable @typescript-eslint/unbound-method */\n \n \n\nimport type {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  PromiseWithResolvers,\n  ProxyPromise,\n  SubscribedPromise,\n} from \"./types\";\n\n/** Memory safe (weakmapped) cache of the ProxyPromise for each Promise,\n * which is retained for the lifetime of the original Promise.\n */\nconst subscribableCache = new WeakMap<\n  PromiseLike<unknown>,\n  ProxyPromise<unknown>\n>();\n\n/** A NOOP function allowing a consistent interface for settled\n * SubscribedPromises (settled promises are not subscribed - they resolve\n * immediately). */\nconst NOOP = () => {\n  // noop\n};\n\n/**\n * Every `Promise<T>` can be shadowed by a single `ProxyPromise<T>`. It is\n * created once, cached and reused throughout the lifetime of the Promise. Get a\n * Promise's ProxyPromise using `Unpromise.proxy(promise)`.\n *\n * The `ProxyPromise<T>` attaches handlers to the original `Promise<T>`\n * `.then()` and `.catch()` just once. Promises derived from it use a\n * subscription- (and unsubscription-) based mechanism that monitors these\n * handlers.\n *\n * Every time you call `.subscribe()`, `.then()` `.catch()` or `.finally()` on a\n * `ProxyPromise<T>` it returns a `SubscribedPromise<T>` having an additional\n * `unsubscribe()` method. Calling `unsubscribe()` detaches reference chains\n * from the original, potentially long-lived Promise, eliminating memory leaks.\n *\n * This approach can eliminate the memory leaks that otherwise come about from\n * repeated `race()` or `any()` calls invoking `.then()` and `.catch()` multiple\n * times on the same long-lived native Promise (subscriptions which can never be\n * cleaned up).\n *\n * `Unpromise.race(promises)` is a reference implementation of `Promise.race`\n * avoiding memory leaks when using long-lived unsettled Promises.\n *\n * `Unpromise.any(promises)` is a reference implementation of `Promise.any`\n * avoiding memory leaks when using long-lived unsettled Promises.\n *\n * `Unpromise.resolve(promise)` returns an ephemeral `SubscribedPromise<T>` for\n * any given `Promise<T>` facilitating arbitrary async/await patterns. Behind\n * the scenes, `resolve` is implemented simply as\n * `Unpromise.proxy(promise).subscribe()`. Don't forget to call `.unsubscribe()`\n * to tidy up!\n *\n */\nexport class Unpromise<T> implements ProxyPromise<T> {\n  /** INSTANCE IMPLEMENTATION */\n\n  /** The promise shadowed by this Unpromise<T>  */\n  protected readonly promise: Promise<T> | PromiseLike<T>;\n\n  /** Promises expecting eventual settlement (unless unsubscribed first). This list is deleted\n   * after the original promise settles - no further notifications will be issued. */\n  protected subscribers: ReadonlyArray<PromiseWithResolvers<T>> | null = [];\n\n  /** The Promise's settlement (recorded when it fulfils or rejects). This is consulted when\n   * calling .subscribe() .then() .catch() .finally() to see if an immediately-resolving Promise\n   * can be returned, and therefore subscription can be bypassed. */\n  protected settlement: PromiseSettledResult<T> | null = null;\n\n  /** Constructor accepts a normal Promise executor function like `new\n   * Unpromise((resolve, reject) => {...})` or accepts a pre-existing Promise\n   * like `new Unpromise(existingPromise)`. Adds `.then()` and `.catch()`\n   * handlers to the Promise. These handlers pass fulfilment and rejection\n   * notifications to downstream subscribers and maintains records of value\n   * or error if the Promise ever settles. */\n  protected constructor(promise: Promise<T>);\n  protected constructor(promise: PromiseLike<T>);\n  protected constructor(executor: PromiseExecutor<T>);\n  protected constructor(arg: Promise<T> | PromiseLike<T> | PromiseExecutor<T>) {\n    // handle either a Promise or a Promise executor function\n    if (typeof arg === \"function\") {\n      this.promise = new Promise(arg);\n    } else {\n      this.promise = arg;\n    }\n\n    // subscribe for eventual fulfilment and rejection\n\n    // handle PromiseLike objects (that at least have .then)\n    const thenReturn = this.promise.then((value) => {\n      // atomically record fulfilment and detach subscriber list\n      const { subscribers } = this;\n      this.subscribers = null;\n      this.settlement = {\n        status: \"fulfilled\",\n        value,\n      };\n      // notify fulfilment to subscriber list\n      subscribers?.forEach(({ resolve }) => {\n        resolve(value);\n      });\n    });\n\n    // handle Promise (that also have a .catch behaviour)\n    if (\"catch\" in thenReturn) {\n      thenReturn.catch((reason) => {\n        // atomically record rejection and detach subscriber list\n        const { subscribers } = this;\n        this.subscribers = null;\n        this.settlement = {\n          status: \"rejected\",\n          reason,\n        };\n        // notify rejection to subscriber list\n        subscribers?.forEach(({ reject }) => {\n          reject(reason);\n        });\n      });\n    }\n  }\n\n  /** Create a promise that mitigates uncontrolled subscription to a long-lived\n   * Promise via .then() and .catch() - otherwise a source of memory leaks.\n   *\n   * The returned promise has an `unsubscribe()` method which can be called when\n   * the Promise is no longer being tracked by application logic, and which\n   * ensures that there is no reference chain from the original promise to the\n   * new one, and therefore no memory leak.\n   *\n   * If original promise has not yet settled, this adds a new unique promise\n   * that listens to then/catch events, along with an `unsubscribe()` method to\n   * detach it.\n   *\n   * If original promise has settled, then creates a new Promise.resolve() or\n   * Promise.reject() and provided unsubscribe is a noop.\n   *\n   * If you call `unsubscribe()` before the returned Promise has settled, it\n   * will never settle.\n   */\n  subscribe(): SubscribedPromise<T> {\n    // in all cases we will combine some promise with its unsubscribe function\n    let promise: Promise<T>;\n    let unsubscribe: () => void;\n\n    const { settlement } = this;\n    if (settlement === null) {\n      // not yet settled - subscribe new promise. Expect eventual settlement\n      if (this.subscribers === null) {\n        // invariant - it is not settled, so it must have subscribers\n        throw new Error(\"Unpromise settled but still has subscribers\");\n      }\n      const subscriber = withResolvers<T>();\n      this.subscribers = listWithMember(this.subscribers, subscriber);\n      promise = subscriber.promise;\n      unsubscribe = () => {\n        if (this.subscribers !== null) {\n          this.subscribers = listWithoutMember(this.subscribers, subscriber);\n        }\n      };\n    } else {\n      // settled - don't create subscribed promise. Just resolve or reject\n      const { status } = settlement;\n      if (status === \"fulfilled\") {\n        promise = Promise.resolve(settlement.value);\n      } else {\n        promise = Promise.reject(settlement.reason);\n      }\n      unsubscribe = NOOP;\n    }\n\n    // extend promise signature with the extra method\n    return Object.assign(promise, { unsubscribe });\n  }\n\n  /** STANDARD PROMISE METHODS (but returning a SubscribedPromise) */\n\n  then<TResult1 = T, TResult2 = never>(\n    onfulfilled?:\n      | ((value: T) => TResult1 | PromiseLike<TResult1>)\n      | null\n       ,\n    onrejected?:\n      | ((reason: any) => TResult2 | PromiseLike<TResult2>)\n      | null\n       \n  ): SubscribedPromise<TResult1 | TResult2> {\n    const subscribed = this.subscribe();\n    const { unsubscribe } = subscribed;\n    return Object.assign(subscribed.then(onfulfilled, onrejected), {\n      unsubscribe,\n    });\n  }\n\n  catch<TResult = never>(\n    onrejected?:\n      | ((reason: any) => TResult | PromiseLike<TResult>)\n      | null\n       \n  ): SubscribedPromise<T | TResult> {\n    const subscribed = this.subscribe();\n    const { unsubscribe } = subscribed;\n    return Object.assign(subscribed.catch(onrejected), {\n      unsubscribe,\n    });\n  }\n\n  finally(onfinally?: (() => void) | null  ): SubscribedPromise<T> {\n    const subscribed = this.subscribe();\n    const { unsubscribe } = subscribed;\n    return Object.assign(subscribed.finally(onfinally), {\n      unsubscribe,\n    });\n  }\n\n  /** TOSTRING SUPPORT */\n\n  readonly [Symbol.toStringTag] = \"Unpromise\";\n\n  /** Unpromise STATIC METHODS */\n\n  /** Create or Retrieve the proxy Unpromise (a re-used Unpromise for the VM lifetime\n   * of the provided Promise reference) */\n  static proxy<T>(promise: PromiseLike<T>): ProxyPromise<T> {\n    const cached = Unpromise.getSubscribablePromise(promise);\n    return typeof cached !== \"undefined\"\n      ? cached\n      : Unpromise.createSubscribablePromise(promise);\n  }\n\n  /** Create and store an Unpromise keyed by an original Promise. */\n  protected static createSubscribablePromise<T>(promise: PromiseLike<T>) {\n    const created = new Unpromise<T>(promise);\n    subscribableCache.set(promise, created as Unpromise<unknown>); // resolve promise to unpromise\n    subscribableCache.set(created, created as Unpromise<unknown>); // resolve the unpromise to itself\n    return created;\n  }\n\n  /** Retrieve a previously-created Unpromise keyed by an original Promise. */\n  protected static getSubscribablePromise<T>(promise: PromiseLike<T>) {\n    return subscribableCache.get(promise) as ProxyPromise<T> | undefined;\n  }\n\n  /** Promise STATIC METHODS */\n\n  /** Lookup the Unpromise for this promise, and derive a SubscribedPromise from\n   * it (that can be later unsubscribed to eliminate Memory leaks) */\n  static resolve<T>(value: T | PromiseLike<T>) {\n    const promise: PromiseLike<T> =\n      typeof value === \"object\" &&\n      value !== null &&\n      \"then\" in value &&\n      typeof value.then === \"function\"\n        ? value\n        : Promise.resolve(value);\n    return Unpromise.proxy(promise).subscribe() as SubscribedPromise<\n      Awaited<T>\n    >;\n  }\n\n  /** Perform Promise.any() via SubscribedPromises, then unsubscribe them.\n   * Equivalent to Promise.any but eliminates memory leaks from long-lived\n   * promises accumulating .then() and .catch() subscribers. */\n  static async any<T extends readonly unknown[] | []>(\n    values: T\n  ): Promise<Awaited<T[number]>>;\n  static async any<T>(\n    values: Iterable<T | PromiseLike<T>>\n  ): Promise<Awaited<T>> {\n    const valuesArray = Array.isArray(values) ? values : [...values];\n    const subscribedPromises = valuesArray.map(Unpromise.resolve);\n    try {\n      return await Promise.any(subscribedPromises);\n    } finally {\n      subscribedPromises.forEach(({ unsubscribe }) => {\n        unsubscribe();\n      });\n    }\n  }\n\n  /** Perform Promise.race via SubscribedPromises, then unsubscribe them.\n   * Equivalent to Promise.race but eliminates memory leaks from long-lived\n   * promises accumulating .then() and .catch() subscribers. */\n  static async race<T extends readonly unknown[] | []>(\n    values: T\n  ): Promise<Awaited<T[number]>>;\n  static async race<T>(\n    values: Iterable<T | PromiseLike<T>>\n  ): Promise<Awaited<T>> {\n    const valuesArray = Array.isArray(values) ? values : [...values];\n    const subscribedPromises = valuesArray.map(Unpromise.resolve);\n    try {\n      return await Promise.race(subscribedPromises);\n    } finally {\n      subscribedPromises.forEach(({ unsubscribe }) => {\n        unsubscribe();\n      });\n    }\n  }\n\n  /** Create a race of SubscribedPromises that will fulfil to a single winning\n   * Promise (in a 1-Tuple). Eliminates memory leaks from long-lived promises\n   * accumulating .then() and .catch() subscribers. Allows simple logic to\n   * consume the result, like...\n   * ```ts\n   * const [ winner ] = await Unpromise.race([ promiseA, promiseB ]);\n   * if(winner === promiseB){\n   *   const result = await promiseB;\n   *   // do the thing\n   * }\n   * ```\n   * */\n  static async raceReferences<TPromise extends Promise<unknown>>(\n    promises: readonly TPromise[]\n  ) {\n    // map each promise to an eventual 1-tuple containing itself\n    const selfPromises = promises.map(resolveSelfTuple);\n\n    // now race them. They will fulfil to a readonly [P] or reject.\n    try {\n      return await Promise.race(selfPromises);\n    } finally {\n      for (const promise of selfPromises) {\n        // unsubscribe proxy promises when the race is over to mitigate memory leaks\n        promise.unsubscribe();\n      }\n    }\n  }\n}\n\n/** Promises a 1-tuple containing the original promise when it resolves. Allows\n * awaiting the eventual Promise ***reference*** (easy to destructure and\n * exactly compare with ===). Avoids resolving to the Promise ***value*** (which\n * may be ambiguous and therefore hard to identify as the winner of a race).\n * You can call unsubscribe on the Promise to mitigate memory leaks.\n * */\nexport function resolveSelfTuple<TPromise extends Promise<unknown>>(\n  promise: TPromise\n): SubscribedPromise<readonly [TPromise]> {\n  return Unpromise.proxy(promise).then(() => [promise] as const);\n}\n\n/** VENDORED (Future) PROMISE UTILITIES */\n\n/** Reference implementation of https://github.com/tc39/proposal-promise-with-resolvers */\nfunction withResolvers<T>(): PromiseWithResolvers<T> {\n  let resolve!: PromiseWithResolvers<T>[\"resolve\"];\n  let reject!: PromiseWithResolvers<T>[\"reject\"];\n  const promise = new Promise<T>((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  return {\n    promise,\n    resolve,\n    reject,\n  };\n}\n\n/** IMMUTABLE LIST OPERATIONS */\n\nfunction listWithMember<T>(arr: readonly T[], member: T): readonly T[] {\n  return [...arr, member];\n}\n\nfunction listWithoutIndex<T>(arr: readonly T[], index: number) {\n  return [...arr.slice(0, index), ...arr.slice(index + 1)];\n}\n\nfunction listWithoutMember<T>(arr: readonly T[], member: unknown) {\n  const index = arr.indexOf(member as T);\n  if (index !== -1) {\n    return listWithoutIndex(arr, index);\n  }\n  return arr;\n}\n", "// @ts-expect-error - polyfilling symbol\n// eslint-disable-next-line no-restricted-syntax\nSymbol.dispose ??= Symbol();\n\n// @ts-expect-error - polyfilling symbol\n// eslint-disable-next-line no-restricted-syntax\nSymbol.asyncDispose ??= Symbol();\n\n/**\n * Takes a value and a dispose function and returns a new object that implements the Disposable interface.\n * The returned object is the original value augmented with a Symbol.dispose method.\n * @param thing The value to make disposable\n * @param dispose Function to call when disposing the resource\n * @returns The original value with Symbol.dispose method added\n */\nexport function makeResource<T>(thing: T, dispose: () => void): T & Disposable {\n  const it = thing as T & Partial<Disposable>;\n\n  // eslint-disable-next-line no-restricted-syntax\n  const existing = it[Symbol.dispose];\n\n  // eslint-disable-next-line no-restricted-syntax\n  it[Symbol.dispose] = () => {\n    dispose();\n    existing?.();\n  };\n\n  return it as T & Disposable;\n}\n\n/**\n * Takes a value and an async dispose function and returns a new object that implements the AsyncDisposable interface.\n * The returned object is the original value augmented with a Symbol.asyncDispose method.\n * @param thing The value to make async disposable\n * @param dispose Async function to call when disposing the resource\n * @returns The original value with Symbol.asyncDispose method added\n */\nexport function makeAsyncResource<T>(\n  thing: T,\n  dispose: () => Promise<void>,\n): T & AsyncDisposable {\n  const it = thing as T & Partial<AsyncDisposable>;\n\n  // eslint-disable-next-line no-restricted-syntax\n  const existing = it[Symbol.asyncDispose];\n\n  // eslint-disable-next-line no-restricted-syntax\n  it[Symbol.asyncDispose] = async () => {\n    await dispose();\n    await existing?.();\n  };\n\n  return it as T & AsyncDisposable;\n}\n", "import { makeResource } from './disposable';\n\nexport const disposablePromiseTimerResult = Symbol();\n\nexport function timerResource(ms: number) {\n  let timer: ReturnType<typeof setTimeout> | null = null;\n\n  return makeResource(\n    {\n      start() {\n        if (timer) {\n          throw new Error('Timer already started');\n        }\n\n        const promise = new Promise<typeof disposablePromiseTimerResult>(\n          (resolve) => {\n            timer = setTimeout(() => resolve(disposablePromiseTimerResult), ms);\n          },\n        );\n        return promise;\n      },\n    },\n    () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    },\n  );\n}\n", "function _usingCtx() {\n  var r = \"function\" == typeof SuppressedError ? SuppressedError : function (r, e) {\n      var n = Error();\n      return n.name = \"SuppressedError\", n.error = r, n.suppressed = e, n;\n    },\n    e = {},\n    n = [];\n  function using(r, e) {\n    if (null != e) {\n      if (Object(e) !== e) throw new TypeError(\"using declarations can only be used with objects, functions, null, or undefined.\");\n      if (r) var o = e[Symbol.asyncDispose || Symbol[\"for\"](\"Symbol.asyncDispose\")];\n      if (void 0 === o && (o = e[Symbol.dispose || Symbol[\"for\"](\"Symbol.dispose\")], r)) var t = o;\n      if (\"function\" != typeof o) throw new TypeError(\"Object is not disposable.\");\n      t && (o = function o() {\n        try {\n          t.call(e);\n        } catch (r) {\n          return Promise.reject(r);\n        }\n      }), n.push({\n        v: e,\n        d: o,\n        a: r\n      });\n    } else r && n.push({\n      d: e,\n      a: r\n    });\n    return e;\n  }\n  return {\n    e: e,\n    u: using.bind(null, !1),\n    a: using.bind(null, !0),\n    d: function d() {\n      var o,\n        t = this.e,\n        s = 0;\n      function next() {\n        for (; o = n.pop();) try {\n          if (!o.a && 1 === s) return s = 0, n.push(o), Promise.resolve().then(next);\n          if (o.d) {\n            var r = o.d.call(o.v);\n            if (o.a) return s |= 2, Promise.resolve(r).then(next, err);\n          } else s |= 1;\n        } catch (r) {\n          return err(r);\n        }\n        if (1 === s) return t !== e ? Promise.reject(t) : Promise.resolve();\n        if (t !== e) throw t;\n      }\n      function err(n) {\n        return t = t !== e ? new r(n, t) : n, next();\n      }\n      return next();\n    }\n  };\n}\nmodule.exports = _usingCtx, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import { Unpromise } from '../../../vendor/unpromise';\nimport { throwAbortError } from '../../http/abortError';\nimport { makeAsyncResource } from './disposable';\nimport { disposablePromiseTimerResult, timerResource } from './timerResource';\n\nexport function iteratorResource<TYield, TReturn, TNext>(\n  iterable: AsyncIterable<TYield, TReturn, TNext>,\n): AsyncIterator<TYield, TReturn, TNext> & AsyncDisposable {\n  const iterator = iterable[Symbol.asyncIterator]();\n\n  // @ts-expect-error - this is added in node 24 which we don't officially support yet\n  // eslint-disable-next-line no-restricted-syntax\n  if (iterator[Symbol.asyncDispose]) {\n    return iterator as AsyncIterator<TYield, TReturn, TNext> & AsyncDisposable;\n  }\n\n  return makeAsyncResource(iterator, async () => {\n    await iterator.return?.();\n  });\n}\n/**\n * Derives a new {@link AsyncGenerator} based on {@link iterable}, that automatically aborts after the specified duration.\n */\nexport async function* withMaxDuration<T>(\n  iterable: AsyncIterable<T>,\n  opts: { maxDurationMs: number },\n): AsyncGenerator<T> {\n  await using iterator = iteratorResource(iterable);\n\n  using timer = timerResource(opts.maxDurationMs);\n\n  const timerPromise = timer.start();\n\n  // declaration outside the loop for garbage collection reasons\n  let result: null | IteratorResult<T> | typeof disposablePromiseTimerResult;\n\n  while (true) {\n    result = await Unpromise.race([iterator.next(), timerPromise]);\n    if (result === disposablePromiseTimerResult) {\n      // cancelled due to timeout\n      throwAbortError();\n    }\n    if (result.done) {\n      return result;\n    }\n    yield result.value;\n    // free up reference for garbage collection\n    result = null;\n  }\n}\n\n/**\n * Derives a new {@link AsyncGenerator} based of {@link iterable}, that yields its first\n * {@link count} values. Then, a grace period of {@link gracePeriodMs} is started in which further\n * values may still come through. After this period, the generator aborts.\n */\nexport async function* takeWithGrace<T>(\n  iterable: AsyncIterable<T>,\n  opts: {\n    count: number;\n    gracePeriodMs: number;\n  },\n): AsyncGenerator<T> {\n  await using iterator = iteratorResource(iterable);\n\n  // declaration outside the loop for garbage collection reasons\n  let result: null | IteratorResult<T> | typeof disposablePromiseTimerResult;\n\n  using timer = timerResource(opts.gracePeriodMs);\n\n  let count = opts.count;\n\n  let timerPromise = new Promise<typeof disposablePromiseTimerResult>(() => {\n    // never resolves\n  });\n\n  while (true) {\n    result = await Unpromise.race([iterator.next(), timerPromise]);\n    if (result === disposablePromiseTimerResult) {\n      throwAbortError();\n    }\n    if (result.done) {\n      return result.value;\n    }\n    yield result.value;\n    if (--count === 0) {\n      timerPromise = timer.start();\n    }\n    // free up reference for garbage collection\n    result = null;\n  }\n}\n", "/* eslint-disable @typescript-eslint/no-non-null-assertion */\nexport function createDeferred<TValue = void>() {\n  let resolve: (value: TValue) => void;\n  let reject: (error: unknown) => void;\n  const promise = new Promise<TValue>((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n\n  return { promise, resolve: resolve!, reject: reject! };\n}\nexport type Deferred<TValue> = ReturnType<typeof createDeferred<TValue>>;\n", "import { createDeferred } from './createDeferred';\nimport { makeAsyncResource } from './disposable';\n\ntype ManagedIteratorResult<TYield, TReturn> =\n  | { status: 'yield'; value: TYield }\n  | { status: 'return'; value: TReturn }\n  | { status: 'error'; error: unknown };\nfunction createManagedIterator<TYield, TReturn>(\n  iterable: AsyncIterable<TYield, TReturn>,\n  onResult: (result: ManagedIteratorResult<TYield, TReturn>) => void,\n) {\n  const iterator = iterable[Symbol.asyncIterator]();\n  let state: 'idle' | 'pending' | 'done' = 'idle';\n\n  function cleanup() {\n    state = 'done';\n    onResult = () => {\n      // noop\n    };\n  }\n\n  function pull() {\n    if (state !== 'idle') {\n      return;\n    }\n    state = 'pending';\n\n    const next = iterator.next();\n    next\n      .then((result) => {\n        if (result.done) {\n          state = 'done';\n          onResult({ status: 'return', value: result.value });\n          cleanup();\n          return;\n        }\n        state = 'idle';\n        onResult({ status: 'yield', value: result.value });\n      })\n      .catch((cause) => {\n        onResult({ status: 'error', error: cause });\n        cleanup();\n      });\n  }\n\n  return {\n    pull,\n    destroy: async () => {\n      cleanup();\n      await iterator.return?.();\n    },\n  };\n}\ntype ManagedIterator<TYield, TReturn> = ReturnType<\n  typeof createManagedIterator<TYield, TReturn>\n>;\n\ninterface MergedAsyncIterables<TYield>\n  extends AsyncIterable<TYield, void, unknown> {\n  add(iterable: AsyncIterable<TYield>): void;\n}\n\n/**\n * Creates a new async iterable that merges multiple async iterables into a single stream.\n * Values from the input iterables are yielded in the order they resolve, similar to Promise.race().\n *\n * New iterables can be added dynamically using the returned {@link MergedAsyncIterables.add} method, even after iteration has started.\n *\n * If any of the input iterables throws an error, that error will be propagated through the merged stream.\n * Other iterables will not continue to be processed.\n *\n * @template TYield The type of values yielded by the input iterables\n */\nexport function mergeAsyncIterables<TYield>(): MergedAsyncIterables<TYield> {\n  let state: 'idle' | 'pending' | 'done' = 'idle';\n  let flushSignal = createDeferred();\n\n  /**\n   * used while {@link state} is `idle`\n   */\n  const iterables: AsyncIterable<TYield, void, unknown>[] = [];\n  /**\n   * used while {@link state} is `pending`\n   */\n  const iterators = new Set<ManagedIterator<TYield, void>>();\n\n  const buffer: Array<\n    [\n      iterator: ManagedIterator<TYield, void>,\n      result: Exclude<\n        ManagedIteratorResult<TYield, void>,\n        { status: 'return' }\n      >,\n    ]\n  > = [];\n\n  function initIterable(iterable: AsyncIterable<TYield, void, unknown>) {\n    if (state !== 'pending') {\n      // shouldn't happen\n      return;\n    }\n    const iterator = createManagedIterator(iterable, (result) => {\n      if (state !== 'pending') {\n        // shouldn't happen\n        return;\n      }\n      switch (result.status) {\n        case 'yield':\n          buffer.push([iterator, result]);\n          break;\n        case 'return':\n          iterators.delete(iterator);\n          break;\n        case 'error':\n          buffer.push([iterator, result]);\n          iterators.delete(iterator);\n          break;\n      }\n      flushSignal.resolve();\n    });\n    iterators.add(iterator);\n    iterator.pull();\n  }\n\n  return {\n    add(iterable: AsyncIterable<TYield, void, unknown>) {\n      switch (state) {\n        case 'idle':\n          iterables.push(iterable);\n          break;\n        case 'pending':\n          initIterable(iterable);\n          break;\n        case 'done': {\n          // shouldn't happen\n          break;\n        }\n      }\n    },\n    async *[Symbol.asyncIterator]() {\n      if (state !== 'idle') {\n        throw new Error('Cannot iterate twice');\n      }\n      state = 'pending';\n\n      await using _finally = makeAsyncResource({}, async () => {\n        state = 'done';\n\n        const errors: unknown[] = [];\n        await Promise.all(\n          Array.from(iterators.values()).map(async (it) => {\n            try {\n              await it.destroy();\n            } catch (cause) {\n              errors.push(cause);\n            }\n          }),\n        );\n        buffer.length = 0;\n        iterators.clear();\n        flushSignal.resolve();\n\n        if (errors.length > 0) {\n          throw new AggregateError(errors);\n        }\n      });\n\n      while (iterables.length > 0) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        initIterable(iterables.shift()!);\n      }\n\n      while (iterators.size > 0) {\n        await flushSignal.promise;\n\n        while (buffer.length > 0) {\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          const [iterator, result] = buffer.shift()!;\n\n          switch (result.status) {\n            case 'yield':\n              yield result.value;\n              iterator.pull();\n              break;\n            case 'error':\n              throw result.error;\n          }\n        }\n        flushSignal = createDeferred();\n      }\n    },\n  };\n}\n", "/**\n * Creates a ReadableStream from an AsyncIterable.\n *\n * @param iterable - The source AsyncIterable to stream from\n * @returns A ReadableStream that yields values from the AsyncIterable\n */\nexport function readableStreamFrom<TYield>(\n  iterable: AsyncIterable<TYield, void>,\n): ReadableStream<TYield> {\n  const iterator = iterable[Symbol.asyncIterator]();\n\n  return new ReadableStream({\n    async cancel() {\n      await iterator.return?.();\n    },\n\n    async pull(controller) {\n      const result = await iterator.next();\n\n      if (result.done) {\n        controller.close();\n        return;\n      }\n\n      controller.enqueue(result.value);\n    },\n  });\n}\n", "import { Unpromise } from '../../../vendor/unpromise';\nimport { iteratorResource } from './asyncIterable';\nimport { disposablePromiseTimerResult, timerResource } from './timerResource';\n\nexport const PING_SYM = Symbol('ping');\n\n/**\n * Derives a new {@link AsyncGenerator} based of {@link iterable}, that yields {@link PING_SYM}\n * whenever no value has been yielded for {@link pingIntervalMs}.\n */\nexport async function* withPing<TValue>(\n  iterable: AsyncIterable<TValue>,\n  pingIntervalMs: number,\n): AsyncGenerator<TValue | typeof PING_SYM> {\n  await using iterator = iteratorResource(iterable);\n\n  // declaration outside the loop for garbage collection reasons\n  let result:\n    | null\n    | IteratorResult<TValue>\n    | typeof disposablePromiseTimerResult;\n\n  let nextPromise = iterator.next();\n\n  while (true) {\n    using pingPromise = timerResource(pingIntervalMs);\n\n    result = await Unpromise.race([nextPromise, pingPromise.start()]);\n\n    if (result === disposablePromiseTimerResult) {\n      // cancelled\n\n      yield PING_SYM;\n      continue;\n    }\n\n    if (result.done) {\n      return result.value;\n    }\n\n    nextPromise = iterator.next();\n    yield result.value;\n\n    // free up reference for garbage collection\n    result = null;\n  }\n}\n", "import { isAsyncIterable, isFunction, isObject, run } from '../utils';\nimport { iteratorResource } from './utils/asyncIterable';\nimport type { Deferred } from './utils/createDeferred';\nimport { createDeferred } from './utils/createDeferred';\nimport { makeResource } from './utils/disposable';\nimport { mergeAsyncIterables } from './utils/mergeAsyncIterables';\nimport { readableStreamFrom } from './utils/readableStreamFrom';\nimport { PING_SYM, withPing } from './utils/withPing';\n\n/**\n * A subset of the standard ReadableStream properties needed by tRPC internally.\n * @see ReadableStream from lib.dom.d.ts\n */\nexport type WebReadableStreamEsque = {\n  getReader: () => ReadableStreamDefaultReader<Uint8Array>;\n};\n\nexport type NodeJSReadableStreamEsque = {\n  on(\n    eventName: string | symbol,\n    listener: (...args: any[]) => void,\n  ): NodeJSReadableStreamEsque;\n};\n\nfunction isPlainObject(value: unknown): value is Record<string, unknown> {\n  return Object.prototype.toString.call(value) === '[object Object]';\n}\n\n// ---------- types\nconst CHUNK_VALUE_TYPE_PROMISE = 0;\ntype CHUNK_VALUE_TYPE_PROMISE = typeof CHUNK_VALUE_TYPE_PROMISE;\nconst CHUNK_VALUE_TYPE_ASYNC_ITERABLE = 1;\ntype CHUNK_VALUE_TYPE_ASYNC_ITERABLE = typeof CHUNK_VALUE_TYPE_ASYNC_ITERABLE;\n\nconst PROMISE_STATUS_FULFILLED = 0;\ntype PROMISE_STATUS_FULFILLED = typeof PROMISE_STATUS_FULFILLED;\nconst PROMISE_STATUS_REJECTED = 1;\ntype PROMISE_STATUS_REJECTED = typeof PROMISE_STATUS_REJECTED;\n\nconst ASYNC_ITERABLE_STATUS_RETURN = 0;\ntype ASYNC_ITERABLE_STATUS_RETURN = typeof ASYNC_ITERABLE_STATUS_RETURN;\nconst ASYNC_ITERABLE_STATUS_YIELD = 1;\ntype ASYNC_ITERABLE_STATUS_YIELD = typeof ASYNC_ITERABLE_STATUS_YIELD;\nconst ASYNC_ITERABLE_STATUS_ERROR = 2;\ntype ASYNC_ITERABLE_STATUS_ERROR = typeof ASYNC_ITERABLE_STATUS_ERROR;\n\ntype ChunkDefinitionKey =\n  // root should be replaced\n  | null\n  // at array path\n  | number\n  // at key path\n  | string;\n\ntype ChunkIndex = number & { __chunkIndex: true };\ntype ChunkValueType =\n  | CHUNK_VALUE_TYPE_PROMISE\n  | CHUNK_VALUE_TYPE_ASYNC_ITERABLE;\ntype ChunkDefinition = [\n  key: ChunkDefinitionKey,\n  type: ChunkValueType,\n  chunkId: ChunkIndex,\n];\ntype EncodedValue = [\n  // data\n  [unknown] | [],\n  // chunk descriptions\n  ...ChunkDefinition[],\n];\n\ntype Head = Record<string, EncodedValue>;\ntype PromiseChunk =\n  | [\n      chunkIndex: ChunkIndex,\n      status: PROMISE_STATUS_FULFILLED,\n      value: EncodedValue,\n    ]\n  | [chunkIndex: ChunkIndex, status: PROMISE_STATUS_REJECTED, error: unknown];\ntype IterableChunk =\n  | [\n      chunkIndex: ChunkIndex,\n      status: ASYNC_ITERABLE_STATUS_RETURN,\n      value: EncodedValue,\n    ]\n  | [\n      chunkIndex: ChunkIndex,\n      status: ASYNC_ITERABLE_STATUS_YIELD,\n      value: EncodedValue,\n    ]\n  | [\n      chunkIndex: ChunkIndex,\n      status: ASYNC_ITERABLE_STATUS_ERROR,\n      error: unknown,\n    ];\ntype ChunkData = PromiseChunk | IterableChunk;\ntype PlaceholderValue = 0 & { __placeholder: true };\nexport function isPromise(value: unknown): value is Promise<unknown> {\n  return (\n    (isObject(value) || isFunction(value)) &&\n    typeof value?.['then'] === 'function' &&\n    typeof value?.['catch'] === 'function'\n  );\n}\n\ntype Serialize = (value: any) => any;\ntype Deserialize = (value: any) => any;\n\ntype PathArray = readonly (string | number)[];\nexport type ProducerOnError = (opts: {\n  error: unknown;\n  path: PathArray;\n}) => void;\nexport interface JSONLProducerOptions {\n  serialize?: Serialize;\n  data: Record<string, unknown> | unknown[];\n  onError?: ProducerOnError;\n  formatError?: (opts: { error: unknown; path: PathArray }) => unknown;\n  maxDepth?: number;\n  /**\n   * Interval in milliseconds to send a ping to the client to keep the connection alive\n   * This will be sent as a whitespace character\n   * @default undefined\n   */\n  pingMs?: number;\n}\n\nclass MaxDepthError extends Error {\n  constructor(public path: (string | number)[]) {\n    super('Max depth reached at path: ' + path.join('.'));\n  }\n}\n\nasync function* createBatchStreamProducer(\n  opts: JSONLProducerOptions,\n): AsyncIterable<Head | ChunkData | typeof PING_SYM, void> {\n  const { data } = opts;\n  let counter = 0 as ChunkIndex;\n  const placeholder = 0 as PlaceholderValue;\n\n  const mergedIterables = mergeAsyncIterables<ChunkData>();\n  function registerAsync(\n    callback: (idx: ChunkIndex) => AsyncIterable<ChunkData, void>,\n  ) {\n    const idx = counter++ as ChunkIndex;\n\n    const iterable = callback(idx);\n    mergedIterables.add(iterable);\n\n    return idx;\n  }\n\n  function encodePromise(promise: Promise<unknown>, path: (string | number)[]) {\n    return registerAsync(async function* (idx) {\n      const error = checkMaxDepth(path);\n      if (error) {\n        // Catch any errors from the original promise to ensure they're reported\n        promise.catch((cause) => {\n          opts.onError?.({ error: cause, path });\n        });\n        // Replace the promise with a rejected one containing the max depth error\n        promise = Promise.reject(error);\n      }\n      try {\n        const next = await promise;\n        yield [idx, PROMISE_STATUS_FULFILLED, encode(next, path)];\n      } catch (cause) {\n        opts.onError?.({ error: cause, path });\n        yield [\n          idx,\n          PROMISE_STATUS_REJECTED,\n          opts.formatError?.({ error: cause, path }),\n        ];\n      }\n    });\n  }\n  function encodeAsyncIterable(\n    iterable: AsyncIterable<unknown>,\n    path: (string | number)[],\n  ) {\n    return registerAsync(async function* (idx) {\n      const error = checkMaxDepth(path);\n      if (error) {\n        throw error;\n      }\n      await using iterator = iteratorResource(iterable);\n\n      try {\n        while (true) {\n          const next = await iterator.next();\n          if (next.done) {\n            yield [idx, ASYNC_ITERABLE_STATUS_RETURN, encode(next.value, path)];\n            break;\n          }\n          yield [idx, ASYNC_ITERABLE_STATUS_YIELD, encode(next.value, path)];\n        }\n      } catch (cause) {\n        opts.onError?.({ error: cause, path });\n\n        yield [\n          idx,\n          ASYNC_ITERABLE_STATUS_ERROR,\n          opts.formatError?.({ error: cause, path }),\n        ];\n      }\n    });\n  }\n  function checkMaxDepth(path: (string | number)[]) {\n    if (opts.maxDepth && path.length > opts.maxDepth) {\n      return new MaxDepthError(path);\n    }\n    return null;\n  }\n  function encodeAsync(\n    value: unknown,\n    path: (string | number)[],\n  ): null | [type: ChunkValueType, chunkId: ChunkIndex] {\n    if (isPromise(value)) {\n      return [CHUNK_VALUE_TYPE_PROMISE, encodePromise(value, path)];\n    }\n    if (isAsyncIterable(value)) {\n      if (opts.maxDepth && path.length >= opts.maxDepth) {\n        throw new Error('Max depth reached');\n      }\n      return [\n        CHUNK_VALUE_TYPE_ASYNC_ITERABLE,\n        encodeAsyncIterable(value, path),\n      ];\n    }\n    return null;\n  }\n  function encode(value: unknown, path: (string | number)[]): EncodedValue {\n    if (value === undefined) {\n      return [[]];\n    }\n    const reg = encodeAsync(value, path);\n    if (reg) {\n      return [[placeholder], [null, ...reg]];\n    }\n\n    if (!isPlainObject(value)) {\n      return [[value]];\n    }\n\n    const newObj: Record<string, unknown> = {};\n    const asyncValues: ChunkDefinition[] = [];\n    for (const [key, item] of Object.entries(value)) {\n      const transformed = encodeAsync(item, [...path, key]);\n      if (!transformed) {\n        newObj[key] = item;\n        continue;\n      }\n      newObj[key] = placeholder;\n      asyncValues.push([key, ...transformed]);\n    }\n    return [[newObj], ...asyncValues];\n  }\n\n  const newHead: Head = {};\n  for (const [key, item] of Object.entries(data)) {\n    newHead[key] = encode(item, [key]);\n  }\n\n  yield newHead;\n\n  let iterable: AsyncIterable<ChunkData | typeof PING_SYM, void> =\n    mergedIterables;\n  if (opts.pingMs) {\n    iterable = withPing(mergedIterables, opts.pingMs);\n  }\n\n  for await (const value of iterable) {\n    yield value;\n  }\n}\n/**\n * JSON Lines stream producer\n * @see https://jsonlines.org/\n */\nexport function jsonlStreamProducer(opts: JSONLProducerOptions) {\n  let stream = readableStreamFrom(createBatchStreamProducer(opts));\n\n  const { serialize } = opts;\n  if (serialize) {\n    stream = stream.pipeThrough(\n      new TransformStream({\n        transform(chunk, controller) {\n          if (chunk === PING_SYM) {\n            controller.enqueue(PING_SYM);\n          } else {\n            controller.enqueue(serialize(chunk));\n          }\n        },\n      }),\n    );\n  }\n\n  return stream\n    .pipeThrough(\n      new TransformStream({\n        transform(chunk, controller) {\n          if (chunk === PING_SYM) {\n            controller.enqueue(' ');\n          } else {\n            controller.enqueue(JSON.stringify(chunk) + '\\n');\n          }\n        },\n      }),\n    )\n    .pipeThrough(new TextEncoderStream());\n}\n\nclass AsyncError extends Error {\n  constructor(public readonly data: unknown) {\n    super('Received error from server');\n  }\n}\nexport type ConsumerOnError = (opts: { error: unknown }) => void;\n\nconst nodeJsStreamToReaderEsque = (source: NodeJSReadableStreamEsque) => {\n  return {\n    getReader() {\n      const stream = new ReadableStream<Uint8Array>({\n        start(controller) {\n          source.on('data', (chunk) => {\n            controller.enqueue(chunk);\n          });\n          source.on('end', () => {\n            controller.close();\n          });\n          source.on('error', (error) => {\n            controller.error(error);\n          });\n        },\n      });\n      return stream.getReader();\n    },\n  };\n};\n\nfunction createLineAccumulator(\n  from: NodeJSReadableStreamEsque | WebReadableStreamEsque,\n) {\n  const reader =\n    'getReader' in from\n      ? from.getReader()\n      : nodeJsStreamToReaderEsque(from).getReader();\n\n  let lineAggregate = '';\n\n  return new ReadableStream({\n    async pull(controller) {\n      const { done, value } = await reader.read();\n\n      if (done) {\n        controller.close();\n      } else {\n        controller.enqueue(value);\n      }\n    },\n    cancel() {\n      return reader.cancel();\n    },\n  })\n    .pipeThrough(new TextDecoderStream())\n    .pipeThrough(\n      new TransformStream<string, string>({\n        transform(chunk, controller) {\n          lineAggregate += chunk;\n          const parts = lineAggregate.split('\\n');\n          lineAggregate = parts.pop() ?? '';\n          for (const part of parts) {\n            controller.enqueue(part);\n          }\n        },\n      }),\n    );\n}\nfunction createConsumerStream<THead>(\n  from: NodeJSReadableStreamEsque | WebReadableStreamEsque,\n) {\n  const stream = createLineAccumulator(from);\n\n  let sentHead = false;\n  return stream.pipeThrough(\n    new TransformStream<string, ChunkData | THead>({\n      transform(line, controller) {\n        if (!sentHead) {\n          const head = JSON.parse(line);\n          controller.enqueue(head as THead);\n          sentHead = true;\n        } else {\n          const chunk: ChunkData = JSON.parse(line);\n          controller.enqueue(chunk);\n        }\n      },\n    }),\n  );\n}\n\n/**\n * Creates a handler for managing stream controllers and their lifecycle\n */\nfunction createStreamsManager(abortController: AbortController) {\n  const controllerMap = new Map<\n    ChunkIndex,\n    ReturnType<typeof createStreamController>\n  >();\n\n  /**\n   * Checks if there are no pending controllers or deferred promises\n   */\n  function isEmpty() {\n    return Array.from(controllerMap.values()).every((c) => c.closed);\n  }\n\n  /**\n   * Creates a stream controller\n   */\n  function createStreamController() {\n    let originalController: ReadableStreamDefaultController<ChunkData>;\n    const stream = new ReadableStream<ChunkData>({\n      start(controller) {\n        originalController = controller;\n      },\n    });\n\n    const streamController = {\n      enqueue: (v: ChunkData) => originalController.enqueue(v),\n      close: () => {\n        originalController.close();\n\n        clear();\n\n        if (isEmpty()) {\n          abortController.abort();\n        }\n      },\n      closed: false,\n      getReaderResource: () => {\n        const reader = stream.getReader();\n\n        return makeResource(reader, () => {\n          reader.releaseLock();\n          streamController.close();\n        });\n      },\n      error: (reason: unknown) => {\n        originalController.error(reason);\n        clear();\n      },\n    };\n    function clear() {\n      Object.assign(streamController, {\n        closed: true,\n        close: () => {\n          // noop\n        },\n        enqueue: () => {\n          // noop\n        },\n        getReaderResource: null,\n        error: () => {\n          // noop\n        },\n      });\n    }\n\n    return streamController;\n  }\n\n  /**\n   * Gets or creates a stream controller\n   */\n  function getOrCreate(chunkId: ChunkIndex) {\n    let c = controllerMap.get(chunkId);\n    if (!c) {\n      c = createStreamController();\n      controllerMap.set(chunkId, c);\n    }\n    return c;\n  }\n\n  /**\n   * Cancels all pending controllers and rejects deferred promises\n   */\n  function cancelAll(reason: unknown) {\n    for (const controller of controllerMap.values()) {\n      controller.error(reason);\n    }\n  }\n\n  return {\n    getOrCreate,\n    isEmpty,\n    cancelAll,\n  };\n}\n\n/**\n * JSON Lines stream consumer\n * @see https://jsonlines.org/\n */\nexport async function jsonlStreamConsumer<THead>(opts: {\n  from: NodeJSReadableStreamEsque | WebReadableStreamEsque;\n  deserialize?: Deserialize;\n  onError?: ConsumerOnError;\n  formatError?: (opts: { error: unknown }) => Error;\n  /**\n   * This `AbortController` will be triggered when there are no more listeners to the stream.\n   */\n  abortController: AbortController;\n}) {\n  const { deserialize = (v) => v } = opts;\n\n  let source = createConsumerStream<Head>(opts.from);\n  if (deserialize) {\n    source = source.pipeThrough(\n      new TransformStream({\n        transform(chunk, controller) {\n          controller.enqueue(deserialize(chunk));\n        },\n      }),\n    );\n  }\n  let headDeferred: null | Deferred<THead> = createDeferred();\n\n  const streamManager = createStreamsManager(opts.abortController);\n\n  function decodeChunkDefinition(value: ChunkDefinition) {\n    const [_path, type, chunkId] = value;\n\n    const controller = streamManager.getOrCreate(chunkId);\n\n    switch (type) {\n      case CHUNK_VALUE_TYPE_PROMISE: {\n        return run(async () => {\n          using reader = controller.getReaderResource();\n\n          const { value } = await reader.read();\n          const [_chunkId, status, data] = value as PromiseChunk;\n          switch (status) {\n            case PROMISE_STATUS_FULFILLED:\n              return decode(data);\n            case PROMISE_STATUS_REJECTED:\n              throw opts.formatError?.({ error: data }) ?? new AsyncError(data);\n          }\n        });\n      }\n      case CHUNK_VALUE_TYPE_ASYNC_ITERABLE: {\n        return run(async function* () {\n          using reader = controller.getReaderResource();\n\n          while (true) {\n            const { value } = await reader.read();\n\n            const [_chunkId, status, data] = value as IterableChunk;\n\n            switch (status) {\n              case ASYNC_ITERABLE_STATUS_YIELD:\n                yield decode(data);\n                break;\n              case ASYNC_ITERABLE_STATUS_RETURN:\n                return decode(data);\n              case ASYNC_ITERABLE_STATUS_ERROR:\n                throw (\n                  opts.formatError?.({ error: data }) ?? new AsyncError(data)\n                );\n            }\n          }\n        });\n      }\n    }\n  }\n\n  function decode(value: EncodedValue): unknown {\n    const [[data], ...asyncProps] = value;\n\n    for (const value of asyncProps) {\n      const [key] = value;\n      const decoded = decodeChunkDefinition(value);\n\n      if (key === null) {\n        return decoded;\n      }\n\n      (data as any)[key] = decoded;\n    }\n    return data;\n  }\n\n  const closeOrAbort = (reason: unknown) => {\n    headDeferred?.reject(reason);\n    streamManager.cancelAll(reason);\n  };\n  source\n    .pipeTo(\n      new WritableStream({\n        write(chunkOrHead) {\n          if (headDeferred) {\n            const head = chunkOrHead as Record<number | string, unknown>;\n\n            for (const [key, value] of Object.entries(chunkOrHead)) {\n              const parsed = decode(value as any);\n              head[key] = parsed;\n            }\n            headDeferred.resolve(head as THead);\n            headDeferred = null;\n\n            return;\n          }\n          const chunk = chunkOrHead as ChunkData;\n          const [idx] = chunk;\n\n          const controller = streamManager.getOrCreate(idx);\n          controller.enqueue(chunk);\n        },\n        close: () => closeOrAbort(new Error('Stream closed')),\n        abort: closeOrAbort,\n      }),\n      {\n        signal: opts.abortController.signal,\n      },\n    )\n    .catch((error) => {\n      opts.onError?.({ error });\n      closeOrAbort(error);\n    });\n\n  return [await headDeferred.promise, streamManager] as const;\n}\n", "import { Unpromise } from '../../vendor/unpromise';\nimport { getTRPCErrorFromUnknown } from '../error/TRPCError';\nimport { isAbortError } from '../http/abortError';\nimport type { MaybePromise } from '../types';\nimport { identity, run } from '../utils';\nimport type { EventSourceLike } from './sse.types';\nimport type { inferTrackedOutput } from './tracked';\nimport { isTrackedEnvelope } from './tracked';\nimport { takeWithGrace, withMaxDuration } from './utils/asyncIterable';\nimport { makeAsyncResource } from './utils/disposable';\nimport { readableStreamFrom } from './utils/readableStreamFrom';\nimport {\n  disposablePromiseTimerResult,\n  timerResource,\n} from './utils/timerResource';\nimport { PING_SYM, withPing } from './utils/withPing';\n\ntype Serialize = (value: any) => any;\ntype Deserialize = (value: any) => any;\n\n/**\n * @internal\n */\nexport interface SSEPingOptions {\n  /**\n   * Enable ping comments sent from the server\n   * @default false\n   */\n  enabled: boolean;\n  /**\n   * Interval in milliseconds\n   * @default 1000\n   */\n  intervalMs?: number;\n}\n\nexport interface SSEClientOptions {\n  /**\n   * Timeout and reconnect after inactivity in milliseconds\n   * @default undefined\n   */\n  reconnectAfterInactivityMs?: number;\n}\n\nexport interface SSEStreamProducerOptions<TValue = unknown> {\n  serialize?: Serialize;\n  data: AsyncIterable<TValue>;\n\n  maxDepth?: number;\n  ping?: SSEPingOptions;\n  /**\n   * Maximum duration in milliseconds for the request before ending the stream\n   * @default undefined\n   */\n  maxDurationMs?: number;\n  /**\n   * End the request immediately after data is sent\n   * Only useful for serverless runtimes that do not support streaming responses\n   * @default false\n   */\n  emitAndEndImmediately?: boolean;\n  formatError?: (opts: { error: unknown }) => unknown;\n  /**\n   * Client-specific options - these will be sent to the client as part of the first message\n   * @default {}\n   */\n  client?: SSEClientOptions;\n}\n\nconst PING_EVENT = 'ping';\nconst SERIALIZED_ERROR_EVENT = 'serialized-error';\nconst CONNECTED_EVENT = 'connected';\nconst RETURN_EVENT = 'return';\n\ninterface SSEvent {\n  id?: string;\n  data: unknown;\n  comment?: string;\n  event?: string;\n}\n/**\n *\n * @see https://html.spec.whatwg.org/multipage/server-sent-events.html\n */\nexport function sseStreamProducer<TValue = unknown>(\n  opts: SSEStreamProducerOptions<TValue>,\n) {\n  const { serialize = identity } = opts;\n\n  const ping: Required<SSEPingOptions> = {\n    enabled: opts.ping?.enabled ?? false,\n    intervalMs: opts.ping?.intervalMs ?? 1000,\n  };\n  const client: SSEClientOptions = opts.client ?? {};\n\n  if (\n    ping.enabled &&\n    client.reconnectAfterInactivityMs &&\n    ping.intervalMs > client.reconnectAfterInactivityMs\n  ) {\n    throw new Error(\n      `Ping interval must be less than client reconnect interval to prevent unnecessary reconnection - ping.intervalMs: ${ping.intervalMs} client.reconnectAfterInactivityMs: ${client.reconnectAfterInactivityMs}`,\n    );\n  }\n\n  async function* generator(): AsyncIterable<SSEvent, void> {\n    yield {\n      event: CONNECTED_EVENT,\n      data: JSON.stringify(client),\n    };\n\n    type TIteratorValue = Awaited<TValue> | typeof PING_SYM;\n\n    let iterable: AsyncIterable<TValue | typeof PING_SYM> = opts.data;\n\n    if (opts.emitAndEndImmediately) {\n      iterable = takeWithGrace(iterable, {\n        count: 1,\n        gracePeriodMs: 1,\n      });\n    }\n\n    if (\n      opts.maxDurationMs &&\n      opts.maxDurationMs > 0 &&\n      opts.maxDurationMs !== Infinity\n    ) {\n      iterable = withMaxDuration(iterable, {\n        maxDurationMs: opts.maxDurationMs,\n      });\n    }\n\n    if (ping.enabled && ping.intervalMs !== Infinity && ping.intervalMs > 0) {\n      iterable = withPing(iterable, ping.intervalMs);\n    }\n\n    // We need those declarations outside the loop for garbage collection reasons. If they were\n    // declared inside, they would not be freed until the next value is present.\n    let value: null | TIteratorValue;\n    let chunk: null | SSEvent;\n\n    for await (value of iterable) {\n      if (value === PING_SYM) {\n        yield { event: PING_EVENT, data: '' };\n        continue;\n      }\n\n      chunk = isTrackedEnvelope(value)\n        ? { id: value[0], data: value[1] }\n        : { data: value };\n\n      chunk.data = JSON.stringify(serialize(chunk.data));\n\n      yield chunk;\n\n      // free up references for garbage collection\n      value = null;\n      chunk = null;\n    }\n  }\n\n  async function* generatorWithErrorHandling(): AsyncIterable<SSEvent, void> {\n    try {\n      yield* generator();\n\n      yield {\n        event: RETURN_EVENT,\n        data: '',\n      };\n    } catch (cause) {\n      if (isAbortError(cause)) {\n        // ignore abort errors, send any other errors\n        return;\n      }\n      // `err` must be caused by `opts.data`, `JSON.stringify` or `serialize`.\n      // So, a user error in any case.\n      const error = getTRPCErrorFromUnknown(cause);\n      const data = opts.formatError?.({ error }) ?? null;\n      yield {\n        event: SERIALIZED_ERROR_EVENT,\n        data: JSON.stringify(serialize(data)),\n      };\n    }\n  }\n\n  const stream = readableStreamFrom(generatorWithErrorHandling());\n\n  return stream\n    .pipeThrough(\n      new TransformStream({\n        transform(chunk, controller: TransformStreamDefaultController<string>) {\n          if ('event' in chunk) {\n            controller.enqueue(`event: ${chunk.event}\\n`);\n          }\n          if ('data' in chunk) {\n            controller.enqueue(`data: ${chunk.data}\\n`);\n          }\n          if ('id' in chunk) {\n            controller.enqueue(`id: ${chunk.id}\\n`);\n          }\n          if ('comment' in chunk) {\n            controller.enqueue(`: ${chunk.comment}\\n`);\n          }\n          controller.enqueue('\\n\\n');\n        },\n      }),\n    )\n    .pipeThrough(new TextEncoderStream());\n}\n\ninterface ConsumerStreamResultBase<TConfig extends ConsumerConfig> {\n  eventSource: InstanceType<TConfig['EventSource']> | null;\n}\n\ninterface ConsumerStreamResultData<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'data';\n  data: inferTrackedOutput<TConfig['data']>;\n}\n\ninterface ConsumerStreamResultError<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'serialized-error';\n  error: TConfig['error'];\n}\n\ninterface ConsumerStreamResultConnecting<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'connecting';\n  event: EventSourceLike.EventOf<TConfig['EventSource']> | null;\n}\ninterface ConsumerStreamResultTimeout<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'timeout';\n  ms: number;\n}\ninterface ConsumerStreamResultPing<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'ping';\n}\n\ninterface ConsumerStreamResultConnected<TConfig extends ConsumerConfig>\n  extends ConsumerStreamResultBase<TConfig> {\n  type: 'connected';\n  options: SSEClientOptions;\n}\n\ntype ConsumerStreamResult<TConfig extends ConsumerConfig> =\n  | ConsumerStreamResultData<TConfig>\n  | ConsumerStreamResultError<TConfig>\n  | ConsumerStreamResultConnecting<TConfig>\n  | ConsumerStreamResultTimeout<TConfig>\n  | ConsumerStreamResultPing<TConfig>\n  | ConsumerStreamResultConnected<TConfig>;\n\nexport interface SSEStreamConsumerOptions<TConfig extends ConsumerConfig> {\n  url: () => MaybePromise<string>;\n  init: () =>\n    | MaybePromise<EventSourceLike.InitDictOf<TConfig['EventSource']>>\n    | undefined;\n  signal: AbortSignal;\n  deserialize?: Deserialize;\n  EventSource: TConfig['EventSource'];\n}\n\ninterface ConsumerConfig {\n  data: unknown;\n  error: unknown;\n  EventSource: EventSourceLike.AnyConstructor;\n}\n\nasync function withTimeout<T>(opts: {\n  promise: Promise<T>;\n  timeoutMs: number;\n  onTimeout: () => Promise<NoInfer<T>>;\n}): Promise<T> {\n  using timeoutPromise = timerResource(opts.timeoutMs);\n  const res = await Unpromise.race([opts.promise, timeoutPromise.start()]);\n\n  if (res === disposablePromiseTimerResult) {\n    return await opts.onTimeout();\n  }\n  return res;\n}\n\n/**\n * @see https://html.spec.whatwg.org/multipage/server-sent-events.html\n */\nexport function sseStreamConsumer<TConfig extends ConsumerConfig>(\n  opts: SSEStreamConsumerOptions<TConfig>,\n): AsyncIterable<ConsumerStreamResult<TConfig>> {\n  const { deserialize = (v) => v } = opts;\n\n  let clientOptions: SSEClientOptions = {};\n\n  const signal = opts.signal;\n\n  let _es: InstanceType<TConfig['EventSource']> | null = null;\n\n  const createStream = () =>\n    new ReadableStream<ConsumerStreamResult<TConfig>>({\n      async start(controller) {\n        const [url, init] = await Promise.all([opts.url(), opts.init()]);\n        const eventSource = (_es = new opts.EventSource(\n          url,\n          init,\n        ) as InstanceType<TConfig['EventSource']>);\n\n        controller.enqueue({\n          type: 'connecting',\n          eventSource: _es,\n          event: null,\n        });\n\n        eventSource.addEventListener(CONNECTED_EVENT, (_msg) => {\n          const msg = _msg as EventSourceLike.MessageEvent;\n\n          const options: SSEClientOptions = JSON.parse(msg.data);\n\n          clientOptions = options;\n          controller.enqueue({\n            type: 'connected',\n            options,\n            eventSource,\n          });\n        });\n\n        eventSource.addEventListener(SERIALIZED_ERROR_EVENT, (_msg) => {\n          const msg = _msg as EventSourceLike.MessageEvent;\n\n          controller.enqueue({\n            type: 'serialized-error',\n            error: deserialize(JSON.parse(msg.data)),\n            eventSource,\n          });\n        });\n        eventSource.addEventListener(PING_EVENT, () => {\n          controller.enqueue({\n            type: 'ping',\n            eventSource,\n          });\n        });\n        eventSource.addEventListener(RETURN_EVENT, () => {\n          eventSource.close();\n          controller.close();\n          _es = null;\n        });\n        eventSource.addEventListener('error', (event) => {\n          if (eventSource.readyState === eventSource.CLOSED) {\n            controller.error(event);\n          } else {\n            controller.enqueue({\n              type: 'connecting',\n              eventSource,\n              event,\n            });\n          }\n        });\n        eventSource.addEventListener('message', (_msg) => {\n          const msg = _msg as EventSourceLike.MessageEvent;\n\n          const chunk = deserialize(JSON.parse(msg.data));\n\n          const def: SSEvent = {\n            data: chunk,\n          };\n          if (msg.lastEventId) {\n            def.id = msg.lastEventId;\n          }\n          controller.enqueue({\n            type: 'data',\n            data: def as inferTrackedOutput<TConfig['data']>,\n            eventSource,\n          });\n        });\n\n        const onAbort = () => {\n          try {\n            eventSource.close();\n            controller.close();\n          } catch {\n            // ignore errors in case the controller is already closed\n          }\n        };\n        if (signal.aborted) {\n          onAbort();\n        } else {\n          signal.addEventListener('abort', onAbort);\n        }\n      },\n      cancel() {\n        _es?.close();\n      },\n    });\n\n  const getStreamResource = () => {\n    let stream = createStream();\n    let reader = stream.getReader();\n\n    async function dispose() {\n      await reader.cancel();\n      _es = null;\n    }\n\n    return makeAsyncResource(\n      {\n        read() {\n          return reader.read();\n        },\n        async recreate() {\n          await dispose();\n\n          stream = createStream();\n          reader = stream.getReader();\n        },\n      },\n      dispose,\n    );\n  };\n\n  return run(async function* () {\n    await using stream = getStreamResource();\n\n    while (true) {\n      let promise = stream.read();\n\n      const timeoutMs = clientOptions.reconnectAfterInactivityMs;\n      if (timeoutMs) {\n        promise = withTimeout({\n          promise,\n          timeoutMs,\n          onTimeout: async () => {\n            const res: Awaited<typeof promise> = {\n              value: {\n                type: 'timeout',\n                ms: timeoutMs,\n                eventSource: _es,\n              },\n              done: false,\n            };\n            // Close and release old reader\n            await stream.recreate();\n\n            return res;\n          },\n        });\n      }\n\n      const result = await promise;\n\n      if (result.done) {\n        return result.value;\n      }\n      yield result.value;\n    }\n  });\n}\n\nexport const sseHeaders = {\n  'Content-Type': 'text/event-stream',\n  'Cache-Control': 'no-cache, no-transform',\n  'X-Accel-Buffering': 'no',\n  Connection: 'keep-alive',\n} as const;\n", "/* eslint-disable @typescript-eslint/no-non-null-assertion */\nimport {\n  isObservable,\n  observableToAsyncIterable,\n} from '../../observable/observable';\nimport { getErrorShape } from '../error/getErrorShape';\nimport { getTRPCErrorFromUnknown, TRPCError } from '../error/TRPCError';\nimport type { ProcedureType } from '../procedure';\nimport {\n  type AnyRouter,\n  type inferRouterContext,\n  type inferRouterError,\n} from '../router';\nimport type { TRPCResponse } from '../rpc';\nimport { isPromise, jsonlStreamProducer } from '../stream/jsonl';\nimport { sseHeaders, sseStreamProducer } from '../stream/sse';\nimport { transformTRPCResponse } from '../transformer';\nimport { isAsyncIterable, isObject, run } from '../utils';\nimport { getRequestInfo } from './contentType';\nimport { getHTTPStatusCode } from './getHTTPStatusCode';\nimport type {\n  HTTPBaseHandlerOptions,\n  ResolveHTTPRequestOptionsContextFn,\n  TRPCRequestInfo,\n} from './types';\n\nfunction errorToAsyncIterable(err: TRPCError): AsyncIterable<never> {\n  return run(async function* () {\n    throw err;\n  });\n}\ntype HTTPMethods =\n  | 'GET'\n  | 'POST'\n  | 'HEAD'\n  | 'OPTIONS'\n  | 'PUT'\n  | 'DELETE'\n  | 'PATCH';\n\nconst TYPE_ACCEPTED_METHOD_MAP: Record<ProcedureType, HTTPMethods[]> = {\n  mutation: ['POST'],\n  query: ['GET'],\n  subscription: ['GET'],\n};\nconst TYPE_ACCEPTED_METHOD_MAP_WITH_METHOD_OVERRIDE: Record<\n  ProcedureType,\n  HTTPMethods[]\n> = {\n  // never allow GET to do a mutation\n  mutation: ['POST'],\n  query: ['GET', 'POST'],\n  subscription: ['GET', 'POST'],\n};\n\ninterface ResolveHTTPRequestOptions<TRouter extends AnyRouter>\n  extends HTTPBaseHandlerOptions<TRouter, Request> {\n  createContext: ResolveHTTPRequestOptionsContextFn<TRouter>;\n  req: Request;\n  path: string;\n  /**\n   * If the request had an issue before reaching the handler\n   */\n  error: TRPCError | null;\n}\n\nfunction initResponse<TRouter extends AnyRouter, TRequest>(initOpts: {\n  ctx: inferRouterContext<TRouter> | undefined;\n  info: TRPCRequestInfo | undefined;\n  responseMeta?: HTTPBaseHandlerOptions<TRouter, TRequest>['responseMeta'];\n  untransformedJSON:\n    | TRPCResponse<unknown, inferRouterError<TRouter>>\n    | TRPCResponse<unknown, inferRouterError<TRouter>>[]\n    | null;\n  errors: TRPCError[];\n  headers: Headers;\n}) {\n  const {\n    ctx,\n    info,\n    responseMeta,\n    untransformedJSON,\n    errors = [],\n    headers,\n  } = initOpts;\n\n  let status = untransformedJSON ? getHTTPStatusCode(untransformedJSON) : 200;\n\n  const eagerGeneration = !untransformedJSON;\n  const data = eagerGeneration\n    ? []\n    : Array.isArray(untransformedJSON)\n      ? untransformedJSON\n      : [untransformedJSON];\n\n  const meta =\n    responseMeta?.({\n      ctx,\n      info,\n      paths: info?.calls.map((call) => call.path),\n      data,\n      errors,\n      eagerGeneration,\n      type:\n        info?.calls.find((call) => call.procedure?._def.type)?.procedure?._def\n          .type ?? 'unknown',\n    }) ?? {};\n\n  if (meta.headers) {\n    if (meta.headers instanceof Headers) {\n      for (const [key, value] of meta.headers.entries()) {\n        headers.append(key, value);\n      }\n    } else {\n      /**\n       * @deprecated, delete in v12\n       */\n      for (const [key, value] of Object.entries(meta.headers)) {\n        if (Array.isArray(value)) {\n          for (const v of value) {\n            headers.append(key, v);\n          }\n        } else if (typeof value === 'string') {\n          headers.set(key, value);\n        }\n      }\n    }\n  }\n  if (meta.status) {\n    status = meta.status;\n  }\n\n  return {\n    status,\n  };\n}\n\nfunction caughtErrorToData<TRouter extends AnyRouter>(\n  cause: unknown,\n  errorOpts: {\n    opts: Pick<\n      ResolveHTTPRequestOptions<TRouter>,\n      'onError' | 'req' | 'router'\n    >;\n    ctx: inferRouterContext<TRouter> | undefined;\n    type: ProcedureType | 'unknown';\n    path?: string;\n    input?: unknown;\n  },\n) {\n  const { router, req, onError } = errorOpts.opts;\n  const error = getTRPCErrorFromUnknown(cause);\n  onError?.({\n    error,\n    path: errorOpts.path,\n    input: errorOpts.input,\n    ctx: errorOpts.ctx,\n    type: errorOpts.type,\n    req,\n  });\n  const untransformedJSON = {\n    error: getErrorShape({\n      config: router._def._config,\n      error,\n      type: errorOpts.type,\n      path: errorOpts.path,\n      input: errorOpts.input,\n      ctx: errorOpts.ctx,\n    }),\n  };\n  const transformedJSON = transformTRPCResponse(\n    router._def._config,\n    untransformedJSON,\n  );\n  const body = JSON.stringify(transformedJSON);\n  return {\n    error,\n    untransformedJSON,\n    body,\n  };\n}\n\n/**\n * Check if a value is a stream-like object\n * - if it's an async iterable\n * - if it's an object with async iterables or promises\n */\nfunction isDataStream(v: unknown) {\n  if (!isObject(v)) {\n    return false;\n  }\n\n  if (isAsyncIterable(v)) {\n    return true;\n  }\n\n  return (\n    Object.values(v).some(isPromise) || Object.values(v).some(isAsyncIterable)\n  );\n}\n\ntype ResultTuple<T> = [undefined, T] | [TRPCError, undefined];\n\nexport async function resolveResponse<TRouter extends AnyRouter>(\n  opts: ResolveHTTPRequestOptions<TRouter>,\n): Promise<Response> {\n  const { router, req } = opts;\n  const headers = new Headers([['vary', 'trpc-accept']]);\n  const config = router._def._config;\n\n  const url = new URL(req.url);\n\n  if (req.method === 'HEAD') {\n    // can be used for lambda warmup\n    return new Response(null, {\n      status: 204,\n    });\n  }\n\n  const allowBatching = opts.allowBatching ?? opts.batching?.enabled ?? true;\n  const allowMethodOverride =\n    (opts.allowMethodOverride ?? false) && req.method === 'POST';\n\n  type $Context = inferRouterContext<TRouter>;\n\n  const infoTuple: ResultTuple<TRPCRequestInfo> = await run(async () => {\n    try {\n      return [\n        undefined,\n        await getRequestInfo({\n          req,\n          path: decodeURIComponent(opts.path),\n          router,\n          searchParams: url.searchParams,\n          headers: opts.req.headers,\n          url,\n        }),\n      ];\n    } catch (cause) {\n      return [getTRPCErrorFromUnknown(cause), undefined];\n    }\n  });\n\n  interface ContextManager {\n    valueOrUndefined: () => $Context | undefined;\n    value: () => $Context;\n    create: (info: TRPCRequestInfo) => Promise<void>;\n  }\n  const ctxManager: ContextManager = run(() => {\n    let result: ResultTuple<$Context> | undefined = undefined;\n    return {\n      valueOrUndefined: () => {\n        if (!result) {\n          return undefined;\n        }\n        return result[1];\n      },\n      value: () => {\n        const [err, ctx] = result!;\n        if (err) {\n          throw err;\n        }\n        return ctx;\n      },\n      create: async (info) => {\n        if (result) {\n          throw new Error(\n            'This should only be called once - report a bug in tRPC',\n          );\n        }\n        try {\n          const ctx = await opts.createContext({\n            info,\n          });\n          result = [undefined, ctx];\n        } catch (cause) {\n          result = [getTRPCErrorFromUnknown(cause), undefined];\n        }\n      },\n    };\n  });\n\n  const methodMapper = allowMethodOverride\n    ? TYPE_ACCEPTED_METHOD_MAP_WITH_METHOD_OVERRIDE\n    : TYPE_ACCEPTED_METHOD_MAP;\n\n  /**\n   * @deprecated\n   */\n  const isStreamCall = req.headers.get('trpc-accept') === 'application/jsonl';\n\n  const experimentalSSE = config.sse?.enabled ?? true;\n  try {\n    const [infoError, info] = infoTuple;\n    if (infoError) {\n      throw infoError;\n    }\n    if (info.isBatchCall && !allowBatching) {\n      throw new TRPCError({\n        code: 'BAD_REQUEST',\n        message: `Batching is not enabled on the server`,\n      });\n    }\n    /* istanbul ignore if -- @preserve */\n    if (isStreamCall && !info.isBatchCall) {\n      throw new TRPCError({\n        message: `Streaming requests must be batched (you can do a batch of 1)`,\n        code: 'BAD_REQUEST',\n      });\n    }\n    await ctxManager.create(info);\n\n    interface RPCResultOk {\n      data: unknown;\n    }\n    type RPCResult = ResultTuple<RPCResultOk>;\n    const rpcCalls = info.calls.map(async (call): Promise<RPCResult> => {\n      const proc = call.procedure;\n      try {\n        if (opts.error) {\n          throw opts.error;\n        }\n\n        if (!proc) {\n          throw new TRPCError({\n            code: 'NOT_FOUND',\n            message: `No procedure found on path \"${call.path}\"`,\n          });\n        }\n\n        if (!methodMapper[proc._def.type].includes(req.method as HTTPMethods)) {\n          throw new TRPCError({\n            code: 'METHOD_NOT_SUPPORTED',\n            message: `Unsupported ${req.method}-request to ${proc._def.type} procedure at path \"${call.path}\"`,\n          });\n        }\n\n        if (proc._def.type === 'subscription') {\n          /* istanbul ignore if -- @preserve */\n          if (info.isBatchCall) {\n            throw new TRPCError({\n              code: 'BAD_REQUEST',\n              message: `Cannot batch subscription calls`,\n            });\n          }\n        }\n        const data: unknown = await proc({\n          path: call.path,\n          getRawInput: call.getRawInput,\n          ctx: ctxManager.value(),\n          type: proc._def.type,\n          signal: opts.req.signal,\n        });\n        return [undefined, { data }];\n      } catch (cause) {\n        const error = getTRPCErrorFromUnknown(cause);\n        const input = call.result();\n\n        opts.onError?.({\n          error,\n          path: call.path,\n          input,\n          ctx: ctxManager.valueOrUndefined(),\n          type: call.procedure?._def.type ?? 'unknown',\n          req: opts.req,\n        });\n\n        return [error, undefined];\n      }\n    });\n\n    // ----------- response handlers -----------\n    if (!info.isBatchCall) {\n      const [call] = info.calls;\n      const [error, result] = await rpcCalls[0]!;\n\n      switch (info.type) {\n        case 'unknown':\n        case 'mutation':\n        case 'query': {\n          // httpLink\n          headers.set('content-type', 'application/json');\n\n          if (isDataStream(result?.data)) {\n            throw new TRPCError({\n              code: 'UNSUPPORTED_MEDIA_TYPE',\n              message:\n                'Cannot use stream-like response in non-streaming request - use httpBatchStreamLink',\n            });\n          }\n          const res: TRPCResponse<unknown, inferRouterError<TRouter>> = error\n            ? {\n                error: getErrorShape({\n                  config,\n                  ctx: ctxManager.valueOrUndefined(),\n                  error,\n                  input: call!.result(),\n                  path: call!.path,\n                  type: info.type,\n                }),\n              }\n            : { result: { data: result.data } };\n\n          const headResponse = initResponse({\n            ctx: ctxManager.valueOrUndefined(),\n            info,\n            responseMeta: opts.responseMeta,\n            errors: error ? [error] : [],\n            headers,\n            untransformedJSON: [res],\n          });\n          return new Response(\n            JSON.stringify(transformTRPCResponse(config, res)),\n            {\n              status: headResponse.status,\n              headers,\n            },\n          );\n        }\n        case 'subscription': {\n          // httpSubscriptionLink\n\n          const iterable: AsyncIterable<unknown> = run(() => {\n            if (error) {\n              return errorToAsyncIterable(error);\n            }\n            if (!experimentalSSE) {\n              return errorToAsyncIterable(\n                new TRPCError({\n                  code: 'METHOD_NOT_SUPPORTED',\n                  message: 'Missing experimental flag \"sseSubscriptions\"',\n                }),\n              );\n            }\n\n            if (!isObservable(result.data) && !isAsyncIterable(result.data)) {\n              return errorToAsyncIterable(\n                new TRPCError({\n                  message: `Subscription ${\n                    call!.path\n                  } did not return an observable or a AsyncGenerator`,\n                  code: 'INTERNAL_SERVER_ERROR',\n                }),\n              );\n            }\n            const dataAsIterable = isObservable(result.data)\n              ? observableToAsyncIterable(result.data, opts.req.signal)\n              : result.data;\n            return dataAsIterable;\n          });\n\n          const stream = sseStreamProducer({\n            ...config.sse,\n            data: iterable,\n            serialize: (v) => config.transformer.output.serialize(v),\n            formatError(errorOpts) {\n              const error = getTRPCErrorFromUnknown(errorOpts.error);\n              const input = call?.result();\n              const path = call?.path;\n              const type = call?.procedure?._def.type ?? 'unknown';\n\n              opts.onError?.({\n                error,\n                path,\n                input,\n                ctx: ctxManager.valueOrUndefined(),\n                req: opts.req,\n                type,\n              });\n\n              const shape = getErrorShape({\n                config,\n                ctx: ctxManager.valueOrUndefined(),\n                error,\n                input,\n                path,\n                type,\n              });\n\n              return shape;\n            },\n          });\n          for (const [key, value] of Object.entries(sseHeaders)) {\n            headers.set(key, value);\n          }\n\n          const headResponse = initResponse({\n            ctx: ctxManager.valueOrUndefined(),\n            info,\n            responseMeta: opts.responseMeta,\n            errors: [],\n            headers,\n            untransformedJSON: null,\n          });\n\n          return new Response(stream, {\n            headers,\n            status: headResponse.status,\n          });\n        }\n      }\n    }\n\n    // batch response handlers\n    if (info.accept === 'application/jsonl') {\n      // httpBatchStreamLink\n      headers.set('content-type', 'application/json');\n      headers.set('transfer-encoding', 'chunked');\n      const headResponse = initResponse({\n        ctx: ctxManager.valueOrUndefined(),\n        info,\n        responseMeta: opts.responseMeta,\n        errors: [],\n        headers,\n        untransformedJSON: null,\n      });\n      const stream = jsonlStreamProducer({\n        ...config.jsonl,\n        /**\n         * Example structure for `maxDepth: 4`:\n         * {\n         *   // 1\n         *   0: {\n         *     // 2\n         *     result: {\n         *       // 3\n         *       data: // 4\n         *     }\n         *   }\n         * }\n         */\n        maxDepth: Infinity,\n        data: rpcCalls.map(async (res) => {\n          const [error, result] = await res;\n\n          const call = info.calls[0];\n\n          if (error) {\n            return {\n              error: getErrorShape({\n                config,\n                ctx: ctxManager.valueOrUndefined(),\n                error,\n                input: call!.result(),\n                path: call!.path,\n                type: call!.procedure?._def.type ?? 'unknown',\n              }),\n            };\n          }\n\n          /**\n           * Not very pretty, but we need to wrap nested data in promises\n           * Our stream producer will only resolve top-level async values or async values that are directly nested in another async value\n           */\n          const iterable = isObservable(result.data)\n            ? observableToAsyncIterable(result.data, opts.req.signal)\n            : Promise.resolve(result.data);\n          return {\n            result: Promise.resolve({\n              data: iterable,\n            }),\n          };\n        }),\n        serialize: config.transformer.output.serialize,\n        onError: (cause) => {\n          opts.onError?.({\n            error: getTRPCErrorFromUnknown(cause),\n            path: undefined,\n            input: undefined,\n            ctx: ctxManager.valueOrUndefined(),\n            req: opts.req,\n            type: info?.type ?? 'unknown',\n          });\n        },\n\n        formatError(errorOpts) {\n          const call = info?.calls[errorOpts.path[0] as any];\n\n          const error = getTRPCErrorFromUnknown(errorOpts.error);\n          const input = call?.result();\n          const path = call?.path;\n          const type = call?.procedure?._def.type ?? 'unknown';\n\n          // no need to call `onError` here as it will be propagated through the stream itself\n\n          const shape = getErrorShape({\n            config,\n            ctx: ctxManager.valueOrUndefined(),\n            error,\n            input,\n            path,\n            type,\n          });\n\n          return shape;\n        },\n      });\n\n      return new Response(stream, {\n        headers,\n        status: headResponse.status,\n      });\n    }\n\n    // httpBatchLink\n    /**\n     * Non-streaming response:\n     * - await all responses in parallel, blocking on the slowest one\n     * - create headers with known response body\n     * - return a complete HTTPResponse\n     */\n    headers.set('content-type', 'application/json');\n    const results: RPCResult[] = (await Promise.all(rpcCalls)).map(\n      (res): RPCResult => {\n        const [error, result] = res;\n        if (error) {\n          return res;\n        }\n\n        if (isDataStream(result.data)) {\n          return [\n            new TRPCError({\n              code: 'UNSUPPORTED_MEDIA_TYPE',\n              message:\n                'Cannot use stream-like response in non-streaming request - use httpBatchStreamLink',\n            }),\n            undefined,\n          ];\n        }\n        return res;\n      },\n    );\n    const resultAsRPCResponse = results.map(\n      (\n        [error, result],\n        index,\n      ): TRPCResponse<unknown, inferRouterError<TRouter>> => {\n        const call = info.calls[index]!;\n        if (error) {\n          return {\n            error: getErrorShape({\n              config,\n              ctx: ctxManager.valueOrUndefined(),\n              error,\n              input: call.result(),\n              path: call.path,\n              type: call.procedure?._def.type ?? 'unknown',\n            }),\n          };\n        }\n        return {\n          result: { data: result.data },\n        };\n      },\n    );\n\n    const errors = results\n      .map(([error]) => error)\n      .filter(Boolean) as TRPCError[];\n\n    const headResponse = initResponse({\n      ctx: ctxManager.valueOrUndefined(),\n      info,\n      responseMeta: opts.responseMeta,\n      untransformedJSON: resultAsRPCResponse,\n      errors,\n      headers,\n    });\n\n    return new Response(\n      JSON.stringify(transformTRPCResponse(config, resultAsRPCResponse)),\n      {\n        status: headResponse.status,\n        headers,\n      },\n    );\n  } catch (cause) {\n    const [_infoError, info] = infoTuple;\n    const ctx = ctxManager.valueOrUndefined();\n    // we get here if\n    // - batching is called when it's not enabled\n    // - `createContext()` throws\n    // - `router._def._config.transformer.output.serialize()` throws\n    // - post body is too large\n    // - input deserialization fails\n    // - `errorFormatter` return value is malformed\n    const { error, untransformedJSON, body } = caughtErrorToData(cause, {\n      opts,\n      ctx: ctxManager.valueOrUndefined(),\n      type: info?.type ?? 'unknown',\n    });\n\n    const headResponse = initResponse({\n      ctx,\n      info,\n      responseMeta: opts.responseMeta,\n      untransformedJSON,\n      errors: [error],\n      headers,\n    });\n\n    return new Response(body, {\n      status: headResponse.status,\n      headers,\n    });\n  }\n}\n", "import { TRPCError } from './error/TRPCError';\nimport type { ParseFn } from './parser';\nimport type { ProcedureType } from './procedure';\nimport type { GetRawInputFn, Overwrite, Simplify } from './types';\nimport { isObject } from './utils';\n\n/** @internal */\nexport const middlewareMarker = 'middlewareMarker' as 'middlewareMarker' & {\n  __brand: 'middlewareMarker';\n};\ntype MiddlewareMarker = typeof middlewareMarker;\n\ninterface MiddlewareResultBase {\n  /**\n   * All middlewares should pass through their `next()`'s output.\n   * Requiring this marker makes sure that can't be forgotten at compile-time.\n   */\n  readonly marker: MiddlewareMarker;\n}\n\ninterface MiddlewareOKResult<_TContextOverride> extends MiddlewareResultBase {\n  ok: true;\n  data: unknown;\n  // this could be extended with `input`/`rawInput` later\n}\n\ninterface MiddlewareErrorResult<_TContextOverride>\n  extends MiddlewareResultBase {\n  ok: false;\n  error: TRPCError;\n}\n\n/**\n * @internal\n */\nexport type MiddlewareResult<_TContextOverride> =\n  | MiddlewareErrorResult<_TContextOverride>\n  | MiddlewareOKResult<_TContextOverride>;\n\n/**\n * @internal\n */\nexport interface MiddlewareBuilder<\n  TContext,\n  TMeta,\n  TContextOverrides,\n  TInputOut,\n> {\n  /**\n   * Create a new builder based on the current middleware builder\n   */\n  unstable_pipe<$ContextOverridesOut>(\n    fn:\n      | MiddlewareFunction<\n          TContext,\n          TMeta,\n          TContextOverrides,\n          $ContextOverridesOut,\n          TInputOut\n        >\n      | MiddlewareBuilder<\n          Overwrite<TContext, TContextOverrides>,\n          TMeta,\n          $ContextOverridesOut,\n          TInputOut\n        >,\n  ): MiddlewareBuilder<\n    TContext,\n    TMeta,\n    Overwrite<TContextOverrides, $ContextOverridesOut>,\n    TInputOut\n  >;\n\n  /**\n   * List of middlewares within this middleware builder\n   */\n  _middlewares: MiddlewareFunction<\n    TContext,\n    TMeta,\n    TContextOverrides,\n    object,\n    TInputOut\n  >[];\n}\n\n/**\n * @internal\n */\nexport type MiddlewareFunction<\n  TContext,\n  TMeta,\n  TContextOverridesIn,\n  $ContextOverridesOut,\n  TInputOut,\n> = {\n  (opts: {\n    ctx: Simplify<Overwrite<TContext, TContextOverridesIn>>;\n    type: ProcedureType;\n    path: string;\n    input: TInputOut;\n    getRawInput: GetRawInputFn;\n    meta: TMeta | undefined;\n    signal: AbortSignal | undefined;\n    next: {\n      (): Promise<MiddlewareResult<TContextOverridesIn>>;\n      <$ContextOverride>(opts: {\n        ctx?: $ContextOverride;\n        input?: unknown;\n      }): Promise<MiddlewareResult<$ContextOverride>>;\n      (opts: {\n        getRawInput: GetRawInputFn;\n      }): Promise<MiddlewareResult<TContextOverridesIn>>;\n    };\n  }): Promise<MiddlewareResult<$ContextOverridesOut>>;\n  _type?: string | undefined;\n};\n\nexport type AnyMiddlewareFunction = MiddlewareFunction<any, any, any, any, any>;\nexport type AnyMiddlewareBuilder = MiddlewareBuilder<any, any, any, any>;\n/**\n * @internal\n */\nexport function createMiddlewareFactory<\n  TContext,\n  TMeta,\n  TInputOut = unknown,\n>() {\n  function createMiddlewareInner(\n    middlewares: AnyMiddlewareFunction[],\n  ): AnyMiddlewareBuilder {\n    return {\n      _middlewares: middlewares,\n      unstable_pipe(middlewareBuilderOrFn) {\n        const pipedMiddleware =\n          '_middlewares' in middlewareBuilderOrFn\n            ? middlewareBuilderOrFn._middlewares\n            : [middlewareBuilderOrFn];\n\n        return createMiddlewareInner([...middlewares, ...pipedMiddleware]);\n      },\n    };\n  }\n\n  function createMiddleware<$ContextOverrides>(\n    fn: MiddlewareFunction<\n      TContext,\n      TMeta,\n      object,\n      $ContextOverrides,\n      TInputOut\n    >,\n  ): MiddlewareBuilder<TContext, TMeta, $ContextOverrides, TInputOut> {\n    return createMiddlewareInner([fn]);\n  }\n\n  return createMiddleware;\n}\n\n/**\n * Create a standalone middleware\n * @see https://trpc.io/docs/v11/server/middlewares#experimental-standalone-middlewares\n * @deprecated use `.concat()` instead\n */\nexport const experimental_standaloneMiddleware = <\n  TCtx extends {\n    ctx?: object;\n    meta?: object;\n    input?: unknown;\n  },\n>() => ({\n  create: createMiddlewareFactory<\n    TCtx extends { ctx: infer T extends object } ? T : any,\n    TCtx extends { meta: infer T extends object } ? T : object,\n    TCtx extends { input: infer T } ? T : unknown\n  >(),\n});\n\n/**\n * @internal\n * Please note, `trpc-openapi` uses this function.\n */\nexport function createInputMiddleware<TInput>(parse: ParseFn<TInput>) {\n  const inputMiddleware: AnyMiddlewareFunction =\n    async function inputValidatorMiddleware(opts) {\n      let parsedInput: ReturnType<typeof parse>;\n\n      const rawInput = await opts.getRawInput();\n      try {\n        parsedInput = await parse(rawInput);\n      } catch (cause) {\n        throw new TRPCError({\n          code: 'BAD_REQUEST',\n          cause,\n        });\n      }\n\n      // Multiple input parsers\n      const combinedInput =\n        isObject(opts.input) && isObject(parsedInput)\n          ? {\n              ...opts.input,\n              ...parsedInput,\n            }\n          : parsedInput;\n\n      return opts.next({ input: combinedInput });\n    };\n  inputMiddleware._type = 'input';\n  return inputMiddleware;\n}\n\n/**\n * @internal\n */\nexport function createOutputMiddleware<TOutput>(parse: ParseFn<TOutput>) {\n  const outputMiddleware: AnyMiddlewareFunction =\n    async function outputValidatorMiddleware({ next }) {\n      const result = await next();\n      if (!result.ok) {\n        // pass through failures without validating\n        return result;\n      }\n      try {\n        const data = await parse(result.data);\n        return {\n          ...result,\n          data,\n        };\n      } catch (cause) {\n        throw new TRPCError({\n          message: 'Output validation failed',\n          code: 'INTERNAL_SERVER_ERROR',\n          cause,\n        });\n      }\n    };\n  outputMiddleware._type = 'output';\n  return outputMiddleware;\n}\n", "import type { StandardSchemaV1 } from \"./spec\";\n\n/** A schema error with useful information. */\n\nexport class StandardSchemaV1Error extends Error {\n  /** The schema issues. */\n  public readonly issues: ReadonlyArray<StandardSchemaV1.Issue>;\n\n  /**\n   * Creates a schema error with useful information.\n   *\n   * @param issues The schema issues.\n   */\n  constructor(issues: ReadonlyArray<StandardSchemaV1.Issue>) {\n    super(issues[0]?.message);\n    this.name = 'SchemaError';\n    this.issues = issues;\n  }\n}\n", "import { StandardSchemaV1Error } from '../vendor/standard-schema-v1/error';\nimport { type StandardSchemaV1 } from '../vendor/standard-schema-v1/spec';\n\n// zod / typeschema\nexport type ParserZodEsque<TInput, TParsedInput> = {\n  _input: TInput;\n  _output: TParsedInput;\n};\n\nexport type ParserValibotEsque<TInput, TParsedInput> = {\n  schema: {\n    _types?: {\n      input: TInput;\n      output: TParsedInput;\n    };\n  };\n};\n\nexport type ParserArkTypeEsque<TInput, TParsedInput> = {\n  inferIn: TInput;\n  infer: TParsedInput;\n};\n\nexport type ParserStandardSchemaEsque<TInput, TParsedInput> = StandardSchemaV1<\n  TInput,\n  TParsedInput\n>;\n\nexport type ParserMyZodEsque<TInput> = {\n  parse: (input: any) => TInput;\n};\n\nexport type ParserSuperstructEsque<TInput> = {\n  create: (input: unknown) => TInput;\n};\n\nexport type ParserCustomValidatorEsque<TInput> = (\n  input: unknown,\n) => Promise<TInput> | TInput;\n\nexport type ParserYupEsque<TInput> = {\n  validateSync: (input: unknown) => TInput;\n};\n\nexport type ParserScaleEsque<TInput> = {\n  assert(value: unknown): asserts value is TInput;\n};\n\nexport type ParserWithoutInput<TInput> =\n  | ParserCustomValidatorEsque<TInput>\n  | ParserMyZodEsque<TInput>\n  | ParserScaleEsque<TInput>\n  | ParserSuperstructEsque<TInput>\n  | ParserYupEsque<TInput>;\n\nexport type ParserWithInputOutput<TInput, TParsedInput> =\n  | ParserZodEsque<TInput, TParsedInput>\n  | ParserValibotEsque<TInput, TParsedInput>\n  | ParserArkTypeEsque<TInput, TParsedInput>\n  | ParserStandardSchemaEsque<TInput, TParsedInput>;\n\nexport type Parser = ParserWithInputOutput<any, any> | ParserWithoutInput<any>;\n\nexport type inferParser<TParser extends Parser> =\n  TParser extends ParserWithInputOutput<infer $TIn, infer $TOut>\n    ? {\n        in: $TIn;\n        out: $TOut;\n      }\n    : TParser extends ParserWithoutInput<infer $InOut>\n      ? {\n          in: $InOut;\n          out: $InOut;\n        }\n      : never;\n\nexport type ParseFn<TType> = (value: unknown) => Promise<TType> | TType;\n\nexport function getParseFn<TType>(procedureParser: Parser): ParseFn<TType> {\n  const parser = procedureParser as any;\n  const isStandardSchema = '~standard' in parser;\n\n  if (typeof parser === 'function' && typeof parser.assert === 'function') {\n    // ParserArkTypeEsque - arktype schemas shouldn't be called as a function because they return a union type instead of throwing\n    return parser.assert.bind(parser);\n  }\n\n  if (typeof parser === 'function' && !isStandardSchema) {\n    // ParserValibotEsque (>= v0.31.0)\n    // ParserCustomValidatorEsque - note the check for standard-schema conformance - some libraries like `effect` use function schemas which are *not* a \"parse\" function.\n    return parser;\n  }\n\n  if (typeof parser.parseAsync === 'function') {\n    // ParserZodEsque\n    return parser.parseAsync.bind(parser);\n  }\n\n  if (typeof parser.parse === 'function') {\n    // ParserZodEsque\n    // ParserValibotEsque (< v0.13.0)\n    return parser.parse.bind(parser);\n  }\n\n  if (typeof parser.validateSync === 'function') {\n    // ParserYupEsque\n    return parser.validateSync.bind(parser);\n  }\n\n  if (typeof parser.create === 'function') {\n    // ParserSuperstructEsque\n    return parser.create.bind(parser);\n  }\n\n  if (typeof parser.assert === 'function') {\n    // ParserScaleEsque\n    return (value) => {\n      parser.assert(value);\n      return value as TType;\n    };\n  }\n\n  if (isStandardSchema) {\n    // StandardSchemaEsque\n    return async (value) => {\n      const result = await parser['~standard'].validate(value);\n      if (result.issues) {\n        throw new StandardSchemaV1Error(result.issues);\n      }\n      return result.value;\n    };\n  }\n\n  throw new Error('Could not find a validator fn');\n}\n", "import type { inferObservableValue, Observable } from '../observable';\nimport { getTRPCErrorFromUnknown, TRPCError } from './error/TRPCError';\nimport type {\n  AnyMiddlewareFunction,\n  MiddlewareBuilder,\n  MiddlewareFunction,\n  MiddlewareResult,\n} from './middleware';\nimport {\n  createInputMiddleware,\n  createOutputMiddleware,\n  middlewareMarker,\n} from './middleware';\nimport type { inferParser, Parser } from './parser';\nimport { getParseFn } from './parser';\nimport type {\n  AnyMutationProcedure,\n  AnyProcedure,\n  AnyQueryProcedure,\n  LegacyObservableSubscriptionProcedure,\n  MutationProcedure,\n  ProcedureType,\n  QueryProcedure,\n  SubscriptionProcedure,\n} from './procedure';\nimport type { inferTrackedOutput } from './stream/tracked';\nimport type {\n  GetRawInputFn,\n  MaybePromise,\n  Overwrite,\n  Simplify,\n  TypeError,\n} from './types';\nimport type { UnsetMarker } from './utils';\nimport { mergeWithoutOverrides } from './utils';\n\ntype IntersectIfDefined<TType, TWith> = TType extends UnsetMarker\n  ? TWith\n  : TWith extends UnsetMarker\n    ? TType\n    : Simplify<TType & TWith>;\n\ntype DefaultValue<TValue, TFallback> = TValue extends UnsetMarker\n  ? TFallback\n  : TValue;\n\ntype inferAsyncIterable<TOutput> =\n  TOutput extends AsyncIterable<infer $Yield, infer $Return, infer $Next>\n    ? {\n        yield: $Yield;\n        return: $Return;\n        next: $Next;\n      }\n    : never;\ntype inferSubscriptionOutput<TOutput> =\n  TOutput extends AsyncIterable<any>\n    ? AsyncIterable<\n        inferTrackedOutput<inferAsyncIterable<TOutput>['yield']>,\n        inferAsyncIterable<TOutput>['return'],\n        inferAsyncIterable<TOutput>['next']\n      >\n    : TypeError<'Subscription output could not be inferred'>;\n\nexport type CallerOverride<TContext> = (opts: {\n  args: unknown[];\n  invoke: (opts: ProcedureCallOptions<TContext>) => Promise<unknown>;\n  _def: AnyProcedure['_def'];\n}) => Promise<unknown>;\ntype ProcedureBuilderDef<TMeta> = {\n  procedure: true;\n  inputs: Parser[];\n  output?: Parser;\n  meta?: TMeta;\n  resolver?: ProcedureBuilderResolver;\n  middlewares: AnyMiddlewareFunction[];\n  /**\n   * @deprecated use `type` instead\n   */\n  mutation?: boolean;\n  /**\n   * @deprecated use `type` instead\n   */\n  query?: boolean;\n  /**\n   * @deprecated use `type` instead\n   */\n  subscription?: boolean;\n  type?: ProcedureType;\n  caller?: CallerOverride<unknown>;\n};\n\ntype AnyProcedureBuilderDef = ProcedureBuilderDef<any>;\n\n/**\n * Procedure resolver options (what the `.query()`, `.mutation()`, and `.subscription()` functions receive)\n * @internal\n */\nexport interface ProcedureResolverOptions<\n  TContext,\n  _TMeta,\n  TContextOverridesIn,\n  TInputOut,\n> {\n  ctx: Simplify<Overwrite<TContext, TContextOverridesIn>>;\n  input: TInputOut extends UnsetMarker ? undefined : TInputOut;\n  /**\n   * The AbortSignal of the request\n   */\n  signal: AbortSignal | undefined;\n}\n\n/**\n * A procedure resolver\n */\ntype ProcedureResolver<\n  TContext,\n  TMeta,\n  TContextOverrides,\n  TInputOut,\n  TOutputParserIn,\n  $Output,\n> = (\n  opts: ProcedureResolverOptions<TContext, TMeta, TContextOverrides, TInputOut>,\n) => MaybePromise<\n  // If an output parser is defined, we need to return what the parser expects, otherwise we return the inferred type\n  DefaultValue<TOutputParserIn, $Output>\n>;\n\ntype AnyResolver = ProcedureResolver<any, any, any, any, any, any>;\nexport type AnyProcedureBuilder = ProcedureBuilder<\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any\n>;\n\n/**\n * Infer the context type from a procedure builder\n * Useful to create common helper functions for different procedures\n */\nexport type inferProcedureBuilderResolverOptions<\n  TProcedureBuilder extends AnyProcedureBuilder,\n> =\n  TProcedureBuilder extends ProcedureBuilder<\n    infer TContext,\n    infer TMeta,\n    infer TContextOverrides,\n    infer _TInputIn,\n    infer TInputOut,\n    infer _TOutputIn,\n    infer _TOutputOut,\n    infer _TCaller\n  >\n    ? ProcedureResolverOptions<\n        TContext,\n        TMeta,\n        TContextOverrides,\n        TInputOut extends UnsetMarker\n          ? // if input is not set, we don't want to infer it as `undefined` since a procedure further down the chain might have set an input\n            unknown\n          : TInputOut extends object\n            ? Simplify<\n                TInputOut & {\n                  /**\n                   * Extra input params might have been added by a `.input()` further down the chain\n                   */\n                  [keyAddedByInputCallFurtherDown: string]: unknown;\n                }\n              >\n            : TInputOut\n      >\n    : never;\n\nexport interface ProcedureBuilder<\n  TContext,\n  TMeta,\n  TContextOverrides,\n  TInputIn,\n  TInputOut,\n  TOutputIn,\n  TOutputOut,\n  TCaller extends boolean,\n> {\n  /**\n   * Add an input parser to the procedure.\n   * @see https://trpc.io/docs/v11/server/validators\n   */\n  input<$Parser extends Parser>(\n    schema: TInputOut extends UnsetMarker\n      ? $Parser\n      : inferParser<$Parser>['out'] extends Record<string, unknown> | undefined\n        ? TInputOut extends Record<string, unknown> | undefined\n          ? undefined extends inferParser<$Parser>['out'] // if current is optional the previous must be too\n            ? undefined extends TInputOut\n              ? $Parser\n              : TypeError<'Cannot chain an optional parser to a required parser'>\n            : $Parser\n          : TypeError<'All input parsers did not resolve to an object'>\n        : TypeError<'All input parsers did not resolve to an object'>,\n  ): ProcedureBuilder<\n    TContext,\n    TMeta,\n    TContextOverrides,\n    IntersectIfDefined<TInputIn, inferParser<$Parser>['in']>,\n    IntersectIfDefined<TInputOut, inferParser<$Parser>['out']>,\n    TOutputIn,\n    TOutputOut,\n    TCaller\n  >;\n  /**\n   * Add an output parser to the procedure.\n   * @see https://trpc.io/docs/v11/server/validators\n   */\n  output<$Parser extends Parser>(\n    schema: $Parser,\n  ): ProcedureBuilder<\n    TContext,\n    TMeta,\n    TContextOverrides,\n    TInputIn,\n    TInputOut,\n    IntersectIfDefined<TOutputIn, inferParser<$Parser>['in']>,\n    IntersectIfDefined<TOutputOut, inferParser<$Parser>['out']>,\n    TCaller\n  >;\n  /**\n   * Add a meta data to the procedure.\n   * @see https://trpc.io/docs/v11/server/metadata\n   */\n  meta(\n    meta: TMeta,\n  ): ProcedureBuilder<\n    TContext,\n    TMeta,\n    TContextOverrides,\n    TInputIn,\n    TInputOut,\n    TOutputIn,\n    TOutputOut,\n    TCaller\n  >;\n  /**\n   * Add a middleware to the procedure.\n   * @see https://trpc.io/docs/v11/server/middlewares\n   */\n  use<$ContextOverridesOut>(\n    fn:\n      | MiddlewareBuilder<\n          Overwrite<TContext, TContextOverrides>,\n          TMeta,\n          $ContextOverridesOut,\n          TInputOut\n        >\n      | MiddlewareFunction<\n          TContext,\n          TMeta,\n          TContextOverrides,\n          $ContextOverridesOut,\n          TInputOut\n        >,\n  ): ProcedureBuilder<\n    TContext,\n    TMeta,\n    Overwrite<TContextOverrides, $ContextOverridesOut>,\n    TInputIn,\n    TInputOut,\n    TOutputIn,\n    TOutputOut,\n    TCaller\n  >;\n\n  /**\n   * @deprecated use {@link concat} instead\n   */\n  unstable_concat<\n    $Context,\n    $Meta,\n    $ContextOverrides,\n    $InputIn,\n    $InputOut,\n    $OutputIn,\n    $OutputOut,\n  >(\n    builder: Overwrite<TContext, TContextOverrides> extends $Context\n      ? TMeta extends $Meta\n        ? ProcedureBuilder<\n            $Context,\n            $Meta,\n            $ContextOverrides,\n            $InputIn,\n            $InputOut,\n            $OutputIn,\n            $OutputOut,\n            TCaller\n          >\n        : TypeError<'Meta mismatch'>\n      : TypeError<'Context mismatch'>,\n  ): ProcedureBuilder<\n    TContext,\n    TMeta,\n    Overwrite<TContextOverrides, $ContextOverrides>,\n    IntersectIfDefined<TInputIn, $InputIn>,\n    IntersectIfDefined<TInputOut, $InputOut>,\n    IntersectIfDefined<TOutputIn, $OutputIn>,\n    IntersectIfDefined<TOutputOut, $OutputOut>,\n    TCaller\n  >;\n\n  /**\n   * Combine two procedure builders\n   */\n  concat<\n    $Context,\n    $Meta,\n    $ContextOverrides,\n    $InputIn,\n    $InputOut,\n    $OutputIn,\n    $OutputOut,\n  >(\n    builder: Overwrite<TContext, TContextOverrides> extends $Context\n      ? TMeta extends $Meta\n        ? ProcedureBuilder<\n            $Context,\n            $Meta,\n            $ContextOverrides,\n            $InputIn,\n            $InputOut,\n            $OutputIn,\n            $OutputOut,\n            TCaller\n          >\n        : TypeError<'Meta mismatch'>\n      : TypeError<'Context mismatch'>,\n  ): ProcedureBuilder<\n    TContext,\n    TMeta,\n    Overwrite<TContextOverrides, $ContextOverrides>,\n    IntersectIfDefined<TInputIn, $InputIn>,\n    IntersectIfDefined<TInputOut, $InputOut>,\n    IntersectIfDefined<TOutputIn, $OutputIn>,\n    IntersectIfDefined<TOutputOut, $OutputOut>,\n    TCaller\n  >;\n  /**\n   * Query procedure\n   * @see https://trpc.io/docs/v11/concepts#vocabulary\n   */\n  query<$Output>(\n    resolver: ProcedureResolver<\n      TContext,\n      TMeta,\n      TContextOverrides,\n      TInputOut,\n      TOutputIn,\n      $Output\n    >,\n  ): TCaller extends true\n    ? (\n        input: DefaultValue<TInputIn, void>,\n      ) => Promise<DefaultValue<TOutputOut, $Output>>\n    : QueryProcedure<{\n        input: DefaultValue<TInputIn, void>;\n        output: DefaultValue<TOutputOut, $Output>;\n        meta: TMeta;\n      }>;\n\n  /**\n   * Mutation procedure\n   * @see https://trpc.io/docs/v11/concepts#vocabulary\n   */\n  mutation<$Output>(\n    resolver: ProcedureResolver<\n      TContext,\n      TMeta,\n      TContextOverrides,\n      TInputOut,\n      TOutputIn,\n      $Output\n    >,\n  ): TCaller extends true\n    ? (\n        input: DefaultValue<TInputIn, void>,\n      ) => Promise<DefaultValue<TOutputOut, $Output>>\n    : MutationProcedure<{\n        input: DefaultValue<TInputIn, void>;\n        output: DefaultValue<TOutputOut, $Output>;\n        meta: TMeta;\n      }>;\n\n  /**\n   * Subscription procedure\n   * @see https://trpc.io/docs/v11/server/subscriptions\n   */\n  subscription<$Output extends AsyncIterable<any, void, any>>(\n    resolver: ProcedureResolver<\n      TContext,\n      TMeta,\n      TContextOverrides,\n      TInputOut,\n      TOutputIn,\n      $Output\n    >,\n  ): TCaller extends true\n    ? TypeError<'Not implemented'>\n    : SubscriptionProcedure<{\n        input: DefaultValue<TInputIn, void>;\n        output: inferSubscriptionOutput<DefaultValue<TOutputOut, $Output>>;\n        meta: TMeta;\n      }>;\n  /**\n   * @deprecated Using subscriptions with an observable is deprecated. Use an async generator instead.\n   * This feature will be removed in v12 of tRPC.\n   * @see https://trpc.io/docs/v11/server/subscriptions\n   */\n  subscription<$Output extends Observable<any, any>>(\n    resolver: ProcedureResolver<\n      TContext,\n      TMeta,\n      TContextOverrides,\n      TInputOut,\n      TOutputIn,\n      $Output\n    >,\n  ): TCaller extends true\n    ? TypeError<'Not implemented'>\n    : LegacyObservableSubscriptionProcedure<{\n        input: DefaultValue<TInputIn, void>;\n        output: inferObservableValue<DefaultValue<TOutputOut, $Output>>;\n        meta: TMeta;\n      }>;\n  /**\n   * Overrides the way a procedure is invoked\n   * Do not use this unless you know what you're doing - this is an experimental API\n   */\n  experimental_caller(\n    caller: CallerOverride<TContext>,\n  ): ProcedureBuilder<\n    TContext,\n    TMeta,\n    TContextOverrides,\n    TInputIn,\n    TInputOut,\n    TOutputIn,\n    TOutputOut,\n    true\n  >;\n  /**\n   * @internal\n   */\n  _def: ProcedureBuilderDef<TMeta>;\n}\n\ntype ProcedureBuilderResolver = (\n  opts: ProcedureResolverOptions<any, any, any, any>,\n) => Promise<unknown>;\n\nfunction createNewBuilder(\n  def1: AnyProcedureBuilderDef,\n  def2: Partial<AnyProcedureBuilderDef>,\n): AnyProcedureBuilder {\n  const { middlewares = [], inputs, meta, ...rest } = def2;\n\n  // TODO: maybe have a fn here to warn about calls\n  return createBuilder({\n    ...mergeWithoutOverrides(def1, rest),\n    inputs: [...def1.inputs, ...(inputs ?? [])],\n    middlewares: [...def1.middlewares, ...middlewares],\n    meta: def1.meta && meta ? { ...def1.meta, ...meta } : (meta ?? def1.meta),\n  });\n}\n\nexport function createBuilder<TContext, TMeta>(\n  initDef: Partial<AnyProcedureBuilderDef> = {},\n): ProcedureBuilder<\n  TContext,\n  TMeta,\n  object,\n  UnsetMarker,\n  UnsetMarker,\n  UnsetMarker,\n  UnsetMarker,\n  false\n> {\n  const _def: AnyProcedureBuilderDef = {\n    procedure: true,\n    inputs: [],\n    middlewares: [],\n    ...initDef,\n  };\n\n  const builder: AnyProcedureBuilder = {\n    _def,\n    input(input) {\n      const parser = getParseFn(input as Parser);\n      return createNewBuilder(_def, {\n        inputs: [input as Parser],\n        middlewares: [createInputMiddleware(parser)],\n      });\n    },\n    output(output: Parser) {\n      const parser = getParseFn(output);\n      return createNewBuilder(_def, {\n        output,\n        middlewares: [createOutputMiddleware(parser)],\n      });\n    },\n    meta(meta) {\n      return createNewBuilder(_def, {\n        meta,\n      });\n    },\n    use(middlewareBuilderOrFn) {\n      // Distinguish between a middleware builder and a middleware function\n      const middlewares =\n        '_middlewares' in middlewareBuilderOrFn\n          ? middlewareBuilderOrFn._middlewares\n          : [middlewareBuilderOrFn];\n\n      return createNewBuilder(_def, {\n        middlewares: middlewares,\n      });\n    },\n    unstable_concat(builder) {\n      return createNewBuilder(_def, (builder as AnyProcedureBuilder)._def);\n    },\n    concat(builder) {\n      return createNewBuilder(_def, (builder as AnyProcedureBuilder)._def);\n    },\n    query(resolver) {\n      return createResolver(\n        { ..._def, type: 'query' },\n        resolver,\n      ) as AnyQueryProcedure;\n    },\n    mutation(resolver) {\n      return createResolver(\n        { ..._def, type: 'mutation' },\n        resolver,\n      ) as AnyMutationProcedure;\n    },\n    subscription(resolver: ProcedureResolver<any, any, any, any, any, any>) {\n      return createResolver({ ..._def, type: 'subscription' }, resolver) as any;\n    },\n    experimental_caller(caller) {\n      return createNewBuilder(_def, {\n        caller,\n      }) as any;\n    },\n  };\n\n  return builder;\n}\n\nfunction createResolver(\n  _defIn: AnyProcedureBuilderDef & { type: ProcedureType },\n  resolver: AnyResolver,\n) {\n  const finalBuilder = createNewBuilder(_defIn, {\n    resolver,\n    middlewares: [\n      async function resolveMiddleware(opts) {\n        const data = await resolver(opts);\n        return {\n          marker: middlewareMarker,\n          ok: true,\n          data,\n          ctx: opts.ctx,\n        } as const;\n      },\n    ],\n  });\n  const _def: AnyProcedure['_def'] = {\n    ...finalBuilder._def,\n    type: _defIn.type,\n    experimental_caller: Boolean(finalBuilder._def.caller),\n    meta: finalBuilder._def.meta,\n    $types: null as any,\n  };\n\n  const invoke = createProcedureCaller(finalBuilder._def);\n  const callerOverride = finalBuilder._def.caller;\n  if (!callerOverride) {\n    return invoke;\n  }\n  const callerWrapper = async (...args: unknown[]) => {\n    return await callerOverride({\n      args,\n      invoke,\n      _def: _def,\n    });\n  };\n\n  callerWrapper._def = _def;\n\n  return callerWrapper;\n}\n\n/**\n * @internal\n */\nexport interface ProcedureCallOptions<TContext> {\n  ctx: TContext;\n  getRawInput: GetRawInputFn;\n  input?: unknown;\n  path: string;\n  type: ProcedureType;\n  signal: AbortSignal | undefined;\n}\n\nconst codeblock = `\nThis is a client-only function.\nIf you want to call this function on the server, see https://trpc.io/docs/v11/server/server-side-calls\n`.trim();\n\n// run the middlewares recursively with the resolver as the last one\nasync function callRecursive(\n  index: number,\n  _def: AnyProcedureBuilderDef,\n  opts: ProcedureCallOptions<any>,\n): Promise<MiddlewareResult<any>> {\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const middleware = _def.middlewares[index]!;\n    const result = await middleware({\n      ...opts,\n      meta: _def.meta,\n      input: opts.input,\n      next(_nextOpts?: any) {\n        const nextOpts = _nextOpts as\n          | {\n              ctx?: Record<string, unknown>;\n              input?: unknown;\n              getRawInput?: GetRawInputFn;\n            }\n          | undefined;\n\n        return callRecursive(index + 1, _def, {\n          ...opts,\n          ctx: nextOpts?.ctx ? { ...opts.ctx, ...nextOpts.ctx } : opts.ctx,\n          input: nextOpts && 'input' in nextOpts ? nextOpts.input : opts.input,\n          getRawInput: nextOpts?.getRawInput ?? opts.getRawInput,\n        });\n      },\n    });\n\n    return result;\n  } catch (cause) {\n    return {\n      ok: false,\n      error: getTRPCErrorFromUnknown(cause),\n      marker: middlewareMarker,\n    };\n  }\n}\n\nfunction createProcedureCaller(_def: AnyProcedureBuilderDef): AnyProcedure {\n  async function procedure(opts: ProcedureCallOptions<unknown>) {\n    // is direct server-side call\n    if (!opts || !('getRawInput' in opts)) {\n      throw new Error(codeblock);\n    }\n\n    // there's always at least one \"next\" since we wrap this.resolver in a middleware\n    const result = await callRecursive(0, _def, opts);\n\n    if (!result) {\n      throw new TRPCError({\n        code: 'INTERNAL_SERVER_ERROR',\n        message:\n          'No result from middlewares - did you forget to `return next()`?',\n      });\n    }\n    if (!result.ok) {\n      // re-throw original error\n      throw result.error;\n    }\n    return result.data;\n  }\n\n  procedure._def = _def;\n  procedure.procedure = true;\n  procedure.meta = _def.meta;\n\n  // FIXME typecast shouldn't be needed - fixittt\n  return procedure as unknown as AnyProcedure;\n}\n", "import type { CombinedDataTransformer } from '../unstable-core-do-not-import';\nimport type { DefaultErrorShape, ErrorFormatter } from './error/formatter';\nimport type { JSONLProducerOptions } from './stream/jsonl';\nimport type { SSEStreamProducerOptions } from './stream/sse';\n\n/**\n * The initial generics that are used in the init function\n * @internal\n */\nexport interface RootTypes {\n  ctx: object;\n  meta: object;\n  errorShape: DefaultErrorShape;\n  transformer: boolean;\n}\n\n/**\n * The default check to see if we're in a server\n */\nexport const isServerDefault: boolean =\n  typeof window === 'undefined' ||\n  'Deno' in window ||\n  // eslint-disable-next-line @typescript-eslint/dot-notation\n  globalThis.process?.env?.['NODE_ENV'] === 'test' ||\n  !!globalThis.process?.env?.['JEST_WORKER_ID'] ||\n  !!globalThis.process?.env?.['VITEST_WORKER_ID'];\n\n/**\n * The tRPC root config\n * @internal\n */\nexport interface RootConfig<TTypes extends RootTypes> {\n  /**\n   * The types that are used in the config\n   * @internal\n   */\n  $types: TTypes;\n  /**\n   * Use a data transformer\n   * @see https://trpc.io/docs/v11/data-transformers\n   */\n  transformer: CombinedDataTransformer;\n  /**\n   * Use custom error formatting\n   * @see https://trpc.io/docs/v11/error-formatting\n   */\n  errorFormatter: ErrorFormatter<TTypes['ctx'], TTypes['errorShape']>;\n  /**\n   * Allow `@trpc/server` to run in non-server environments\n   * @warning **Use with caution**, this should likely mainly be used within testing.\n   * @default false\n   */\n  allowOutsideOfServer: boolean;\n  /**\n   * Is this a server environment?\n   * @warning **Use with caution**, this should likely mainly be used within testing.\n   * @default typeof window === 'undefined' || 'Deno' in window || process.env.NODE_ENV === 'test'\n   */\n  isServer: boolean;\n  /**\n   * Is this development?\n   * Will be used to decide if the API should return stack traces\n   * @default process.env.NODE_ENV !== 'production'\n   */\n  isDev: boolean;\n\n  defaultMeta?: TTypes['meta'] extends object ? TTypes['meta'] : never;\n\n  /**\n   * Options for server-sent events (SSE) subscriptions\n   * @see https://trpc.io/docs/client/links/httpSubscriptionLink\n   */\n  sse?: {\n    /**\n     * Enable server-sent events (SSE) subscriptions\n     * @default true\n     */\n    enabled?: boolean;\n  } & Pick<\n    SSEStreamProducerOptions,\n    'ping' | 'emitAndEndImmediately' | 'maxDurationMs' | 'client'\n  >;\n\n  /**\n   * Options for batch stream\n   * @see https://trpc.io/docs/client/links/httpBatchStreamLink\n   */\n  jsonl?: Pick<JSONLProducerOptions, 'pingMs'>;\n  experimental?: {};\n}\n\n/**\n * @internal\n */\nexport type CreateRootTypes<TGenerics extends RootTypes> = TGenerics;\n\nexport type AnyRootTypes = CreateRootTypes<{\n  ctx: any;\n  meta: any;\n  errorShape: any;\n  transformer: any;\n}>;\n\ntype PartialIf<TCondition extends boolean, TType> = TCondition extends true\n  ? Partial<TType>\n  : TType;\n\n/**\n * Adds a `createContext` option with a given callback function\n * If context is the default value, then the `createContext` option is optional\n */\nexport type CreateContextCallback<\n  TContext,\n  TFunction extends (...args: any[]) => any,\n> = PartialIf<\n  object extends TContext ? true : false,\n  {\n    /**\n     * @see https://trpc.io/docs/v11/context\n     **/\n    createContext: TFunction;\n  }\n>;\n", "import {\n  defaultFormatter,\n  type DefaultErrorShape,\n  type ErrorFormatter,\n} from './error/formatter';\nimport type { MiddlewareBuilder, MiddlewareFunction } from './middleware';\nimport { createMiddlewareFactory } from './middleware';\nimport type { ProcedureBuilder } from './procedureBuilder';\nimport { createBuilder } from './procedureBuilder';\nimport type { AnyRootTypes, CreateRootTypes } from './rootConfig';\nimport { isServerDefault, type RootConfig } from './rootConfig';\nimport type {\n  AnyRouter,\n  MergeRouters,\n  RouterBuilder,\n  RouterCallerFactory,\n} from './router';\nimport {\n  createCallerFactory,\n  createRouterFactory,\n  mergeRouters,\n} from './router';\nimport type { DataTransformerOptions } from './transformer';\nimport { defaultTransformer, getDataTransformer } from './transformer';\nimport type { Unwrap, ValidateShape } from './types';\nimport type { UnsetMarker } from './utils';\n\ntype inferErrorFormatterShape<TType> =\n  TType extends ErrorFormatter<any, infer TShape> ? TShape : DefaultErrorShape;\n/** @internal */\nexport interface RuntimeConfigOptions<\n  TContext extends object,\n  TMeta extends object,\n> extends Partial<\n    Omit<\n      RootConfig<{\n        ctx: TContext;\n        meta: TMeta;\n        errorShape: any;\n        transformer: any;\n      }>,\n      '$types' | 'transformer'\n    >\n  > {\n  /**\n   * Use a data transformer\n   * @see https://trpc.io/docs/v11/data-transformers\n   */\n  transformer?: DataTransformerOptions;\n}\n\ntype ContextCallback = (...args: any[]) => object | Promise<object>;\n\nexport interface TRPCRootObject<\n  TContext extends object,\n  TMeta extends object,\n  TOptions extends RuntimeConfigOptions<TContext, TMeta>,\n  $Root extends AnyRootTypes = {\n    ctx: TContext;\n    meta: TMeta;\n    errorShape: undefined extends TOptions['errorFormatter']\n      ? DefaultErrorShape\n      : inferErrorFormatterShape<TOptions['errorFormatter']>;\n    transformer: undefined extends TOptions['transformer'] ? false : true;\n  },\n> {\n  /**\n   * Your router config\n   * @internal\n   */\n  _config: RootConfig<$Root>;\n\n  /**\n   * Builder object for creating procedures\n   * @see https://trpc.io/docs/v11/server/procedures\n   */\n  procedure: ProcedureBuilder<\n    TContext,\n    TMeta,\n    object,\n    UnsetMarker,\n    UnsetMarker,\n    UnsetMarker,\n    UnsetMarker,\n    false\n  >;\n\n  /**\n   * Create reusable middlewares\n   * @see https://trpc.io/docs/v11/server/middlewares\n   */\n  middleware: <$ContextOverrides>(\n    fn: MiddlewareFunction<TContext, TMeta, object, $ContextOverrides, unknown>,\n  ) => MiddlewareBuilder<TContext, TMeta, $ContextOverrides, unknown>;\n\n  /**\n   * Create a router\n   * @see https://trpc.io/docs/v11/server/routers\n   */\n  router: RouterBuilder<$Root>;\n\n  /**\n   * Merge Routers\n   * @see https://trpc.io/docs/v11/server/merging-routers\n   */\n  mergeRouters: <TRouters extends AnyRouter[]>(\n    ...routerList: [...TRouters]\n  ) => MergeRouters<TRouters>;\n\n  /**\n   * Create a server-side caller for a router\n   * @see https://trpc.io/docs/v11/server/server-side-calls\n   */\n  createCallerFactory: RouterCallerFactory<$Root>;\n}\n\nclass TRPCBuilder<TContext extends object, TMeta extends object> {\n  /**\n   * Add a context shape as a generic to the root object\n   * @see https://trpc.io/docs/v11/server/context\n   */\n  context<TNewContext extends object | ContextCallback>() {\n    return new TRPCBuilder<\n      TNewContext extends ContextCallback ? Unwrap<TNewContext> : TNewContext,\n      TMeta\n    >();\n  }\n\n  /**\n   * Add a meta shape as a generic to the root object\n   * @see https://trpc.io/docs/v11/quickstart\n   */\n  meta<TNewMeta extends object>() {\n    return new TRPCBuilder<TContext, TNewMeta>();\n  }\n\n  /**\n   * Create the root object\n   * @see https://trpc.io/docs/v11/server/routers#initialize-trpc\n   */\n  create<TOptions extends RuntimeConfigOptions<TContext, TMeta>>(\n    opts?: ValidateShape<TOptions, RuntimeConfigOptions<TContext, TMeta>>,\n  ): TRPCRootObject<TContext, TMeta, TOptions> {\n    type $Root = CreateRootTypes<{\n      ctx: TContext;\n      meta: TMeta;\n      errorShape: undefined extends TOptions['errorFormatter']\n        ? DefaultErrorShape\n        : inferErrorFormatterShape<TOptions['errorFormatter']>;\n      transformer: undefined extends TOptions['transformer'] ? false : true;\n    }>;\n\n    const config: RootConfig<$Root> = {\n      ...opts,\n      transformer: getDataTransformer(opts?.transformer ?? defaultTransformer),\n      isDev:\n        opts?.isDev ??\n        // eslint-disable-next-line @typescript-eslint/dot-notation\n        globalThis.process?.env['NODE_ENV'] !== 'production',\n      allowOutsideOfServer: opts?.allowOutsideOfServer ?? false,\n      errorFormatter: opts?.errorFormatter ?? defaultFormatter,\n      isServer: opts?.isServer ?? isServerDefault,\n      /**\n       * These are just types, they can't be used at runtime\n       * @internal\n       */\n      $types: null as any,\n    };\n\n    {\n      // Server check\n      const isServer: boolean = opts?.isServer ?? isServerDefault;\n\n      if (!isServer && opts?.allowOutsideOfServer !== true) {\n        throw new Error(\n          `You're trying to use @trpc/server in a non-server environment. This is not supported by default.`,\n        );\n      }\n    }\n    return {\n      /**\n       * Your router config\n       * @internal\n       */\n      _config: config,\n      /**\n       * Builder object for creating procedures\n       * @see https://trpc.io/docs/v11/server/procedures\n       */\n      procedure: createBuilder<$Root['ctx'], $Root['meta']>({\n        meta: opts?.defaultMeta,\n      }),\n      /**\n       * Create reusable middlewares\n       * @see https://trpc.io/docs/v11/server/middlewares\n       */\n      middleware: createMiddlewareFactory<$Root['ctx'], $Root['meta']>(),\n      /**\n       * Create a router\n       * @see https://trpc.io/docs/v11/server/routers\n       */\n      router: createRouterFactory<$Root>(config),\n      /**\n       * Merge Routers\n       * @see https://trpc.io/docs/v11/server/merging-routers\n       */\n      mergeRouters,\n      /**\n       * Create a server-side caller for a router\n       * @see https://trpc.io/docs/v11/server/server-side-calls\n       */\n      createCallerFactory: createCallerFactory<$Root>(),\n    };\n  }\n}\n\n/**\n * Builder to initialize the tRPC root object - use this exactly once per backend\n * @see https://trpc.io/docs/v11/quickstart\n */\nexport const initTRPC = new TRPCBuilder();\nexport type { TRPCBuilder };\n", "import type {\n  inferClientTypes,\n  InferrableClientTypes,\n  Maybe,\n  TRPCErrorResponse,\n} from '@trpc/server/unstable-core-do-not-import';\nimport {\n  isObject,\n  type DefaultErrorShape,\n} from '@trpc/server/unstable-core-do-not-import';\n\ntype inferErrorShape<TInferrable extends InferrableClientTypes> =\n  inferClientTypes<TInferrable>['errorShape'];\nexport interface TRPCClientErrorBase<TShape extends DefaultErrorShape> {\n  readonly message: string;\n  readonly shape: Maybe<TShape>;\n  readonly data: Maybe<TShape['data']>;\n}\nexport type TRPCClientErrorLike<TInferrable extends InferrableClientTypes> =\n  TRPCClientErrorBase<inferErrorShape<TInferrable>>;\n\nexport function isTRPCClientError<TInferrable extends InferrableClientTypes>(\n  cause: unknown,\n): cause is TRPCClientError<TInferrable> {\n  return cause instanceof TRPCClientError;\n}\n\nfunction isTRPCErrorResponse(obj: unknown): obj is TRPCErrorResponse<any> {\n  return (\n    isObject(obj) &&\n    isObject(obj['error']) &&\n    typeof obj['error']['code'] === 'number' &&\n    typeof obj['error']['message'] === 'string'\n  );\n}\n\nfunction getMessageFromUnknownError(err: unknown, fallback: string): string {\n  if (typeof err === 'string') {\n    return err;\n  }\n  if (isObject(err) && typeof err['message'] === 'string') {\n    return err['message'];\n  }\n  return fallback;\n}\n\nexport class TRPCClientError<TRouterOrProcedure extends InferrableClientTypes>\n  extends Error\n  implements TRPCClientErrorBase<inferErrorShape<TRouterOrProcedure>>\n{\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore override doesn't work in all environments due to \"This member cannot have an 'override' modifier because it is not declared in the base class 'Error'\"\n  public override readonly cause;\n  public readonly shape: Maybe<inferErrorShape<TRouterOrProcedure>>;\n  public readonly data: Maybe<inferErrorShape<TRouterOrProcedure>['data']>;\n\n  /**\n   * Additional meta data about the error\n   * In the case of HTTP-errors, we'll have `response` and potentially `responseJSON` here\n   */\n  public meta;\n\n  constructor(\n    message: string,\n    opts?: {\n      result?: Maybe<TRPCErrorResponse<inferErrorShape<TRouterOrProcedure>>>;\n      cause?: Error;\n      meta?: Record<string, unknown>;\n    },\n  ) {\n    const cause = opts?.cause;\n\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore https://github.com/tc39/proposal-error-cause\n    super(message, { cause });\n\n    this.meta = opts?.meta;\n\n    this.cause = cause;\n    this.shape = opts?.result?.error;\n    this.data = opts?.result?.error.data;\n    this.name = 'TRPCClientError';\n\n    Object.setPrototypeOf(this, TRPCClientError.prototype);\n  }\n\n  public static from<TRouterOrProcedure extends InferrableClientTypes>(\n    _cause: Error | TRPCErrorResponse<any> | object,\n    opts: { meta?: Record<string, unknown> } = {},\n  ): TRPCClientError<TRouterOrProcedure> {\n    const cause = _cause as unknown;\n\n    if (isTRPCClientError(cause)) {\n      if (opts.meta) {\n        // Decorate with meta error data\n        cause.meta = {\n          ...cause.meta,\n          ...opts.meta,\n        };\n      }\n      return cause;\n    }\n    if (isTRPCErrorResponse(cause)) {\n      return new TRPCClientError(cause.error.message, {\n        ...opts,\n        result: cause,\n      });\n    }\n    return new TRPCClientError(\n      getMessageFromUnknownError(cause, 'Unknown error'),\n      {\n        ...opts,\n        cause: cause as any,\n      },\n    );\n  }\n}\n", "import type {\n  AnyClientTypes,\n  CombinedDataTransformer,\n  DataTransformerOptions,\n  TypeError,\n} from '@trpc/server/unstable-core-do-not-import';\n\n/**\n * @internal\n */\nexport type CoercedTransformerParameters = {\n  transformer?: DataTransformerOptions;\n};\n\ntype TransformerOptionYes = {\n  /**\n   * Data transformer\n   *\n   * You must use the same transformer on the backend and frontend\n   * @see https://trpc.io/docs/v11/data-transformers\n   **/\n  transformer: DataTransformerOptions;\n};\ntype TransformerOptionNo = {\n  /**\n   * Data transformer\n   *\n   * You must use the same transformer on the backend and frontend\n   * @see https://trpc.io/docs/v11/data-transformers\n   **/\n  transformer?: TypeError<'You must define a transformer on your your `initTRPC`-object first'>;\n};\n\n/**\n * @internal\n */\nexport type TransformerOptions<\n  TRoot extends Pick<AnyClientTypes, 'transformer'>,\n> = TRoot['transformer'] extends true\n  ? TransformerOptionYes\n  : TransformerOptionNo;\n/**\n * @internal\n */\n\n/**\n * @internal\n */\nexport function getTransformer(\n  transformer:\n    | TransformerOptions<{ transformer: false }>['transformer']\n    | TransformerOptions<{ transformer: true }>['transformer']\n    | undefined,\n): CombinedDataTransformer {\n  const _transformer =\n    transformer as CoercedTransformerParameters['transformer'];\n  if (!_transformer) {\n    return {\n      input: {\n        serialize: (data) => data,\n        deserialize: (data) => data,\n      },\n      output: {\n        serialize: (data) => data,\n        deserialize: (data) => data,\n      },\n    };\n  }\n  if ('input' in _transformer) {\n    return _transformer;\n  }\n  return {\n    input: _transformer,\n    output: _transformer,\n  };\n}\n", "import type { FetchEsque, NativeFetchEsque } from './internals/types';\n\ntype AnyFn = (...args: any[]) => unknown;\n\nconst isFunction = (fn: unknown): fn is AnyFn => typeof fn === 'function';\n\nexport function getFetch(\n  customFetchImpl?: FetchEsque | NativeFetchEsque,\n): FetchEsque {\n  if (customFetchImpl) {\n    return customFetchImpl as FetchEsque;\n  }\n\n  if (typeof window !== 'undefined' && isFunction(window.fetch)) {\n    return window.fetch as FetchEsque;\n  }\n\n  if (typeof globalThis !== 'undefined' && isFunction(globalThis.fetch)) {\n    return globalThis.fetch as FetchEsque;\n  }\n\n  throw new Error('No fetch implementation found');\n}\n", "import type {\n  AnyClientTypes,\n  CombinedDataTransformer,\n  Maybe,\n  ProcedureType,\n  TRPCAcceptHeader,\n  TRPCResponse,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { getFetch } from '../../getFetch';\nimport type {\n  FetchEsque,\n  RequestInitEsque,\n  ResponseEsque,\n} from '../../internals/types';\nimport type { TransformerOptions } from '../../unstable-internals';\nimport { getTransformer } from '../../unstable-internals';\nimport type { HTTPHeaders } from '../types';\n\n/**\n * @internal\n */\nexport type HTTPLinkBaseOptions<\n  TRoot extends Pick<AnyClientTypes, 'transformer'>,\n> = {\n  url: string | URL;\n  /**\n   * Add ponyfill for fetch\n   */\n  fetch?: FetchEsque;\n  /**\n   * Send all requests `as POST`s requests regardless of the procedure type\n   * The HTTP handler must separately allow overriding the method. See:\n   * @see https://trpc.io/docs/rpc\n   */\n  methodOverride?: 'POST';\n} & TransformerOptions<TRoot>;\n\nexport interface ResolvedHTTPLinkOptions {\n  url: string;\n  fetch?: FetchEsque;\n  transformer: CombinedDataTransformer;\n  methodOverride?: 'POST';\n}\n\nexport function resolveHTTPLinkOptions(\n  opts: HTTPLinkBaseOptions<AnyClientTypes>,\n): ResolvedHTTPLinkOptions {\n  return {\n    url: opts.url.toString(),\n    fetch: opts.fetch,\n    transformer: getTransformer(opts.transformer),\n    methodOverride: opts.methodOverride,\n  };\n}\n\n// https://github.com/trpc/trpc/pull/669\nfunction arrayToDict(array: unknown[]) {\n  const dict: Record<number, unknown> = {};\n  for (let index = 0; index < array.length; index++) {\n    const element = array[index];\n    dict[index] = element;\n  }\n  return dict;\n}\n\nconst METHOD = {\n  query: 'GET',\n  mutation: 'POST',\n  subscription: 'PATCH',\n} as const;\n\nexport interface HTTPResult {\n  json: TRPCResponse;\n  meta: {\n    response: ResponseEsque;\n    responseJSON?: unknown;\n  };\n}\n\ntype GetInputOptions = {\n  transformer: CombinedDataTransformer;\n} & ({ input: unknown } | { inputs: unknown[] });\n\nexport function getInput(opts: GetInputOptions) {\n  return 'input' in opts\n    ? opts.transformer.input.serialize(opts.input)\n    : arrayToDict(\n        opts.inputs.map((_input) => opts.transformer.input.serialize(_input)),\n      );\n}\n\nexport type HTTPBaseRequestOptions = GetInputOptions &\n  ResolvedHTTPLinkOptions & {\n    type: ProcedureType;\n    path: string;\n    signal: Maybe<AbortSignal>;\n  };\n\ntype GetUrl = (opts: HTTPBaseRequestOptions) => string;\ntype GetBody = (opts: HTTPBaseRequestOptions) => RequestInitEsque['body'];\n\nexport type ContentOptions = {\n  trpcAcceptHeader?: TRPCAcceptHeader;\n  contentTypeHeader?: string;\n  getUrl: GetUrl;\n  getBody: GetBody;\n};\n\nexport const getUrl: GetUrl = (opts) => {\n  const parts = opts.url.split('?') as [string, string?];\n  const base = parts[0].replace(/\\/$/, ''); // Remove any trailing slashes\n\n  let url = base + '/' + opts.path;\n  const queryParts: string[] = [];\n\n  if (parts[1]) {\n    queryParts.push(parts[1]);\n  }\n  if ('inputs' in opts) {\n    queryParts.push('batch=1');\n  }\n  if (opts.type === 'query' || opts.type === 'subscription') {\n    const input = getInput(opts);\n    if (input !== undefined && opts.methodOverride !== 'POST') {\n      queryParts.push(`input=${encodeURIComponent(JSON.stringify(input))}`);\n    }\n  }\n  if (queryParts.length) {\n    url += '?' + queryParts.join('&');\n  }\n  return url;\n};\n\nexport const getBody: GetBody = (opts) => {\n  if (opts.type === 'query' && opts.methodOverride !== 'POST') {\n    return undefined;\n  }\n  const input = getInput(opts);\n  return input !== undefined ? JSON.stringify(input) : undefined;\n};\n\nexport type Requester = (\n  opts: HTTPBaseRequestOptions & {\n    headers: () => HTTPHeaders | Promise<HTTPHeaders>;\n  },\n) => Promise<HTTPResult>;\n\nexport const jsonHttpRequester: Requester = (opts) => {\n  return httpRequest({\n    ...opts,\n    contentTypeHeader: 'application/json',\n    getUrl,\n    getBody,\n  });\n};\n\n/**\n * Polyfill for DOMException with AbortError name\n */\nclass AbortError extends Error {\n  constructor() {\n    const name = 'AbortError';\n    super(name);\n    this.name = name;\n    this.message = name;\n  }\n}\n\nexport type HTTPRequestOptions = ContentOptions &\n  HTTPBaseRequestOptions & {\n    headers: () => HTTPHeaders | Promise<HTTPHeaders>;\n  };\n\n/**\n * Polyfill for `signal.throwIfAborted()`\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/throwIfAborted\n */\nconst throwIfAborted = (signal: Maybe<AbortSignal>) => {\n  if (!signal?.aborted) {\n    return;\n  }\n  // If available, use the native implementation\n  signal.throwIfAborted?.();\n\n  // If we have `DOMException`, use it\n  if (typeof DOMException !== 'undefined') {\n    throw new DOMException('AbortError', 'AbortError');\n  }\n\n  // Otherwise, use our own implementation\n  throw new AbortError();\n};\n\nexport async function fetchHTTPResponse(opts: HTTPRequestOptions) {\n  throwIfAborted(opts.signal);\n\n  const url = opts.getUrl(opts);\n  const body = opts.getBody(opts);\n  const { type } = opts;\n  const resolvedHeaders = await (async () => {\n    const heads = await opts.headers();\n    if (Symbol.iterator in heads) {\n      return Object.fromEntries(heads);\n    }\n    return heads;\n  })();\n  const headers = {\n    ...(opts.contentTypeHeader\n      ? { 'content-type': opts.contentTypeHeader }\n      : {}),\n    ...(opts.trpcAcceptHeader\n      ? { 'trpc-accept': opts.trpcAcceptHeader }\n      : undefined),\n    ...resolvedHeaders,\n  };\n\n  return getFetch(opts.fetch)(url, {\n    method: opts.methodOverride ?? METHOD[type],\n    signal: opts.signal,\n    body,\n    headers,\n  });\n}\n\nexport async function httpRequest(\n  opts: HTTPRequestOptions,\n): Promise<HTTPResult> {\n  const meta = {} as HTTPResult['meta'];\n\n  const res = await fetchHTTPResponse(opts);\n  meta.response = res;\n\n  const json = await res.json();\n\n  meta.responseJSON = json;\n\n  return {\n    json: json as TRPCResponse,\n    meta,\n  };\n}\n", "export function isOctetType(input: unknown) {\n  return (\n    input instanceof Uint8Array ||\n    // File extends from Blob but is only available in nodejs from v20\n    input instanceof Blob\n  );\n}\n\nexport function isFormData(input: unknown) {\n  return input instanceof FormData;\n}\n\nexport function isNonJsonSerializable(input: unknown) {\n  return isOctetType(input) || isFormData(input);\n}\n", "import { observable } from '@trpc/server/observable';\nimport type {\n  AnyClientTypes,\n  AnyRouter,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { transformResult } from '@trpc/server/unstable-core-do-not-import';\nimport { TRPCClientError } from '../TRPCClientError';\nimport type {\n  HTTPLinkBaseOptions,\n  HTTPResult,\n  Requester,\n} from './internals/httpUtils';\nimport {\n  getUrl,\n  httpRequest,\n  jsonHttpRequester,\n  resolveHTTPLinkOptions,\n} from './internals/httpUtils';\nimport {\n  isFormData,\n  isOctetType,\n  type HTTPHeaders,\n  type Operation,\n  type TRPCLink,\n} from './types';\n\nexport type HTTPLinkOptions<TRoot extends AnyClientTypes> =\n  HTTPLinkBaseOptions<TRoot> & {\n    /**\n     * Headers to be set on outgoing requests or a callback that of said headers\n     * @see http://trpc.io/docs/client/headers\n     */\n    headers?:\n      | HTTPHeaders\n      | ((opts: { op: Operation }) => HTTPHeaders | Promise<HTTPHeaders>);\n  };\n\nconst universalRequester: Requester = (opts) => {\n  if ('input' in opts) {\n    const { input } = opts;\n    if (isFormData(input)) {\n      if (opts.type !== 'mutation' && opts.methodOverride !== 'POST') {\n        throw new Error('FormData is only supported for mutations');\n      }\n\n      return httpRequest({\n        ...opts,\n        // The browser will set this automatically and include the boundary= in it\n        contentTypeHeader: undefined,\n        getUrl,\n        getBody: () => input,\n      });\n    }\n\n    if (isOctetType(input)) {\n      if (opts.type !== 'mutation' && opts.methodOverride !== 'POST') {\n        throw new Error('Octet type input is only supported for mutations');\n      }\n\n      return httpRequest({\n        ...opts,\n        contentTypeHeader: 'application/octet-stream',\n        getUrl,\n        getBody: () => input,\n      });\n    }\n  }\n\n  return jsonHttpRequester(opts);\n};\n\n/**\n * @see https://trpc.io/docs/client/links/httpLink\n */\nexport function httpLink<TRouter extends AnyRouter = AnyRouter>(\n  opts: HTTPLinkOptions<TRouter['_def']['_config']['$types']>,\n): TRPCLink<TRouter> {\n  const resolvedOpts = resolveHTTPLinkOptions(opts);\n  return () => {\n    return ({ op }) => {\n      return observable((observer) => {\n        const { path, input, type } = op;\n        /* istanbul ignore if -- @preserve */\n        if (type === 'subscription') {\n          throw new Error(\n            'Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`',\n          );\n        }\n\n        const request = universalRequester({\n          ...resolvedOpts,\n          type,\n          path,\n          input,\n          signal: op.signal,\n          headers() {\n            if (!opts.headers) {\n              return {};\n            }\n            if (typeof opts.headers === 'function') {\n              return opts.headers({\n                op,\n              });\n            }\n            return opts.headers;\n          },\n        });\n        let meta: HTTPResult['meta'] | undefined = undefined;\n        request\n          .then((res) => {\n            meta = res.meta;\n            const transformed = transformResult(\n              res.json,\n              resolvedOpts.transformer.output,\n            );\n\n            if (!transformed.ok) {\n              observer.error(\n                TRPCClientError.from(transformed.error, {\n                  meta,\n                }),\n              );\n              return;\n            }\n            observer.next({\n              context: res.meta,\n              result: transformed.result,\n            });\n            observer.complete();\n          })\n          .catch((cause) => {\n            observer.error(TRPCClientError.from(cause, { meta }));\n          });\n\n        return () => {\n          // noop\n        };\n      });\n    };\n  };\n}\n", "/* eslint-disable @typescript-eslint/no-non-null-assertion */\n\ntype BatchItem<TKey, TValue> = {\n  aborted: boolean;\n  key: TKey;\n  resolve: ((value: TValue) => void) | null;\n  reject: ((error: Error) => void) | null;\n  batch: Batch<TKey, TValue> | null;\n};\ntype Batch<TKey, TValue> = {\n  items: BatchItem<TKey, TValue>[];\n};\nexport type Batch<PERSON>oader<TKey, TValue> = {\n  validate: (keys: TKey[]) => boolean;\n  fetch: (keys: TKey[]) => Promise<TValue[] | Promise<TValue>[]>;\n};\n\n/**\n * A function that should never be called unless we messed something up.\n */\nconst throwFatalError = () => {\n  throw new Error(\n    'Something went wrong. Please submit an issue at https://github.com/trpc/trpc/issues/new',\n  );\n};\n\n/**\n * Dataloader that's very inspired by https://github.com/graphql/dataloader\n * Less configuration, no caching, and allows you to cancel requests\n * When cancelling a single fetch the whole batch will be cancelled only when _all_ items are cancelled\n */\nexport function dataLoader<TKey, TValue>(\n  batchLoader: BatchLoader<TKey, TValue>,\n) {\n  let pendingItems: BatchItem<TKey, TValue>[] | null = null;\n  let dispatchTimer: ReturnType<typeof setTimeout> | null = null;\n\n  const destroyTimerAndPendingItems = () => {\n    clearTimeout(dispatchTimer as any);\n    dispatchTimer = null;\n    pendingItems = null;\n  };\n\n  /**\n   * Iterate through the items and split them into groups based on the `batchLoader`'s validate function\n   */\n  function groupItems(items: BatchItem<TKey, TValue>[]) {\n    const groupedItems: BatchItem<TKey, TValue>[][] = [[]];\n    let index = 0;\n    while (true) {\n      const item = items[index];\n      if (!item) {\n        // we're done\n        break;\n      }\n      const lastGroup = groupedItems[groupedItems.length - 1]!;\n\n      if (item.aborted) {\n        // Item was aborted before it was dispatched\n        item.reject?.(new Error('Aborted'));\n        index++;\n        continue;\n      }\n\n      const isValid = batchLoader.validate(\n        lastGroup.concat(item).map((it) => it.key),\n      );\n\n      if (isValid) {\n        lastGroup.push(item);\n        index++;\n        continue;\n      }\n\n      if (lastGroup.length === 0) {\n        item.reject?.(new Error('Input is too big for a single dispatch'));\n        index++;\n        continue;\n      }\n      // Create new group, next iteration will try to add the item to that\n      groupedItems.push([]);\n    }\n    return groupedItems;\n  }\n\n  function dispatch() {\n    const groupedItems = groupItems(pendingItems!);\n    destroyTimerAndPendingItems();\n\n    // Create batches for each group of items\n    for (const items of groupedItems) {\n      if (!items.length) {\n        continue;\n      }\n      const batch: Batch<TKey, TValue> = {\n        items,\n      };\n      for (const item of items) {\n        item.batch = batch;\n      }\n      const promise = batchLoader.fetch(batch.items.map((_item) => _item.key));\n\n      promise\n        .then(async (result) => {\n          await Promise.all(\n            result.map(async (valueOrPromise, index) => {\n              const item = batch.items[index]!;\n              try {\n                const value = await Promise.resolve(valueOrPromise);\n\n                item.resolve?.(value);\n              } catch (cause) {\n                item.reject?.(cause as Error);\n              }\n\n              item.batch = null;\n              item.reject = null;\n              item.resolve = null;\n            }),\n          );\n\n          for (const item of batch.items) {\n            item.reject?.(new Error('Missing result'));\n            item.batch = null;\n          }\n        })\n        .catch((cause) => {\n          for (const item of batch.items) {\n            item.reject?.(cause);\n            item.batch = null;\n          }\n        });\n    }\n  }\n  function load(key: TKey): Promise<TValue> {\n    const item: BatchItem<TKey, TValue> = {\n      aborted: false,\n      key,\n      batch: null,\n      resolve: throwFatalError,\n      reject: throwFatalError,\n    };\n\n    const promise = new Promise<TValue>((resolve, reject) => {\n      item.reject = reject;\n      item.resolve = resolve;\n\n      pendingItems ??= [];\n      pendingItems.push(item);\n    });\n\n    dispatchTimer ??= setTimeout(dispatch);\n\n    return promise;\n  }\n\n  return {\n    load,\n  };\n}\n", "import type { Maybe } from '@trpc/server/unstable-core-do-not-import';\n\n/**\n * Like `Promise.all()` but for abort signals\n * - When all signals have been aborted, the merged signal will be aborted\n * - If one signal is `null`, no signal will be aborted\n */\nexport function allAbortSignals(...signals: Maybe<AbortSignal>[]): AbortSignal {\n  const ac = new AbortController();\n\n  const count = signals.length;\n\n  let abortedCount = 0;\n\n  const onAbort = () => {\n    if (++abortedCount === count) {\n      ac.abort();\n    }\n  };\n\n  for (const signal of signals) {\n    if (signal?.aborted) {\n      onAbort();\n    } else {\n      signal?.addEventListener('abort', onAbort, {\n        once: true,\n      });\n    }\n  }\n\n  return ac.signal;\n}\n\n/**\n * Like `Promise.race` but for abort signals\n *\n * Basically, a ponyfill for\n * [`AbortSignal.any`](https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/any_static).\n */\nexport function raceAbortSignals(\n  ...signals: Maybe<AbortSignal>[]\n): AbortSignal {\n  const ac = new AbortController();\n\n  for (const signal of signals) {\n    if (signal?.aborted) {\n      ac.abort();\n    } else {\n      signal?.addEventListener('abort', () => ac.abort(), { once: true });\n    }\n  }\n\n  return ac.signal;\n}\n\nexport function abortSignalToPromise(signal: AbortSignal): Promise<never> {\n  return new Promise((_, reject) => {\n    if (signal.aborted) {\n      reject(signal.reason);\n      return;\n    }\n    signal.addEventListener(\n      'abort',\n      () => {\n        reject(signal.reason);\n      },\n      { once: true },\n    );\n  });\n}\n", "import type { AnyRouter, ProcedureType } from '@trpc/server';\nimport { observable } from '@trpc/server/observable';\nimport { transformResult } from '@trpc/server/unstable-core-do-not-import';\nimport type { BatchLoader } from '../internals/dataLoader';\nimport { dataLoader } from '../internals/dataLoader';\nimport { allAbortSignals } from '../internals/signals';\nimport type { NonEmptyArray } from '../internals/types';\nimport { TRPCClientError } from '../TRPCClientError';\nimport type { HTTPBatchLinkOptions } from './HTTPBatchLinkOptions';\nimport type { HTTPResult } from './internals/httpUtils';\nimport {\n  getUrl,\n  jsonHttpRequester,\n  resolveHTTPLinkOptions,\n} from './internals/httpUtils';\nimport type { Operation, TRPCLink } from './types';\n\n/**\n * @see https://trpc.io/docs/client/links/httpBatchLink\n */\nexport function httpBatchLink<TRouter extends AnyRouter>(\n  opts: HTTPBatchLinkOptions<TRouter['_def']['_config']['$types']>,\n): TRPCLink<TRouter> {\n  const resolvedOpts = resolveHTTPLinkOptions(opts);\n  const maxURLLength = opts.maxURLLength ?? Infinity;\n  const maxItems = opts.maxItems ?? Infinity;\n\n  return () => {\n    const batchLoader = (\n      type: ProcedureType,\n    ): BatchLoader<Operation, HTTPResult> => {\n      return {\n        validate(batchOps) {\n          if (maxURLLength === Infinity && maxItems === Infinity) {\n            // escape hatch for quick calcs\n            return true;\n          }\n          if (batchOps.length > maxItems) {\n            return false;\n          }\n          const path = batchOps.map((op) => op.path).join(',');\n          const inputs = batchOps.map((op) => op.input);\n\n          const url = getUrl({\n            ...resolvedOpts,\n            type,\n            path,\n            inputs,\n            signal: null,\n          });\n\n          return url.length <= maxURLLength;\n        },\n        async fetch(batchOps) {\n          const path = batchOps.map((op) => op.path).join(',');\n          const inputs = batchOps.map((op) => op.input);\n          const signal = allAbortSignals(...batchOps.map((op) => op.signal));\n\n          const res = await jsonHttpRequester({\n            ...resolvedOpts,\n            path,\n            inputs,\n            type,\n            headers() {\n              if (!opts.headers) {\n                return {};\n              }\n              if (typeof opts.headers === 'function') {\n                return opts.headers({\n                  opList: batchOps as NonEmptyArray<Operation>,\n                });\n              }\n              return opts.headers;\n            },\n            signal,\n          });\n          const resJSON = Array.isArray(res.json)\n            ? res.json\n            : batchOps.map(() => res.json);\n          const result = resJSON.map((item) => ({\n            meta: res.meta,\n            json: item,\n          }));\n          return result;\n        },\n      };\n    };\n\n    const query = dataLoader(batchLoader('query'));\n    const mutation = dataLoader(batchLoader('mutation'));\n\n    const loaders = { query, mutation };\n    return ({ op }) => {\n      return observable((observer) => {\n        /* istanbul ignore if -- @preserve */\n        if (op.type === 'subscription') {\n          throw new Error(\n            'Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`',\n          );\n        }\n        const loader = loaders[op.type];\n        const promise = loader.load(op);\n\n        let _res = undefined as HTTPResult | undefined;\n        promise\n          .then((res) => {\n            _res = res;\n            const transformed = transformResult(\n              res.json,\n              resolvedOpts.transformer.output,\n            );\n\n            if (!transformed.ok) {\n              observer.error(\n                TRPCClientError.from(transformed.error, {\n                  meta: res.meta,\n                }),\n              );\n              return;\n            }\n            observer.next({\n              context: res.meta,\n              result: transformed.result,\n            });\n            observer.complete();\n          })\n          .catch((err) => {\n            observer.error(\n              TRPCClientError.from(err, {\n                meta: _res?.meta,\n              }),\n            );\n          });\n\n        return () => {\n          // noop\n        };\n      });\n    };\n  };\n}\n", "/// <reference lib=\"dom.iterable\" />\n\n// `dom.iterable` types are explicitly required for extracting `FormData` values,\n// as all implementations of `Symbol.iterable` are separated from the main `dom` types.\n// Using triple-slash directive makes sure that it will be available,\n// even if end-user `tsconfig.json` omits it in the `lib` array.\n\nimport { observable, tap } from '@trpc/server/observable';\nimport type {\n  AnyRouter,\n  InferrableClientTypes,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type { TRPCClientError } from '../TRPCClientError';\nimport type { Operation, OperationResultEnvelope, TRPCLink } from './types';\n\ntype ConsoleEsque = {\n  log: (...args: any[]) => void;\n  error: (...args: any[]) => void;\n};\n\ntype EnableFnOptions<TRouter extends InferrableClientTypes> =\n  | {\n      direction: 'down';\n      result:\n        | OperationResultEnvelope<unknown, TRPCClientError<TRouter>>\n        | TRPCClientError<TRouter>;\n    }\n  | (Operation & {\n      direction: 'up';\n    });\ntype EnabledFn<TRouter extends AnyRouter> = (\n  opts: EnableFnOptions<TRouter>,\n) => boolean;\n\ntype LoggerLinkFnOptions<TRouter extends AnyRouter> = Operation &\n  (\n    | {\n        /**\n         * Request result\n         */\n        direction: 'down';\n        result:\n          | OperationResultEnvelope<unknown, TRPCClientError<TRouter>>\n          | TRPCClientError<TRouter>;\n        elapsedMs: number;\n      }\n    | {\n        /**\n         * Request was just initialized\n         */\n        direction: 'up';\n      }\n  );\n\ntype LoggerLinkFn<TRouter extends AnyRouter> = (\n  opts: LoggerLinkFnOptions<TRouter>,\n) => void;\n\ntype ColorMode = 'ansi' | 'css' | 'none';\n\nexport interface LoggerLinkOptions<TRouter extends AnyRouter> {\n  logger?: LoggerLinkFn<TRouter>;\n  enabled?: EnabledFn<TRouter>;\n  /**\n   * Used in the built-in defaultLogger\n   */\n  console?: ConsoleEsque;\n  /**\n   * Color mode\n   * @default typeof window === 'undefined' ? 'ansi' : 'css'\n   */\n  colorMode?: ColorMode;\n\n  /**\n   * Include context in the log - defaults to false unless `colorMode` is 'css'\n   */\n  withContext?: boolean;\n}\n\nfunction isFormData(value: unknown): value is FormData {\n  if (typeof FormData === 'undefined') {\n    // FormData is not supported\n    return false;\n  }\n  return value instanceof FormData;\n}\n\nconst palettes = {\n  css: {\n    query: ['72e3ff', '3fb0d8'],\n    mutation: ['c5a3fc', '904dfc'],\n    subscription: ['ff49e1', 'd83fbe'],\n  },\n  ansi: {\n    regular: {\n      // Cyan background, black and white text respectively\n      query: ['\\x1b[30;46m', '\\x1b[97;46m'],\n      // Magenta background, black and white text respectively\n      mutation: ['\\x1b[30;45m', '\\x1b[97;45m'],\n      // Green background, black and white text respectively\n      subscription: ['\\x1b[30;42m', '\\x1b[97;42m'],\n    },\n    bold: {\n      query: ['\\x1b[1;30;46m', '\\x1b[1;97;46m'],\n      mutation: ['\\x1b[1;30;45m', '\\x1b[1;97;45m'],\n      subscription: ['\\x1b[1;30;42m', '\\x1b[1;97;42m'],\n    },\n  },\n} as const;\n\nfunction constructPartsAndArgs(\n  opts: LoggerLinkFnOptions<any> & {\n    colorMode: ColorMode;\n    withContext?: boolean;\n  },\n) {\n  const { direction, type, withContext, path, id, input } = opts;\n\n  const parts: string[] = [];\n  const args: any[] = [];\n\n  if (opts.colorMode === 'none') {\n    parts.push(direction === 'up' ? '>>' : '<<', type, `#${id}`, path);\n  } else if (opts.colorMode === 'ansi') {\n    const [lightRegular, darkRegular] = palettes.ansi.regular[type];\n    const [lightBold, darkBold] = palettes.ansi.bold[type];\n    const reset = '\\x1b[0m';\n\n    parts.push(\n      direction === 'up' ? lightRegular : darkRegular,\n      direction === 'up' ? '>>' : '<<',\n      type,\n      direction === 'up' ? lightBold : darkBold,\n      `#${id}`,\n      path,\n      reset,\n    );\n  } else {\n    // css color mode\n    const [light, dark] = palettes.css[type];\n    const css = `\n    background-color: #${direction === 'up' ? light : dark};\n    color: ${direction === 'up' ? 'black' : 'white'};\n    padding: 2px;\n  `;\n\n    parts.push(\n      '%c',\n      direction === 'up' ? '>>' : '<<',\n      type,\n      `#${id}`,\n      `%c${path}%c`,\n      '%O',\n    );\n    args.push(\n      css,\n      `${css}; font-weight: bold;`,\n      `${css}; font-weight: normal;`,\n    );\n  }\n\n  if (direction === 'up') {\n    args.push(withContext ? { input, context: opts.context } : { input });\n  } else {\n    args.push({\n      input,\n      result: opts.result,\n      elapsedMs: opts.elapsedMs,\n      ...(withContext && { context: opts.context }),\n    });\n  }\n\n  return { parts, args };\n}\n\n// maybe this should be moved to it's own package\nconst defaultLogger =\n  <TRouter extends AnyRouter>({\n    c = console,\n    colorMode = 'css',\n    withContext,\n  }: {\n    c?: ConsoleEsque;\n    colorMode?: ColorMode;\n    withContext?: boolean;\n  }): LoggerLinkFn<TRouter> =>\n  (props) => {\n    const rawInput = props.input;\n    const input = isFormData(rawInput)\n      ? Object.fromEntries(rawInput)\n      : rawInput;\n\n    const { parts, args } = constructPartsAndArgs({\n      ...props,\n      colorMode,\n      input,\n      withContext,\n    });\n\n    const fn: 'error' | 'log' =\n      props.direction === 'down' &&\n      props.result &&\n      (props.result instanceof Error ||\n        ('error' in props.result.result && props.result.result.error))\n        ? 'error'\n        : 'log';\n\n    c[fn].apply(null, [parts.join(' ')].concat(args));\n  };\n\n/**\n * @see https://trpc.io/docs/v11/client/links/loggerLink\n */\nexport function loggerLink<TRouter extends AnyRouter = AnyRouter>(\n  opts: LoggerLinkOptions<TRouter> = {},\n): TRPCLink<TRouter> {\n  const { enabled = () => true } = opts;\n\n  const colorMode =\n    opts.colorMode ?? (typeof window === 'undefined' ? 'ansi' : 'css');\n  const withContext = opts.withContext ?? colorMode === 'css';\n  const {\n    logger = defaultLogger({ c: opts.console, colorMode, withContext }),\n  } = opts;\n\n  return () => {\n    return ({ op, next }) => {\n      return observable((observer) => {\n        // ->\n        if (enabled({ ...op, direction: 'up' })) {\n          logger({\n            ...op,\n            direction: 'up',\n          });\n        }\n        const requestStartTime = Date.now();\n        function logResult(\n          result:\n            | OperationResultEnvelope<unknown, TRPCClientError<TRouter>>\n            | TRPCClientError<TRouter>,\n        ) {\n          const elapsedMs = Date.now() - requestStartTime;\n\n          if (enabled({ ...op, direction: 'down', result })) {\n            logger({\n              ...op,\n              direction: 'down',\n              elapsedMs,\n              result,\n            });\n          }\n        }\n        return next(op)\n          .pipe(\n            tap({\n              next(result) {\n                logResult(result);\n              },\n              error(result) {\n                logResult(result);\n              },\n            }),\n          )\n          .subscribe(observer);\n      });\n    };\n  };\n}\n", "import type { UrlOptionsWithConnectionParams } from '../../internals/urlWithConnectionParams';\n\nexport interface WebSocketClientOptions extends UrlOptionsWithConnectionParams {\n  /**\n   * Ponyfill which WebSocket implementation to use\n   */\n  WebSocket?: typeof WebSocket;\n  /**\n   * The number of milliseconds before a reconnect is attempted.\n   * @default {@link exponentialBackoff}\n   */\n  retryDelayMs?: (attemptIndex: number) => number;\n  /**\n   * Triggered when a WebSocket connection is established\n   */\n  onOpen?: () => void;\n  /**\n   * Triggered when a WebSocket connection encounters an error\n   */\n  onError?: (evt?: Event) => void;\n  /**\n   * Triggered when a WebSocket connection is closed\n   */\n  onClose?: (cause?: { code?: number }) => void;\n  /**\n   * Lazy mode will close the WebSocket automatically after a period of inactivity (no messages sent or received and no pending requests)\n   */\n  lazy?: {\n    /**\n     * Enable lazy mode\n     * @default false\n     */\n    enabled: boolean;\n    /**\n     * Close the WebSocket after this many milliseconds\n     * @default 0\n     */\n    closeMs: number;\n  };\n  /**\n   * Send ping messages to the server and kill the connection if no pong message is returned\n   */\n  keepAlive?: {\n    /**\n     * @default false\n     */\n    enabled: boolean;\n    /**\n     * Send a ping message every this many milliseconds\n     * @default 5_000\n     */\n    intervalMs?: number;\n    /**\n     * Close the WebSocket after this many milliseconds if the server does not respond\n     * @default 1_000\n     */\n    pongTimeoutMs?: number;\n  };\n}\n\n/**\n * Default options for lazy WebSocket connections.\n * Determines whether the connection should be established lazily and defines the delay before closure.\n */\nexport type LazyOptions = Required<NonNullable<WebSocketClientOptions['lazy']>>;\nexport const lazyDefaults: LazyOptions = {\n  enabled: false,\n  closeMs: 0,\n};\n\n/**\n * Default options for the WebSocket keep-alive mechanism.\n * Configures whether keep-alive is enabled and specifies the timeout and interval for ping-pong messages.\n */\nexport type KeepAliveOptions = Required<\n  NonNullable<WebSocketClientOptions['keepAlive']>\n>;\nexport const keepAliveDefaults: KeepAliveOptions = {\n  enabled: false,\n  pongTimeoutMs: 1_000,\n  intervalMs: 5_000,\n};\n\n/**\n * Calculates a delay for exponential backoff based on the retry attempt index.\n * The delay starts at 0 for the first attempt and doubles for each subsequent attempt,\n * capped at 30 seconds.\n */\nexport const exponentialBackoff = (attemptIndex: number) => {\n  return attemptIndex === 0 ? 0 : Math.min(1000 * 2 ** attemptIndex, 30000);\n};\n", "import { type TRPCRequestInfo } from '@trpc/server/http';\n\n/**\n * Get the result of a value or function that returns a value\n * It also optionally accepts typesafe arguments for the function\n */\nexport const resultOf = <T, TArgs extends any[]>(\n  value: T | ((...args: TArgs) => T),\n  ...args: TArgs\n): T => {\n  return typeof value === 'function'\n    ? (value as (...args: TArgs) => T)(...args)\n    : value;\n};\n\n/**\n * A value that can be wrapped in callback\n */\nexport type CallbackOrValue<T> = T | (() => T | Promise<T>);\n\nexport interface UrlOptionsWithConnectionParams {\n  /**\n   * The URL to connect to (can be a function that returns a URL)\n   */\n  url: CallbackOrValue<string>;\n\n  /**\n   * Connection params that are available in `createContext()`\n   * - For `wsLink`/`wsClient`, these are sent as the first message\n   * - For `httpSubscriptionLink`, these are serialized as part of the URL under the `connectionParams` query\n   */\n  connectionParams?: CallbackOrValue<TRPCRequestInfo['connectionParams']>;\n}\n", "import type {\n  TRPCConnectionParamsMessage,\n  TRPCRequestInfo,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type {\n  CallbackOrValue,\n  UrlOptionsWithConnectionParams,\n} from '../../internals/urlWithConnectionParams';\nimport { resultOf } from '../../internals/urlWithConnectionParams';\n\nexport class TRPCWebSocketClosedError extends Error {\n  constructor(opts: { message: string; cause?: unknown }) {\n    super(opts.message, {\n      cause: opts.cause,\n    });\n    this.name = 'TRPCWebSocketClosedError';\n    Object.setPrototypeOf(this, TRPCWebSocketClosedError.prototype);\n  }\n}\n\n/**\n * Utility class for managing a timeout that can be started, stopped, and reset.\n * Useful for scenarios where the timeout duration is reset dynamically based on events.\n */\nexport class ResettableTimeout {\n  private timeout: ReturnType<typeof setTimeout> | undefined;\n\n  constructor(\n    private readonly onTimeout: () => void,\n    private readonly timeoutMs: number,\n  ) {}\n\n  /**\n   * Resets the current timeout, restarting it with the same duration.\n   * Does nothing if no timeout is active.\n   */\n  public reset() {\n    if (!this.timeout) return;\n\n    clearTimeout(this.timeout);\n    this.timeout = setTimeout(this.onTimeout, this.timeoutMs);\n  }\n\n  public start() {\n    clearTimeout(this.timeout);\n    this.timeout = setTimeout(this.onTimeout, this.timeoutMs);\n  }\n\n  public stop() {\n    clearTimeout(this.timeout);\n    this.timeout = undefined;\n  }\n}\n\n// Ponyfill for Promise.withResolvers https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/withResolvers\nexport function withResolvers<T>() {\n  let resolve: (value: T | PromiseLike<T>) => void;\n  let reject: (reason?: any) => void;\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  return { promise, resolve: resolve!, reject: reject! };\n}\n\n/**\n * Resolves a WebSocket URL and optionally appends connection parameters.\n *\n * If connectionParams are provided, appends 'connectionParams=1' query parameter.\n */\nexport async function prepareUrl(urlOptions: UrlOptionsWithConnectionParams) {\n  const url = await resultOf(urlOptions.url);\n\n  if (!urlOptions.connectionParams) return url;\n\n  // append `?connectionParams=1` when connection params are used\n  const prefix = url.includes('?') ? '&' : '?';\n  const connectionParams = `${prefix}connectionParams=1`;\n\n  return url + connectionParams;\n}\n\nexport async function buildConnectionMessage(\n  connectionParams: CallbackOrValue<TRPCRequestInfo['connectionParams']>,\n) {\n  const message: TRPCConnectionParamsMessage = {\n    method: 'connectionParams',\n    data: await resultOf(connectionParams),\n  };\n\n  return JSON.stringify(message);\n}\n", "import type { AnyTRPCRouter, inferRouterError } from '@trpc/server';\nimport type { Observer } from '@trpc/server/observable';\nimport type {\n  TRPCClientOutgoingMessage,\n  TRPCResponseMessage,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type { TRPCClientError } from '../../../TRPCClientError';\nimport { withResolvers } from './utils';\n\nexport type TCallbacks = Observer<\n  TRPCResponseMessage<unknown, inferRouterError<AnyTRPCRouter>>,\n  TRPCClientError<AnyTRPCRouter>\n>;\n\ntype MessageId = string;\ntype MessageIdLike = string | number | null;\n\n/**\n * Represents a WebSocket request managed by the RequestManager.\n * Combines the network message, a utility promise (`end`) that mirrors the lifecycle\n * handled by `callbacks`, and a set of state monitoring callbacks.\n */\ninterface Request {\n  message: TRPCClientOutgoingMessage;\n  end: Promise<void>;\n  callbacks: TCallbacks;\n}\n\n/**\n * Manages WebSocket requests, tracking their lifecycle and providing utility methods\n * for handling outgoing and pending requests.\n *\n * - **Outgoing requests**: Requests that are queued and waiting to be sent.\n * - **Pending requests**: Requests that have been sent and are in flight awaiting a response.\n *   For subscriptions, multiple responses may be received until the subscription is closed.\n */\nexport class RequestManager {\n  /**\n   * Stores requests that are outgoing, meaning they are registered but not yet sent over the WebSocket.\n   */\n  private outgoingRequests = new Array<Request & { id: MessageId }>();\n\n  /**\n   * Stores requests that are pending (in flight), meaning they have been sent over the WebSocket\n   * and are awaiting responses. For subscriptions, this includes requests\n   * that may receive multiple responses.\n   */\n  private pendingRequests: Record<MessageId, Request> = {};\n\n  /**\n   * Registers a new request by adding it to the outgoing queue and setting up\n   * callbacks for lifecycle events such as completion or error.\n   *\n   * @param message - The outgoing message to be sent.\n   * @param callbacks - Callback functions to observe the request's state.\n   * @returns A cleanup function to manually remove the request.\n   */\n  public register(message: TRPCClientOutgoingMessage, callbacks: TCallbacks) {\n    const { promise: end, resolve } = withResolvers<void>();\n\n    this.outgoingRequests.push({\n      id: String(message.id),\n      message,\n      end,\n      callbacks: {\n        next: callbacks.next,\n        complete: () => {\n          callbacks.complete();\n          resolve();\n        },\n        error: (e) => {\n          callbacks.error(e);\n          resolve();\n        },\n      },\n    });\n\n    return () => {\n      this.delete(message.id);\n      callbacks.complete();\n      resolve();\n    };\n  }\n\n  /**\n   * Deletes a request from both the outgoing and pending collections, if it exists.\n   */\n  public delete(messageId: MessageIdLike) {\n    if (messageId === null) return;\n\n    this.outgoingRequests = this.outgoingRequests.filter(\n      ({ id }) => id !== String(messageId),\n    );\n    delete this.pendingRequests[String(messageId)];\n  }\n\n  /**\n   * Moves all outgoing requests to the pending state and clears the outgoing queue.\n   *\n   * The caller is expected to handle the actual sending of the requests\n   * (e.g., sending them over the network) after this method is called.\n   *\n   * @returns The list of requests that were transitioned to the pending state.\n   */\n  public flush() {\n    const requests = this.outgoingRequests;\n    this.outgoingRequests = [];\n\n    for (const request of requests) {\n      this.pendingRequests[request.id] = request;\n    }\n    return requests;\n  }\n\n  /**\n   * Retrieves all currently pending requests, which are in flight awaiting responses\n   * or handling ongoing subscriptions.\n   */\n  public getPendingRequests() {\n    return Object.values(this.pendingRequests);\n  }\n\n  /**\n   * Retrieves a specific pending request by its message ID.\n   */\n  public getPendingRequest(messageId: MessageIdLike) {\n    if (messageId === null) return null;\n\n    return this.pendingRequests[String(messageId)];\n  }\n\n  /**\n   * Retrieves all outgoing requests, which are waiting to be sent.\n   */\n  public getOutgoingRequests() {\n    return this.outgoingRequests;\n  }\n\n  /**\n   * Retrieves all requests, both outgoing and pending, with their respective states.\n   *\n   * @returns An array of all requests with their state (\"outgoing\" or \"pending\").\n   */\n  public getRequests() {\n    return [\n      ...this.getOutgoingRequests().map((request) => ({\n        state: 'outgoing' as const,\n        message: request.message,\n        end: request.end,\n        callbacks: request.callbacks,\n      })),\n      ...this.getPendingRequests().map((request) => ({\n        state: 'pending' as const,\n        message: request.message,\n        end: request.end,\n        callbacks: request.callbacks,\n      })),\n    ];\n  }\n\n  /**\n   * Checks if there are any pending requests, including ongoing subscriptions.\n   */\n  public hasPendingRequests() {\n    return this.getPendingRequests().length > 0;\n  }\n\n  /**\n   * Checks if there are any pending subscriptions\n   */\n  public hasPendingSubscriptions() {\n    return this.getPendingRequests().some(\n      (request) => request.message.method === 'subscription',\n    );\n  }\n\n  /**\n   * Checks if there are any outgoing requests waiting to be sent.\n   */\n  public hasOutgoingRequests() {\n    return this.outgoingRequests.length > 0;\n  }\n}\n", "import { behaviorSubject } from '@trpc/server/observable';\nimport type { UrlOptionsWithConnectionParams } from '../../internals/urlWithConnectionParams';\nimport { buildConnectionMessage, prepareUrl, withResolvers } from './utils';\n\n/**\n * Opens a WebSocket connection asynchronously and returns a promise\n * that resolves when the connection is successfully established.\n * The promise rejects if an error occurs during the connection attempt.\n */\nfunction asyncWsOpen(ws: WebSocket) {\n  const { promise, resolve, reject } = withResolvers<void>();\n\n  ws.addEventListener('open', () => {\n    ws.removeEventListener('error', reject);\n    resolve();\n  });\n  ws.addEventListener('error', reject);\n\n  return promise;\n}\n\ninterface PingPongOptions {\n  /**\n   * The interval (in milliseconds) between \"PING\" messages.\n   */\n  intervalMs: number;\n\n  /**\n   * The timeout (in milliseconds) to wait for a \"PONG\" response before closing the connection.\n   */\n  pongTimeoutMs: number;\n}\n\n/**\n * Sets up a periodic ping-pong mechanism to keep the WebSocket connection alive.\n *\n * - Sends \"PING\" messages at regular intervals defined by `intervalMs`.\n * - If a \"PONG\" response is not received within the `pongTimeoutMs`, the WebSocket is closed.\n * - The ping timer resets upon receiving any message to maintain activity.\n * - Automatically starts the ping process when the WebSocket connection is opened.\n * - Cleans up timers when the WebSocket is closed.\n *\n * @param ws - The WebSocket instance to manage.\n * @param options - Configuration options for ping-pong intervals and timeouts.\n */\nfunction setupPingInterval(\n  ws: WebSocket,\n  { intervalMs, pongTimeoutMs }: PingPongOptions,\n) {\n  let pingTimeout: ReturnType<typeof setTimeout> | undefined;\n  let pongTimeout: ReturnType<typeof setTimeout> | undefined;\n\n  function start() {\n    pingTimeout = setTimeout(() => {\n      ws.send('PING');\n      pongTimeout = setTimeout(() => {\n        ws.close();\n      }, pongTimeoutMs);\n    }, intervalMs);\n  }\n\n  function reset() {\n    clearTimeout(pingTimeout);\n    start();\n  }\n\n  function pong() {\n    clearTimeout(pongTimeout);\n    reset();\n  }\n\n  ws.addEventListener('open', start);\n  ws.addEventListener('message', ({ data }) => {\n    clearTimeout(pingTimeout);\n    start();\n\n    if (data === 'PONG') {\n      pong();\n    }\n  });\n  ws.addEventListener('close', () => {\n    clearTimeout(pingTimeout);\n    clearTimeout(pongTimeout);\n  });\n}\n\nexport interface WebSocketConnectionOptions {\n  WebSocketPonyfill?: typeof WebSocket;\n  urlOptions: UrlOptionsWithConnectionParams;\n  keepAlive: PingPongOptions & {\n    enabled: boolean;\n  };\n}\n\n/**\n * Manages a WebSocket connection with support for reconnection, keep-alive mechanisms,\n * and observable state tracking.\n */\nexport class WsConnection {\n  static connectCount = 0;\n  public id = ++WsConnection.connectCount;\n\n  private readonly WebSocketPonyfill: typeof WebSocket;\n  private readonly urlOptions: UrlOptionsWithConnectionParams;\n  private readonly keepAliveOpts: WebSocketConnectionOptions['keepAlive'];\n  public readonly wsObservable = behaviorSubject<WebSocket | null>(null);\n\n  constructor(opts: WebSocketConnectionOptions) {\n    this.WebSocketPonyfill = opts.WebSocketPonyfill ?? WebSocket;\n    if (!this.WebSocketPonyfill) {\n      throw new Error(\n        \"No WebSocket implementation found - you probably don't want to use this on the server, but if you do you need to pass a `WebSocket`-ponyfill\",\n      );\n    }\n\n    this.urlOptions = opts.urlOptions;\n    this.keepAliveOpts = opts.keepAlive;\n  }\n\n  public get ws() {\n    return this.wsObservable.get();\n  }\n\n  private set ws(ws) {\n    this.wsObservable.next(ws);\n  }\n\n  /**\n   * Checks if the WebSocket connection is open and ready to communicate.\n   */\n  public isOpen(): this is { ws: WebSocket } {\n    return (\n      !!this.ws &&\n      this.ws.readyState === this.WebSocketPonyfill.OPEN &&\n      !this.openPromise\n    );\n  }\n\n  /**\n   * Checks if the WebSocket connection is closed or in the process of closing.\n   */\n  public isClosed(): this is { ws: WebSocket } {\n    return (\n      !!this.ws &&\n      (this.ws.readyState === this.WebSocketPonyfill.CLOSING ||\n        this.ws.readyState === this.WebSocketPonyfill.CLOSED)\n    );\n  }\n\n  /**\n   * Manages the WebSocket opening process, ensuring that only one open operation\n   * occurs at a time. Tracks the ongoing operation with `openPromise` to avoid\n   * redundant calls and ensure proper synchronization.\n   *\n   * Sets up the keep-alive mechanism and necessary event listeners for the connection.\n   *\n   * @returns A promise that resolves once the WebSocket connection is successfully opened.\n   */\n  private openPromise: Promise<void> | null = null;\n  public async open() {\n    if (this.openPromise) return this.openPromise;\n\n    this.id = ++WsConnection.connectCount;\n    const wsPromise = prepareUrl(this.urlOptions).then(\n      (url) => new this.WebSocketPonyfill(url),\n    );\n    this.openPromise = wsPromise.then(async (ws) => {\n      this.ws = ws;\n\n      // Setup ping listener\n      ws.addEventListener('message', function ({ data }) {\n        if (data === 'PING') {\n          this.send('PONG');\n        }\n      });\n\n      if (this.keepAliveOpts.enabled) {\n        setupPingInterval(ws, this.keepAliveOpts);\n      }\n\n      ws.addEventListener('close', () => {\n        if (this.ws === ws) {\n          this.ws = null;\n        }\n      });\n\n      await asyncWsOpen(ws);\n\n      if (this.urlOptions.connectionParams) {\n        ws.send(await buildConnectionMessage(this.urlOptions.connectionParams));\n      }\n    });\n\n    try {\n      await this.openPromise;\n    } finally {\n      this.openPromise = null;\n    }\n  }\n\n  /**\n   * Closes the WebSocket connection gracefully.\n   * Waits for any ongoing open operation to complete before closing.\n   */\n  public async close() {\n    try {\n      await this.openPromise;\n    } finally {\n      this.ws?.close();\n    }\n  }\n}\n\n/**\n * Provides a backward-compatible representation of the connection state.\n */\nexport function backwardCompatibility(connection: WsConnection) {\n  if (connection.isOpen()) {\n    return {\n      id: connection.id,\n      state: 'open',\n      ws: connection.ws,\n    } as const;\n  }\n\n  if (connection.isClosed()) {\n    return {\n      id: connection.id,\n      state: 'closed',\n      ws: connection.ws,\n    } as const;\n  }\n\n  if (!connection.ws) {\n    return null;\n  }\n\n  return {\n    id: connection.id,\n    state: 'connecting',\n    ws: connection.ws,\n  } as const;\n}\n", "import type { AnyTR<PERSON>Router } from '@trpc/server';\nimport type { BehaviorSubject } from '@trpc/server/observable';\nimport { behaviorSubject, observable } from '@trpc/server/observable';\nimport type {\n  CombinedDataTransformer,\n  TRPCClientIncomingMessage,\n  TRPCClientIncomingRequest,\n  TRPCClientOutgoingMessage,\n  TRPCResponseMessage,\n} from '@trpc/server/unstable-core-do-not-import';\nimport {\n  run,\n  sleep,\n  transformResult,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { TRPCClientError } from '../../../TRPCClientError';\nimport type { TRPCConnectionState } from '../../internals/subscriptions';\nimport type { Operation, OperationResultEnvelope } from '../../types';\nimport type { WebSocketClientOptions } from './options';\nimport { exponentialBackoff, keepAliveDefaults, lazyDefaults } from './options';\nimport type { TCallbacks } from './requestManager';\nimport { RequestManager } from './requestManager';\nimport { ResettableTimeout, TRPCWebSocketClosedError } from './utils';\nimport { backwardCompatibility, WsConnection } from './wsConnection';\n\n/**\n * A WebSocket client for managing TRPC operations, supporting lazy initialization,\n * reconnection, keep-alive, and request management.\n */\nexport class WsClient {\n  /**\n   * Observable tracking the current connection state, including errors.\n   */\n  public readonly connectionState: BehaviorSubject<\n    TRPCConnectionState<TRPCClientError<AnyTRPCRouter>>\n  >;\n\n  private allowReconnect = false;\n  private requestManager = new RequestManager();\n  private readonly activeConnection: WsConnection;\n  private readonly reconnectRetryDelay: (attemptIndex: number) => number;\n  private inactivityTimeout: ResettableTimeout;\n  private readonly callbacks: Pick<\n    WebSocketClientOptions,\n    'onOpen' | 'onClose' | 'onError'\n  >;\n  private readonly lazyMode: boolean;\n\n  constructor(opts: WebSocketClientOptions) {\n    // Initialize callbacks, connection parameters, and options.\n    this.callbacks = {\n      onOpen: opts.onOpen,\n      onClose: opts.onClose,\n      onError: opts.onError,\n    };\n\n    const lazyOptions = {\n      ...lazyDefaults,\n      ...opts.lazy,\n    };\n\n    // Set up inactivity timeout for lazy connections.\n    this.inactivityTimeout = new ResettableTimeout(() => {\n      if (\n        this.requestManager.hasOutgoingRequests() ||\n        this.requestManager.hasPendingRequests()\n      ) {\n        this.inactivityTimeout.reset();\n        return;\n      }\n\n      this.close().catch(() => null);\n    }, lazyOptions.closeMs);\n\n    // Initialize the WebSocket connection.\n    this.activeConnection = new WsConnection({\n      WebSocketPonyfill: opts.WebSocket,\n      urlOptions: opts,\n      keepAlive: {\n        ...keepAliveDefaults,\n        ...opts.keepAlive,\n      },\n    });\n    this.activeConnection.wsObservable.subscribe({\n      next: (ws) => {\n        if (!ws) return;\n        this.setupWebSocketListeners(ws);\n      },\n    });\n    this.reconnectRetryDelay = opts.retryDelayMs ?? exponentialBackoff;\n\n    this.lazyMode = lazyOptions.enabled;\n\n    this.connectionState = behaviorSubject<\n      TRPCConnectionState<TRPCClientError<AnyTRPCRouter>>\n    >({\n      type: 'state',\n      state: lazyOptions.enabled ? 'idle' : 'connecting',\n      error: null,\n    });\n\n    // Automatically open the connection if lazy mode is disabled.\n    if (!this.lazyMode) {\n      this.open().catch(() => null);\n    }\n  }\n\n  /**\n   * Opens the WebSocket connection. Handles reconnection attempts and updates\n   * the connection state accordingly.\n   */\n  private async open() {\n    this.allowReconnect = true;\n    if (this.connectionState.get().state !== 'connecting') {\n      this.connectionState.next({\n        type: 'state',\n        state: 'connecting',\n        error: null,\n      });\n    }\n\n    try {\n      await this.activeConnection.open();\n    } catch (error) {\n      this.reconnect(\n        new TRPCWebSocketClosedError({\n          message: 'Initialization error',\n          cause: error,\n        }),\n      );\n      return this.reconnecting;\n    }\n  }\n\n  /**\n   * Closes the WebSocket connection and stops managing requests.\n   * Ensures all outgoing and pending requests are properly finalized.\n   */\n  public async close() {\n    this.allowReconnect = false;\n    this.inactivityTimeout.stop();\n\n    const requestsToAwait: Promise<void>[] = [];\n    for (const request of this.requestManager.getRequests()) {\n      if (request.message.method === 'subscription') {\n        request.callbacks.complete();\n      } else if (request.state === 'outgoing') {\n        request.callbacks.error(\n          TRPCClientError.from(\n            new TRPCWebSocketClosedError({\n              message: 'Closed before connection was established',\n            }),\n          ),\n        );\n      } else {\n        requestsToAwait.push(request.end);\n      }\n    }\n\n    await Promise.all(requestsToAwait).catch(() => null);\n    await this.activeConnection.close().catch(() => null);\n\n    this.connectionState.next({\n      type: 'state',\n      state: 'idle',\n      error: null,\n    });\n  }\n\n  /**\n   * Method to request the server.\n   * Handles data transformation, batching of requests, and subscription lifecycle.\n   *\n   * @param op - The operation details including id, type, path, input and signal\n   * @param transformer - Data transformer for serializing requests and deserializing responses\n   * @param lastEventId - Optional ID of the last received event for subscriptions\n   *\n   * @returns An observable that emits operation results and handles cleanup\n   */\n  public request({\n    op: { id, type, path, input, signal },\n    transformer,\n    lastEventId,\n  }: {\n    op: Pick<Operation, 'id' | 'type' | 'path' | 'input' | 'signal'>;\n    transformer: CombinedDataTransformer;\n    lastEventId?: string;\n  }) {\n    return observable<\n      OperationResultEnvelope<unknown, TRPCClientError<AnyTRPCRouter>>,\n      TRPCClientError<AnyTRPCRouter>\n    >((observer) => {\n      const abort = this.batchSend(\n        {\n          id,\n          method: type,\n          params: {\n            input: transformer.input.serialize(input),\n            path,\n            lastEventId,\n          },\n        },\n        {\n          ...observer,\n          next(event) {\n            const transformed = transformResult(event, transformer.output);\n\n            if (!transformed.ok) {\n              observer.error(TRPCClientError.from(transformed.error));\n              return;\n            }\n\n            observer.next({\n              result: transformed.result,\n            });\n          },\n        },\n      );\n\n      return () => {\n        abort();\n\n        if (type === 'subscription' && this.activeConnection.isOpen()) {\n          this.send({\n            id,\n            method: 'subscription.stop',\n          });\n        }\n\n        signal?.removeEventListener('abort', abort);\n      };\n    });\n  }\n\n  public get connection() {\n    return backwardCompatibility(this.activeConnection);\n  }\n\n  /**\n   * Manages the reconnection process for the WebSocket using retry logic.\n   * Ensures that only one reconnection attempt is active at a time by tracking the current\n   * reconnection state in the `reconnecting` promise.\n   */\n  private reconnecting: Promise<void> | null = null;\n  private reconnect(closedError: TRPCWebSocketClosedError) {\n    this.connectionState.next({\n      type: 'state',\n      state: 'connecting',\n      error: TRPCClientError.from(closedError),\n    });\n    if (this.reconnecting) return;\n\n    const tryReconnect = async (attemptIndex: number) => {\n      try {\n        await sleep(this.reconnectRetryDelay(attemptIndex));\n        if (this.allowReconnect) {\n          await this.activeConnection.close();\n          await this.activeConnection.open();\n\n          if (this.requestManager.hasPendingRequests()) {\n            this.send(\n              this.requestManager\n                .getPendingRequests()\n                .map(({ message }) => message),\n            );\n          }\n        }\n        this.reconnecting = null;\n      } catch {\n        await tryReconnect(attemptIndex + 1);\n      }\n    };\n\n    this.reconnecting = tryReconnect(0);\n  }\n\n  private setupWebSocketListeners(ws: WebSocket) {\n    const handleCloseOrError = (cause: unknown) => {\n      const reqs = this.requestManager.getPendingRequests();\n      for (const { message, callbacks } of reqs) {\n        if (message.method === 'subscription') continue;\n\n        callbacks.error(\n          TRPCClientError.from(\n            cause ??\n              new TRPCWebSocketClosedError({\n                message: 'WebSocket closed',\n                cause,\n              }),\n          ),\n        );\n        this.requestManager.delete(message.id);\n      }\n    };\n\n    ws.addEventListener('open', () => {\n      run(async () => {\n        if (this.lazyMode) {\n          this.inactivityTimeout.start();\n        }\n\n        this.callbacks.onOpen?.();\n\n        this.connectionState.next({\n          type: 'state',\n          state: 'pending',\n          error: null,\n        });\n      }).catch((error) => {\n        ws.close(3000);\n        handleCloseOrError(error);\n      });\n    });\n\n    ws.addEventListener('message', ({ data }) => {\n      this.inactivityTimeout.reset();\n\n      if (typeof data !== 'string' || ['PING', 'PONG'].includes(data)) return;\n\n      const incomingMessage = JSON.parse(data) as TRPCClientIncomingMessage;\n      if ('method' in incomingMessage) {\n        this.handleIncomingRequest(incomingMessage);\n        return;\n      }\n\n      this.handleResponseMessage(incomingMessage);\n    });\n\n    ws.addEventListener('close', (event) => {\n      handleCloseOrError(event);\n      this.callbacks.onClose?.(event);\n\n      if (!this.lazyMode || this.requestManager.hasPendingSubscriptions()) {\n        this.reconnect(\n          new TRPCWebSocketClosedError({\n            message: 'WebSocket closed',\n            cause: event,\n          }),\n        );\n      }\n    });\n\n    ws.addEventListener('error', (event) => {\n      handleCloseOrError(event);\n      this.callbacks.onError?.(event);\n\n      this.reconnect(\n        new TRPCWebSocketClosedError({\n          message: 'WebSocket closed',\n          cause: event,\n        }),\n      );\n    });\n  }\n\n  private handleResponseMessage(message: TRPCResponseMessage) {\n    const request = this.requestManager.getPendingRequest(message.id);\n    if (!request) return;\n\n    request.callbacks.next(message);\n\n    let completed = true;\n    if ('result' in message && request.message.method === 'subscription') {\n      if (message.result.type === 'data') {\n        request.message.params.lastEventId = message.result.id;\n      }\n\n      if (message.result.type !== 'stopped') {\n        completed = false;\n      }\n    }\n\n    if (completed) {\n      request.callbacks.complete();\n      this.requestManager.delete(message.id);\n    }\n  }\n\n  private handleIncomingRequest(message: TRPCClientIncomingRequest) {\n    if (message.method === 'reconnect') {\n      this.reconnect(\n        new TRPCWebSocketClosedError({\n          message: 'Server requested reconnect',\n        }),\n      );\n    }\n  }\n\n  /**\n   * Sends a message or batch of messages directly to the server.\n   */\n  private send(\n    messageOrMessages: TRPCClientOutgoingMessage | TRPCClientOutgoingMessage[],\n  ) {\n    if (!this.activeConnection.isOpen()) {\n      throw new Error('Active connection is not open');\n    }\n\n    const messages =\n      messageOrMessages instanceof Array\n        ? messageOrMessages\n        : [messageOrMessages];\n    this.activeConnection.ws.send(\n      JSON.stringify(messages.length === 1 ? messages[0] : messages),\n    );\n  }\n\n  /**\n   * Groups requests for batch sending.\n   *\n   * @returns A function to abort the batched request.\n   */\n  private batchSend(message: TRPCClientOutgoingMessage, callbacks: TCallbacks) {\n    this.inactivityTimeout.reset();\n\n    run(async () => {\n      if (!this.activeConnection.isOpen()) {\n        await this.open();\n      }\n      await sleep(0);\n\n      if (!this.requestManager.hasOutgoingRequests()) return;\n\n      this.send(this.requestManager.flush().map(({ message }) => message));\n    }).catch((err) => {\n      this.requestManager.delete(message.id);\n      callbacks.error(TRPCClientError.from(err));\n    });\n\n    return this.requestManager.register(message, callbacks);\n  }\n}\n", "import type { WebSocketClientOptions } from './wsClient/options';\nimport { WsClient } from './wsClient/wsClient';\n\nexport function createWSClient(opts: WebSocketClientOptions) {\n  return new WsClient(opts);\n}\n\nexport type TRPCWebSocketClient = ReturnType<typeof createWSClient>;\n\nexport { WebSocketClientOptions };\n", "import { observable } from '@trpc/server/observable';\nimport type {\n  AnyRouter,\n  inferClientTypes,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type { TransformerOptions } from '../../unstable-internals';\nimport { getTransformer } from '../../unstable-internals';\nimport type { TRPCLink } from '../types';\nimport type {\n  TRPCWebSocketClient,\n  WebSocketClientOptions,\n} from './createWsClient';\nimport { createWSClient } from './createWsClient';\n\nexport type WebSocketLinkOptions<TRouter extends AnyRouter> = {\n  client: TRPCWebSocketClient;\n} & TransformerOptions<inferClientTypes<TRouter>>;\n\nexport function wsLink<TRouter extends AnyRouter>(\n  opts: WebSocketLinkOptions<TRouter>,\n): TRPCLink<TRouter> {\n  const { client } = opts;\n  const transformer = getTransformer(opts.transformer);\n  return () => {\n    return ({ op }) => {\n      return observable((observer) => {\n        const connStateSubscription =\n          op.type === 'subscription'\n            ? client.connectionState.subscribe({\n                next(result) {\n                  observer.next({\n                    result,\n                    context: op.context,\n                  });\n                },\n              })\n            : null;\n\n        const requestSubscription = client\n          .request({\n            op,\n            transformer,\n          })\n          .subscribe(observer);\n\n        return () => {\n          requestSubscription.unsubscribe();\n          connStateSubscription?.unsubscribe();\n        };\n      });\n    };\n  };\n}\n\nexport { TRPCWebSocketClient, WebSocketClientOptions, createWSClient };\n", "import type {\n  inferObservableValue,\n  Unsubscribable,\n} from '@trpc/server/observable';\nimport { observableToPromise, share } from '@trpc/server/observable';\nimport type {\n  AnyRouter,\n  inferAsyncIterableYield,\n  InferrableClientTypes,\n  Maybe,\n  TypeError,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { createChain } from '../links/internals/createChain';\nimport type { TRPCConnectionState } from '../links/internals/subscriptions';\nimport type {\n  OperationContext,\n  OperationLink,\n  TRPCClientRuntime,\n  TRPCLink,\n} from '../links/types';\nimport { TRPCClientError } from '../TRPCClientError';\n\ntype TRPCType = 'mutation' | 'query' | 'subscription';\nexport interface TRPCRequestOptions {\n  /**\n   * Pass additional context to links\n   */\n  context?: OperationContext;\n  signal?: AbortSignal;\n}\n\nexport interface TRPCSubscriptionObserver<TValue, TError> {\n  onStarted: (opts: { context: OperationContext | undefined }) => void;\n  onData: (value: inferAsyncIterableYield<TValue>) => void;\n  onError: (err: TError) => void;\n  onStopped: () => void;\n  onComplete: () => void;\n  onConnectionStateChange: (state: TRPCConnectionState<TError>) => void;\n}\n\n/** @internal */\nexport type CreateTRPCClientOptions<TRouter extends InferrableClientTypes> = {\n  links: TRPCLink<TRouter>[];\n  transformer?: TypeError<'The transformer property has moved to httpLink/httpBatchLink/wsLink'>;\n};\n\nexport class TRPCUntypedClient<TInferrable extends InferrableClientTypes> {\n  private readonly links: OperationLink<TInferrable>[];\n  public readonly runtime: TRPCClientRuntime;\n  private requestId: number;\n\n  constructor(opts: CreateTRPCClientOptions<TInferrable>) {\n    this.requestId = 0;\n\n    this.runtime = {};\n\n    // Initialize the links\n    this.links = opts.links.map((link) => link(this.runtime));\n  }\n\n  private $request<TInput = unknown, TOutput = unknown>(opts: {\n    type: TRPCType;\n    input: TInput;\n    path: string;\n    context?: OperationContext;\n    signal: Maybe<AbortSignal>;\n  }) {\n    const chain$ = createChain<AnyRouter, TInput, TOutput>({\n      links: this.links as OperationLink<any, any, any>[],\n      op: {\n        ...opts,\n        context: opts.context ?? {},\n        id: ++this.requestId,\n      },\n    });\n    return chain$.pipe(share());\n  }\n\n  private async requestAsPromise<TInput = unknown, TOutput = unknown>(opts: {\n    type: TRPCType;\n    input: TInput;\n    path: string;\n    context?: OperationContext;\n    signal: Maybe<AbortSignal>;\n  }): Promise<TOutput> {\n    try {\n      const req$ = this.$request<TInput, TOutput>(opts);\n      type TValue = inferObservableValue<typeof req$>;\n\n      const envelope = await observableToPromise<TValue>(req$);\n      const data = (envelope.result as any).data;\n      return data;\n    } catch (err) {\n      throw TRPCClientError.from(err as Error);\n    }\n  }\n  public query(path: string, input?: unknown, opts?: TRPCRequestOptions) {\n    return this.requestAsPromise<unknown, unknown>({\n      type: 'query',\n      path,\n      input,\n      context: opts?.context,\n      signal: opts?.signal,\n    });\n  }\n  public mutation(path: string, input?: unknown, opts?: TRPCRequestOptions) {\n    return this.requestAsPromise<unknown, unknown>({\n      type: 'mutation',\n      path,\n      input,\n      context: opts?.context,\n      signal: opts?.signal,\n    });\n  }\n  public subscription(\n    path: string,\n    input: unknown,\n    opts: Partial<\n      TRPCSubscriptionObserver<unknown, TRPCClientError<AnyRouter>>\n    > &\n      TRPCRequestOptions,\n  ): Unsubscribable {\n    const observable$ = this.$request({\n      type: 'subscription',\n      path,\n      input,\n      context: opts.context,\n      signal: opts.signal,\n    });\n    return observable$.subscribe({\n      next(envelope) {\n        switch (envelope.result.type) {\n          case 'state': {\n            opts.onConnectionStateChange?.(envelope.result);\n            break;\n          }\n          case 'started': {\n            opts.onStarted?.({\n              context: envelope.context,\n            });\n            break;\n          }\n          case 'stopped': {\n            opts.onStopped?.();\n            break;\n          }\n          case 'data':\n          case undefined: {\n            opts.onData?.(envelope.result.data);\n            break;\n          }\n        }\n      },\n      error(err) {\n        opts.onError?.(err);\n      },\n      complete() {\n        opts.onComplete?.();\n      },\n    });\n  }\n}\n", "import type { AnyRouter } from '@trpc/server/unstable-core-do-not-import';\nimport type { CreateTRPCClientOptions } from './internals/TRPCUntypedClient';\nimport { TRPCUntypedClient } from './internals/TRPCUntypedClient';\n\nexport function createTRPCUntypedClient<TRouter extends AnyRouter>(\n  opts: CreateTRPCClientOptions<TRouter>,\n): TRPCUntypedClient<TRouter> {\n  return new TRPCUntypedClient(opts);\n}\n\nexport type {\n  CreateTRPCClientOptions,\n  TRPCRequestOptions,\n} from './internals/TRPCUntypedClient';\nexport { TRPCUntypedClient } from './internals/TRPCUntypedClient';\n", "/* eslint-disable @typescript-eslint/no-non-null-assertion */\nimport type { Unsubscribable } from '@trpc/server/observable';\nimport type {\n  AnyProcedure,\n  AnyRouter,\n  inferClientTypes,\n  inferProcedureInput,\n  InferrableClientTypes,\n  inferTransformedProcedureOutput,\n  ProcedureType,\n  RouterRecord,\n} from '@trpc/server/unstable-core-do-not-import';\nimport {\n  createFlatProxy,\n  createRecursiveProxy,\n} from '@trpc/server/unstable-core-do-not-import';\nimport type { CreateTRPCClientOptions } from './createTRPCUntypedClient';\nimport type { TRPCSubscriptionObserver } from './internals/TRPCUntypedClient';\nimport { TRPCUntypedClient } from './internals/TRPCUntypedClient';\nimport type { TRPCProcedureOptions } from './internals/types';\nimport type { TRPCClientError } from './TRPCClientError';\n\n/**\n * @public\n * @deprecated use {@link TRPCClient} instead, will be removed in v12\n **/\nexport type inferRouterClient<TRouter extends AnyRouter> = TRPCClient<TRouter>;\n\n/**\n * @public\n * @deprecated use {@link TRPCClient} instead, will be removed in v12\n **/\nexport type CreateTRPCClient<TRouter extends AnyRouter> = TRPCClient<TRouter>;\n\nconst untypedClientSymbol = Symbol.for('trpc_untypedClient');\n\n/**\n * @public\n **/\nexport type TRPCClient<TRouter extends AnyRouter> = DecoratedProcedureRecord<\n  {\n    transformer: TRouter['_def']['_config']['$types']['transformer'];\n    errorShape: TRouter['_def']['_config']['$types']['errorShape'];\n  },\n  TRouter['_def']['record']\n> & {\n  [untypedClientSymbol]: TRPCUntypedClient<TRouter>;\n};\n\ntype ResolverDef = {\n  input: any;\n  output: any;\n  transformer: boolean;\n  errorShape: any;\n};\n\ntype coerceAsyncGeneratorToIterable<T> =\n  T extends AsyncGenerator<infer $T, infer $Return, infer $Next>\n    ? AsyncIterable<$T, $Return, $Next>\n    : T;\n\n/** @internal */\nexport type Resolver<TDef extends ResolverDef> = (\n  input: TDef['input'],\n  opts?: TRPCProcedureOptions,\n) => Promise<coerceAsyncGeneratorToIterable<TDef['output']>>;\n\ntype SubscriptionResolver<TDef extends ResolverDef> = (\n  input: TDef['input'],\n  opts: Partial<\n    TRPCSubscriptionObserver<TDef['output'], TRPCClientError<TDef>>\n  > &\n    TRPCProcedureOptions,\n) => Unsubscribable;\n\ntype DecorateProcedure<\n  TType extends ProcedureType,\n  TDef extends ResolverDef,\n> = TType extends 'query'\n  ? {\n      query: Resolver<TDef>;\n    }\n  : TType extends 'mutation'\n    ? {\n        mutate: Resolver<TDef>;\n      }\n    : TType extends 'subscription'\n      ? {\n          subscribe: SubscriptionResolver<TDef>;\n        }\n      : never;\n\n/**\n * @internal\n */\ntype DecoratedProcedureRecord<\n  TRoot extends InferrableClientTypes,\n  TRecord extends RouterRecord,\n> = {\n  [TKey in keyof TRecord]: TRecord[TKey] extends infer $Value\n    ? $Value extends AnyProcedure\n      ? DecorateProcedure<\n          $Value['_def']['type'],\n          {\n            input: inferProcedureInput<$Value>;\n            output: inferTransformedProcedureOutput<\n              inferClientTypes<TRoot>,\n              $Value\n            >;\n            errorShape: inferClientTypes<TRoot>['errorShape'];\n            transformer: inferClientTypes<TRoot>['transformer'];\n          }\n        >\n      : $Value extends RouterRecord\n        ? DecoratedProcedureRecord<TRoot, $Value>\n        : never\n    : never;\n};\n\nconst clientCallTypeMap: Record<\n  keyof DecorateProcedure<any, any>,\n  ProcedureType\n> = {\n  query: 'query',\n  mutate: 'mutation',\n  subscribe: 'subscription',\n};\n\n/** @internal */\nexport const clientCallTypeToProcedureType = (\n  clientCallType: string,\n): ProcedureType => {\n  return clientCallTypeMap[clientCallType as keyof typeof clientCallTypeMap];\n};\n\n/**\n * @internal\n */\nexport function createTRPCClientProxy<TRouter extends AnyRouter>(\n  client: TRPCUntypedClient<TRouter>,\n): TRPCClient<TRouter> {\n  const proxy = createRecursiveProxy<TRPCClient<TRouter>>(({ path, args }) => {\n    const pathCopy = [...path];\n    const procedureType = clientCallTypeToProcedureType(pathCopy.pop()!);\n\n    const fullPath = pathCopy.join('.');\n\n    return (client[procedureType] as any)(fullPath, ...(args as any));\n  });\n  return createFlatProxy<TRPCClient<TRouter>>((key) => {\n    if (key === untypedClientSymbol) {\n      return client;\n    }\n    return proxy[key];\n  });\n}\n\nexport function createTRPCClient<TRouter extends AnyRouter>(\n  opts: CreateTRPCClientOptions<TRouter>,\n): TRPCClient<TRouter> {\n  const client = new TRPCUntypedClient(opts);\n  const proxy = createTRPCClientProxy<TRouter>(client);\n  return proxy;\n}\n\n/**\n * Get an untyped client from a proxy client\n * @internal\n */\nexport function getUntypedClient<TRouter extends AnyRouter>(\n  client: TRPCClient<TRouter>,\n): TRPCUntypedClient<TRouter> {\n  return client[untypedClientSymbol];\n}\n", "import type { AnyRouter, ProcedureType } from '@trpc/server';\nimport { observable } from '@trpc/server/observable';\nimport type { TRPCErrorShape, TRPCResponse } from '@trpc/server/rpc';\nimport { jsonlStreamConsumer } from '@trpc/server/unstable-core-do-not-import';\nimport type { BatchLoader } from '../internals/dataLoader';\nimport { dataLoader } from '../internals/dataLoader';\nimport { allAbortSignals, raceAbortSignals } from '../internals/signals';\nimport type { NonEmptyArray } from '../internals/types';\nimport { TRPCClientError } from '../TRPCClientError';\nimport type { HTTPBatchLinkOptions } from './HTTPBatchLinkOptions';\nimport type { HTTPResult } from './internals/httpUtils';\nimport {\n  fetchHTTPResponse,\n  getBody,\n  getUrl,\n  resolveHTTPLinkOptions,\n} from './internals/httpUtils';\nimport type { Operation, TRPCLink } from './types';\n\n/**\n * @see https://trpc.io/docs/client/links/httpBatchStreamLink\n */\nexport function httpBatchStreamLink<TRouter extends AnyRouter>(\n  opts: HTTPBatchLinkOptions<TRouter['_def']['_config']['$types']>,\n): TRPCLink<TRouter> {\n  const resolvedOpts = resolveHTTPLinkOptions(opts);\n  const maxURLLength = opts.maxURLLength ?? Infinity;\n  const maxItems = opts.maxItems ?? Infinity;\n\n  return () => {\n    const batchLoader = (\n      type: ProcedureType,\n    ): BatchLoader<Operation, HTTPResult> => {\n      return {\n        validate(batchOps) {\n          if (maxURLLength === Infinity && maxItems === Infinity) {\n            // escape hatch for quick calcs\n            return true;\n          }\n          if (batchOps.length > maxItems) {\n            return false;\n          }\n          const path = batchOps.map((op) => op.path).join(',');\n          const inputs = batchOps.map((op) => op.input);\n\n          const url = getUrl({\n            ...resolvedOpts,\n            type,\n            path,\n            inputs,\n            signal: null,\n          });\n\n          return url.length <= maxURLLength;\n        },\n        async fetch(batchOps) {\n          const path = batchOps.map((op) => op.path).join(',');\n          const inputs = batchOps.map((op) => op.input);\n\n          const batchSignals = allAbortSignals(\n            ...batchOps.map((op) => op.signal),\n          );\n          const abortController = new AbortController();\n\n          const responsePromise = fetchHTTPResponse({\n            ...resolvedOpts,\n            signal: raceAbortSignals(batchSignals, abortController.signal),\n            type,\n            contentTypeHeader: 'application/json',\n            trpcAcceptHeader: 'application/jsonl',\n            getUrl,\n            getBody,\n            inputs,\n            path,\n            headers() {\n              if (!opts.headers) {\n                return {};\n              }\n              if (typeof opts.headers === 'function') {\n                return opts.headers({\n                  opList: batchOps as NonEmptyArray<Operation>,\n                });\n              }\n              return opts.headers;\n            },\n          });\n\n          const res = await responsePromise;\n          const [head] = await jsonlStreamConsumer<\n            Record<string, Promise<any>>\n          >({\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            from: res.body!,\n            deserialize: resolvedOpts.transformer.output.deserialize,\n            // onError: console.error,\n            formatError(opts) {\n              const error = opts.error as TRPCErrorShape;\n              return TRPCClientError.from({\n                error,\n              });\n            },\n            abortController,\n          });\n          const promises = Object.keys(batchOps).map(\n            async (key): Promise<HTTPResult> => {\n              let json: TRPCResponse = await Promise.resolve(head[key]);\n\n              if ('result' in json) {\n                /**\n                 * Not very pretty, but we need to unwrap nested data as promises\n                 * Our stream producer will only resolve top-level async values or async values that are directly nested in another async value\n                 */\n                const result = await Promise.resolve(json.result);\n                json = {\n                  result: {\n                    data: await Promise.resolve(result.data),\n                  },\n                };\n              }\n\n              return {\n                json,\n                meta: {\n                  response: res,\n                },\n              };\n            },\n          );\n          return promises;\n        },\n      };\n    };\n\n    const query = dataLoader(batchLoader('query'));\n    const mutation = dataLoader(batchLoader('mutation'));\n\n    const loaders = { query, mutation };\n    return ({ op }) => {\n      return observable((observer) => {\n        /* istanbul ignore if -- @preserve */\n        if (op.type === 'subscription') {\n          throw new Error(\n            'Subscriptions are unsupported by `httpBatchStreamLink` - use `httpSubscriptionLink` or `wsLink`',\n          );\n        }\n        const loader = loaders[op.type];\n        const promise = loader.load(op);\n\n        let _res = undefined as HTTPResult | undefined;\n        promise\n          .then((res) => {\n            _res = res;\n            if ('error' in res.json) {\n              observer.error(\n                TRPCClientError.from(res.json, {\n                  meta: res.meta,\n                }),\n              );\n              return;\n            } else if ('result' in res.json) {\n              observer.next({\n                context: res.meta,\n                result: res.json.result,\n              });\n              observer.complete();\n              return;\n            }\n\n            observer.complete();\n          })\n          .catch((err) => {\n            observer.error(\n              TRPCClientError.from(err, {\n                meta: _res?.meta,\n              }),\n            );\n          });\n\n        return () => {\n          // noop\n        };\n      });\n    };\n  };\n}\n\n/**\n * @deprecated use {@link httpBatchStreamLink} instead\n */\nexport const unstable_httpBatchStreamLink = httpBatchStreamLink;\n", "export function inputWithTrackedEventId(\n  input: unknown,\n  lastEventId: string | undefined,\n) {\n  if (!lastEventId) {\n    return input;\n  }\n  if (input != null && typeof input !== 'object') {\n    return input;\n  }\n  return {\n    ...(input ?? {}),\n    lastEventId,\n  };\n}\n", "import { behaviorSubject, observable } from '@trpc/server/observable';\nimport type { TRPCErrorShape, TRPCResult } from '@trpc/server/rpc';\nimport type {\n  AnyClientTypes,\n  EventSourceLike,\n  inferClientTypes,\n  InferrableClientTypes,\n} from '@trpc/server/unstable-core-do-not-import';\nimport {\n  retryableRpcCodes,\n  run,\n  sseStreamConsumer,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { inputWithTrackedEventId } from '../internals/inputWithTrackedEventId';\nimport { raceAbortSignals } from '../internals/signals';\nimport { TRPCClientError } from '../TRPCClientError';\nimport type { TRPCConnectionState } from '../unstable-internals';\nimport { getTransformer, type TransformerOptions } from '../unstable-internals';\nimport { getUrl } from './internals/httpUtils';\nimport {\n  resultOf,\n  type UrlOptionsWithConnectionParams,\n} from './internals/urlWithConnectionParams';\nimport type { Operation, TRPCLink } from './types';\n\nasync function urlWithConnectionParams(\n  opts: UrlOptionsWithConnectionParams,\n): Promise<string> {\n  let url = await resultOf(opts.url);\n  if (opts.connectionParams) {\n    const params = await resultOf(opts.connectionParams);\n\n    const prefix = url.includes('?') ? '&' : '?';\n    url +=\n      prefix + 'connectionParams=' + encodeURIComponent(JSON.stringify(params));\n  }\n\n  return url;\n}\n\ntype HTTPSubscriptionLinkOptions<\n  TRoot extends AnyClientTypes,\n  TEventSource extends EventSourceLike.AnyConstructor = typeof EventSource,\n> = {\n  /**\n   * EventSource ponyfill\n   */\n  EventSource?: TEventSource;\n  /**\n   * EventSource options or a callback that returns them\n   */\n  eventSourceOptions?:\n    | EventSourceLike.InitDictOf<TEventSource>\n    | ((opts: {\n        op: Operation;\n      }) =>\n        | EventSourceLike.InitDictOf<TEventSource>\n        | Promise<EventSourceLike.InitDictOf<TEventSource>>);\n} & TransformerOptions<TRoot> &\n  UrlOptionsWithConnectionParams;\n\n/**\n * @see https://trpc.io/docs/client/links/httpSubscriptionLink\n */\nexport function httpSubscriptionLink<\n  TInferrable extends InferrableClientTypes,\n  TEventSource extends EventSourceLike.AnyConstructor,\n>(\n  opts: HTTPSubscriptionLinkOptions<\n    inferClientTypes<TInferrable>,\n    TEventSource\n  >,\n): TRPCLink<TInferrable> {\n  const transformer = getTransformer(opts.transformer);\n\n  return () => {\n    return ({ op }) => {\n      return observable((observer) => {\n        const { type, path, input } = op;\n\n        /* istanbul ignore if -- @preserve */\n        if (type !== 'subscription') {\n          throw new Error('httpSubscriptionLink only supports subscriptions');\n        }\n\n        let lastEventId: string | undefined = undefined;\n        const ac = new AbortController();\n        const signal = raceAbortSignals(op.signal, ac.signal);\n        const eventSourceStream = sseStreamConsumer<{\n          EventSource: TEventSource;\n          data: Partial<{\n            id?: string;\n            data: unknown;\n          }>;\n          error: TRPCErrorShape;\n        }>({\n          url: async () =>\n            getUrl({\n              transformer,\n              url: await urlWithConnectionParams(opts),\n              input: inputWithTrackedEventId(input, lastEventId),\n              path,\n              type,\n              signal: null,\n            }),\n          init: () => resultOf(opts.eventSourceOptions, { op }),\n          signal,\n          deserialize: transformer.output.deserialize,\n          EventSource:\n            opts.EventSource ??\n            (globalThis.EventSource as never as TEventSource),\n        });\n\n        const connectionState = behaviorSubject<\n          TRPCConnectionState<TRPCClientError<any>>\n        >({\n          type: 'state',\n          state: 'connecting',\n          error: null,\n        });\n\n        const connectionSub = connectionState.subscribe({\n          next(state) {\n            observer.next({\n              result: state,\n            });\n          },\n        });\n        run(async () => {\n          for await (const chunk of eventSourceStream) {\n            switch (chunk.type) {\n              case 'ping':\n                // do nothing\n                break;\n              case 'data':\n                const chunkData = chunk.data;\n\n                let result: TRPCResult<unknown>;\n                if (chunkData.id) {\n                  // if the `tracked()`-helper is used, we always have an `id` field\n                  lastEventId = chunkData.id;\n                  result = {\n                    id: chunkData.id,\n                    data: chunkData,\n                  };\n                } else {\n                  result = {\n                    data: chunkData.data,\n                  };\n                }\n\n                observer.next({\n                  result,\n                  context: {\n                    eventSource: chunk.eventSource,\n                  },\n                });\n                break;\n              case 'connected': {\n                observer.next({\n                  result: {\n                    type: 'started',\n                  },\n                  context: {\n                    eventSource: chunk.eventSource,\n                  },\n                });\n                connectionState.next({\n                  type: 'state',\n                  state: 'pending',\n                  error: null,\n                });\n                break;\n              }\n              case 'serialized-error': {\n                const error = TRPCClientError.from({ error: chunk.error });\n\n                if (retryableRpcCodes.includes(chunk.error.code)) {\n                  //\n                  connectionState.next({\n                    type: 'state',\n                    state: 'connecting',\n                    error,\n                  });\n                  break;\n                }\n                //\n                // non-retryable error, cancel the subscription\n                throw error;\n              }\n              case 'connecting': {\n                const lastState = connectionState.get();\n\n                const error = chunk.event && TRPCClientError.from(chunk.event);\n                if (!error && lastState.state === 'connecting') {\n                  break;\n                }\n\n                connectionState.next({\n                  type: 'state',\n                  state: 'connecting',\n                  error,\n                });\n                break;\n              }\n              case 'timeout': {\n                connectionState.next({\n                  type: 'state',\n                  state: 'connecting',\n                  error: new TRPCClientError(\n                    `Timeout of ${chunk.ms}ms reached while waiting for a response`,\n                  ),\n                });\n              }\n            }\n          }\n          observer.next({\n            result: {\n              type: 'stopped',\n            },\n          });\n          connectionState.next({\n            type: 'state',\n            state: 'idle',\n            error: null,\n          });\n          observer.complete();\n        }).catch((error) => {\n          observer.error(TRPCClientError.from(error));\n        });\n\n        return () => {\n          observer.complete();\n          ac.abort();\n          connectionSub.unsubscribe();\n        };\n      });\n    };\n  };\n}\n\n/**\n * @deprecated use {@link httpSubscriptionLink} instead\n */\nexport const unstable_httpSubscriptionLink = httpSubscriptionLink;\n", "/* istanbul ignore file -- @preserve */\n// We're not actually exporting this link\nimport type { Unsubscribable } from '@trpc/server/observable';\nimport { observable } from '@trpc/server/observable';\nimport type { InferrableClientTypes } from '@trpc/server/unstable-core-do-not-import';\nimport { inputWithTrackedEventId } from '../internals/inputWithTrackedEventId';\nimport type { TRPCClientError } from '../TRPCClientError';\nimport type { Operation, TRPCLink } from './types';\n\ninterface RetryLinkOptions<TInferrable extends InferrableClientTypes> {\n  /**\n   * The retry function\n   */\n  retry: (opts: RetryFnOptions<TInferrable>) => boolean;\n  /**\n   * The delay between retries in ms (defaults to 0)\n   */\n  retryDelayMs?: (attempt: number) => number;\n}\n\ninterface RetryFnOptions<TInferrable extends InferrableClientTypes> {\n  /**\n   * The operation that failed\n   */\n  op: Operation;\n  /**\n   * The error that occurred\n   */\n  error: TRPCClientError<TInferrable>;\n  /**\n   * The number of attempts that have been made (including the first call)\n   */\n  attempts: number;\n}\n\n/**\n * @see https://trpc.io/docs/v11/client/links/retryLink\n */\nexport function retryLink<TInferrable extends InferrableClientTypes>(\n  opts: RetryLinkOptions<TInferrable>,\n): TRPCLink<TInferrable> {\n  // initialized config\n  return () => {\n    // initialized in app\n    return (callOpts) => {\n      // initialized for request\n      return observable((observer) => {\n        let next$: Unsubscribable;\n        let callNextTimeout: ReturnType<typeof setTimeout> | undefined =\n          undefined;\n\n        let lastEventId: string | undefined = undefined;\n\n        attempt(1);\n\n        function opWithLastEventId() {\n          const op = callOpts.op;\n          if (!lastEventId) {\n            return op;\n          }\n\n          return {\n            ...op,\n            input: inputWithTrackedEventId(op.input, lastEventId),\n          };\n        }\n\n        function attempt(attempts: number) {\n          const op = opWithLastEventId();\n\n          next$ = callOpts.next(op).subscribe({\n            error(error) {\n              const shouldRetry = opts.retry({\n                op,\n                attempts,\n                error,\n              });\n              if (!shouldRetry) {\n                observer.error(error);\n                return;\n              }\n              const delayMs = opts.retryDelayMs?.(attempts) ?? 0;\n\n              if (delayMs <= 0) {\n                attempt(attempts + 1);\n                return;\n              }\n              callNextTimeout = setTimeout(\n                () => attempt(attempts + 1),\n                delayMs,\n              );\n            },\n            next(envelope) {\n              //\n              if (\n                (!envelope.result.type || envelope.result.type === 'data') &&\n                envelope.result.id\n              ) {\n                //\n                lastEventId = envelope.result.id;\n              }\n\n              observer.next(envelope);\n            },\n            complete() {\n              observer.complete();\n            },\n          });\n        }\n        return () => {\n          next$.unsubscribe();\n          clearTimeout(callNextTimeout);\n        };\n      });\n    };\n  };\n}\n", "function _usingCtx() {\n  var r = \"function\" == typeof SuppressedError ? SuppressedError : function (r, e) {\n      var n = Error();\n      return n.name = \"SuppressedError\", n.error = r, n.suppressed = e, n;\n    },\n    e = {},\n    n = [];\n  function using(r, e) {\n    if (null != e) {\n      if (Object(e) !== e) throw new TypeError(\"using declarations can only be used with objects, functions, null, or undefined.\");\n      if (r) var o = e[Symbol.asyncDispose || Symbol[\"for\"](\"Symbol.asyncDispose\")];\n      if (void 0 === o && (o = e[Symbol.dispose || Symbol[\"for\"](\"Symbol.dispose\")], r)) var t = o;\n      if (\"function\" != typeof o) throw new TypeError(\"Object is not disposable.\");\n      t && (o = function o() {\n        try {\n          t.call(e);\n        } catch (r) {\n          return Promise.reject(r);\n        }\n      }), n.push({\n        v: e,\n        d: o,\n        a: r\n      });\n    } else r && n.push({\n      d: e,\n      a: r\n    });\n    return e;\n  }\n  return {\n    e: e,\n    u: using.bind(null, !1),\n    a: using.bind(null, !0),\n    d: function d() {\n      var o,\n        t = this.e,\n        s = 0;\n      function next() {\n        for (; o = n.pop();) try {\n          if (!o.a && 1 === s) return s = 0, n.push(o), Promise.resolve().then(next);\n          if (o.d) {\n            var r = o.d.call(o.v);\n            if (o.a) return s |= 2, Promise.resolve(r).then(next, err);\n          } else s |= 1;\n        } catch (r) {\n          return err(r);\n        }\n        if (1 === s) return t !== e ? Promise.reject(t) : Promise.resolve();\n        if (t !== e) throw t;\n      }\n      function err(n) {\n        return t = t !== e ? new r(n, t) : n, next();\n      }\n      return next();\n    }\n  };\n}\nmodule.exports = _usingCtx, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import {\n  getTRPCErrorFromUnknown,\n  getTRPCErrorShape,\n  isTrackedEnvelope,\n} from '@trpc/server';\nimport { behaviorSubject, observable } from '@trpc/server/observable';\nimport { TRPC_ERROR_CODES_BY_KEY, type TRPCResult } from '@trpc/server/rpc';\nimport {\n  callProcedure,\n  isAbortError,\n  isAsyncIterable,\n  iteratorResource,\n  makeResource,\n  retryableRpcCodes,\n  run,\n  type AnyRouter,\n  type ErrorHandlerOptions,\n  type inferClientTypes,\n  type inferRouterContext,\n} from '@trpc/server/unstable-core-do-not-import';\nimport { inputWithTrackedEventId } from '../internals/inputWithTrackedEventId';\nimport { abortSignalToPromise, raceAbortSignals } from '../internals/signals';\nimport { getTransformer } from '../internals/transformer';\nimport type { TransformerOptions } from '../internals/transformer';\nimport { isTR<PERSON><PERSON><PERSON>Error, TRPCClientError } from '../TRPCClientError';\nimport type { TRPCConnectionState } from './internals/subscriptions';\nimport type { TRPCLink } from './types';\n\nexport type LocalLinkOptions<TRouter extends AnyRouter> = {\n  router: TRouter;\n  createContext: () => Promise<inferRouterContext<TRouter>>;\n  onError?: (opts: ErrorHandlerOptions<inferRouterContext<TRouter>>) => void;\n} & TransformerOptions<inferClientTypes<TRouter>>;\n\n/**\n * localLink is a terminating link that allows you to make tRPC procedure calls directly in your application without going through HTTP.\n *\n * @see https://trpc.io/docs/links/localLink\n */\nexport function unstable_localLink<TRouter extends AnyRouter>(\n  opts: LocalLinkOptions<TRouter>,\n): TRPCLink<TRouter> {\n  const transformer = getTransformer(opts.transformer);\n\n  const transformChunk = (chunk: unknown) => {\n    if (opts.transformer) {\n      // assume transformer will do the right thing\n      return chunk;\n    }\n    // Special case for undefined, because `JSON.stringify(undefined)` throws\n    if (chunk === undefined) {\n      return chunk;\n    }\n    const serialized = JSON.stringify(transformer.input.serialize(chunk));\n    const deserialized = JSON.parse(transformer.output.deserialize(serialized));\n    return deserialized;\n  };\n\n  return () =>\n    ({ op }) =>\n      observable((observer) => {\n        let ctx: inferRouterContext<TRouter> | undefined = undefined;\n        const ac = new AbortController();\n\n        const signal = raceAbortSignals(op.signal, ac.signal);\n        const signalPromise = abortSignalToPromise(signal);\n\n        signalPromise.catch(() => {\n          // prevent unhandled rejection\n        });\n\n        let input = op.input;\n        async function runProcedure(newInput: unknown): Promise<unknown> {\n          input = newInput;\n\n          ctx = await opts.createContext();\n\n          return callProcedure({\n            router: opts.router,\n            path: op.path,\n            getRawInput: async () => newInput,\n            ctx,\n            type: op.type,\n            signal,\n          });\n        }\n\n        function onErrorCallback(cause: unknown) {\n          if (isAbortError(cause)) {\n            return;\n          }\n          opts.onError?.({\n            error: getTRPCErrorFromUnknown(cause),\n            type: op.type,\n            path: op.path,\n            input,\n            ctx,\n          });\n        }\n\n        function coerceToTRPCClientError(cause: unknown) {\n          if (isTRPCClientError<TRouter>(cause)) {\n            return cause;\n          }\n          const error = getTRPCErrorFromUnknown(cause);\n\n          const shape = getTRPCErrorShape({\n            config: opts.router._def._config,\n            ctx,\n            error,\n            input,\n            path: op.path,\n            type: op.type,\n          });\n          return TRPCClientError.from({\n            error: transformChunk(shape),\n          });\n        }\n\n        run(async () => {\n          switch (op.type) {\n            case 'query':\n            case 'mutation': {\n              const result = await runProcedure(op.input);\n              if (!isAsyncIterable(result)) {\n                observer.next({\n                  result: { data: transformChunk(result) },\n                });\n                observer.complete();\n                break;\n              }\n\n              observer.next({\n                result: {\n                  data: (async function* () {\n                    await using iterator = iteratorResource(result);\n                    using _finally = makeResource({}, () => {\n                      observer.complete();\n                    });\n                    try {\n                      while (true) {\n                        const res = await Promise.race([\n                          iterator.next(),\n                          signalPromise,\n                        ]);\n                        if (res.done) {\n                          return transformChunk(res.value);\n                        }\n                        yield transformChunk(res.value);\n                      }\n                    } catch (cause) {\n                      onErrorCallback(cause);\n                      throw coerceToTRPCClientError(cause);\n                    }\n                  })(),\n                },\n              });\n              break;\n            }\n            case 'subscription': {\n              const connectionState = behaviorSubject<\n                TRPCConnectionState<TRPCClientError<any>>\n              >({\n                type: 'state',\n                state: 'connecting',\n                error: null,\n              });\n\n              const connectionSub = connectionState.subscribe({\n                next(state) {\n                  observer.next({\n                    result: state,\n                  });\n                },\n              });\n              let lastEventId: string | undefined = undefined;\n\n              using _finally = makeResource({}, async () => {\n                observer.complete();\n\n                connectionState.next({\n                  type: 'state',\n                  state: 'idle',\n                  error: null,\n                });\n                connectionSub.unsubscribe();\n              });\n              while (true) {\n                const result = await runProcedure(\n                  inputWithTrackedEventId(op.input, lastEventId),\n                );\n                if (!isAsyncIterable(result)) {\n                  throw new Error('Expected an async iterable');\n                }\n                await using iterator = iteratorResource(result);\n\n                observer.next({\n                  result: {\n                    type: 'started',\n                  },\n                });\n                connectionState.next({\n                  type: 'state',\n                  state: 'pending',\n                  error: null,\n                });\n\n                // Use a while loop to handle errors and reconnects\n                while (true) {\n                  let res;\n                  try {\n                    res = await Promise.race([iterator.next(), signalPromise]);\n                  } catch (cause) {\n                    if (isAbortError(cause)) {\n                      return;\n                    }\n                    const error = getTRPCErrorFromUnknown(cause);\n\n                    if (\n                      !retryableRpcCodes.includes(\n                        TRPC_ERROR_CODES_BY_KEY[error.code],\n                      )\n                    ) {\n                      throw coerceToTRPCClientError(error);\n                    }\n\n                    onErrorCallback(error);\n                    connectionState.next({\n                      type: 'state',\n                      state: 'connecting',\n                      error: coerceToTRPCClientError(error),\n                    });\n\n                    break;\n                  }\n\n                  if (res.done) {\n                    return;\n                  }\n                  let chunk: TRPCResult<unknown>;\n                  if (isTrackedEnvelope(res.value)) {\n                    lastEventId = res.value[0];\n\n                    chunk = {\n                      id: res.value[0],\n                      data: {\n                        id: res.value[0],\n                        data: res.value[1],\n                      },\n                    };\n                  } else {\n                    chunk = {\n                      data: res.value,\n                    };\n                  }\n\n                  observer.next({\n                    result: {\n                      ...chunk,\n                      data: transformChunk(chunk.data),\n                    },\n                  });\n                }\n              }\n              break;\n            }\n          }\n        }).catch((cause) => {\n          onErrorCallback(cause);\n          observer.error(coerceToTRPCClientError(cause));\n        });\n\n        return () => {\n          ac.abort();\n        };\n      });\n}\n/**\n * @deprecated Renamed to `unstable_localLink`. This alias will be removed in a future major release.\n */\nexport const experimental_localLink: typeof unstable_localLink =\n  unstable_localLink;\n"], "mappings": ";;;;;AAoBA,SAAgB,WACdA,WAC4B;AAC5B,QAAMC,OAAmC;IACvC,UAAU,UAAU;AAClB,UAAIC,cAAoC;AACxC,UAAI,SAAS;AACb,UAAI,eAAe;AACnB,UAAI,sBAAsB;AAC1B,eAAS,cAAc;AACrB,YAAI,gBAAgB,MAAM;AACxB,gCAAsB;AACtB;QACD;AACD,YAAI,aACF;AAEF,uBAAe;AAEf,YAAA,OAAW,gBAAgB,WACzB,aAAA;iBACS,YACT,aAAY,YAAA;MAEf;AACD,oBAAc,UAAU;QACtB,KAAK,OAAO;;AACV,cAAI,OACF;AAEF,WAAAC,MAAA,SAAS,SAAT,gBAAAA,IAAA,eAAgB;QACjB;QACD,MAAM,KAAK;;AACT,cAAI,OACF;AAEF,mBAAS;AACT,WAAAA,MAAA,SAAS,UAAT,gBAAAA,IAAA,eAAiB;AACjB,sBAAA;QACD;QACD,WAAW;;AACT,cAAI,OACF;AAEF,mBAAS;AACT,WAAAA,MAAA,SAAS,aAAT,gBAAAA,IAAA;AACA,sBAAA;QACD;MACF,CAAA;AACD,UAAI,oBACF,aAAA;AAEF,aAAO,EACL,YACD;IACF;IACD,QACK,YACmB;AACtB,aAAO,WAAW,OAAO,aAAa,IAAA;IACvC;EACF;AACD,SAAO;AACR;AAED,SAAS,YAAYC,MAAWC,IAA6B;AAC3D,SAAO,GAAG,IAAA;AACX;AAGD,SAAgB,oBACdC,cACA;AACA,QAAM,KAAK,IAAI,gBAAA;AACf,QAAM,UAAU,IAAI,QAAgB,CAAC,SAAS,WAAW;AACvD,QAAI,SAAS;AACb,aAAS,SAAS;AAChB,UAAI,OACF;AAEF,eAAS;AACT,WAAK,YAAA;IACN;AACD,OAAG,OAAO,iBAAiB,SAAS,MAAM;AACxC,aAAO,GAAG,OAAO,MAAA;IAClB,CAAA;AACD,UAAM,OAAO,aAAW,UAAU;MAChC,KAAK,MAAM;AACT,iBAAS;AACT,gBAAQ,IAAA;AACR,eAAA;MACD;MACD,MAAM,MAAM;AACV,eAAO,IAAA;MACR;MACD,WAAW;AACT,WAAG,MAAA;AACH,eAAA;MACD;IACF,CAAA;EACF,CAAA;AACD,SAAO;AACR;;;AC3FD,SAAgB,MACdC,OAC0C;AAC1C,SAAO,CAAC,WAAW;AACjB,QAAI,WAAW;AAEf,QAAIC,eAAsC;AAC1C,UAAMC,YAAiD,CAAE;AAEzD,aAAS,gBAAgB;AACvB,UAAI,aACF;AAEF,qBAAe,OAAO,UAAU;QAC9B,KAAK,OAAO;;AACV,qBAAW,YAAY,UACrB,EAAAC,MAAA,SAAS,SAAT,gBAAAA,IAAA,eAAgB;QAEnB;QACD,MAAM,OAAO;;AACX,qBAAW,YAAY,UACrB,EAAAA,MAAA,SAAS,UAAT,gBAAAA,IAAA,eAAiB;QAEpB;QACD,WAAW;;AACT,qBAAW,YAAY,UACrB,EAAAA,MAAA,SAAS,aAAT,gBAAAA,IAAA;QAEH;MACF,CAAA;IACF;AACD,aAAS,gBAAgB;AAEvB,UAAI,aAAa,KAAK,cAAc;AAClC,cAAM,OAAO;AACb,uBAAe;AACf,aAAK,YAAA;MACN;IACF;AAED,WAAO,WAAW,CAAC,eAAe;AAChC;AAEA,gBAAU,KAAK,UAAA;AACf,oBAAA;AACA,aAAO,EACL,cAAc;AACZ;AACA,sBAAA;AAEA,cAAM,QAAQ,UAAU,UAAU,CAAC,MAAM,MAAM,UAAA;AAE/C,YAAI,QAAQ,GACV,WAAU,OAAO,OAAO,CAAA;MAE3B,EACF;IACF,CAAA;EACF;AACF;AAED,SAAgB,IACdC,UAC0C;AAC1C,SAAO,CAAC,WAAW;AACjB,WAAO,WAAW,CAAC,gBAAgB;AACjC,aAAO,OAAO,UAAU;QACtB,KAAK,OAAO;;AACV,WAAAD,MAAA,SAAS,SAAT,gBAAAA,IAAA,eAAgB;AAChB,sBAAY,KAAK,KAAA;QAClB;QACD,MAAM,OAAO;;AACX,WAAAA,MAAA,SAAS,UAAT,gBAAAA,IAAA,eAAiB;AACjB,sBAAY,MAAM,KAAA;QACnB;QACD,WAAW;;AACT,WAAAA,MAAA,SAAS,aAAT,gBAAAA,IAAA;AACA,sBAAY,SAAA;QACb;MACF,CAAA;IACF,CAAA;EACF;AACF;AAED,IAAM,sBAAsB,OAAA;AClG5B,SAAgB,gBACdE,cACyB;AACzB,MAAIC,QAAgB;AAEpB,QAAMC,eAA0C,CAAE;AAElD,QAAM,cAAc,CAACC,aAAsC;AACzD,QAAI,UAAA,OACF,UAAS,KAAK,KAAA;AAEhB,iBAAa,KAAK,QAAA;EACnB;AACD,QAAM,iBAAiB,CAACA,aAAsC;AAC5D,iBAAa,OAAO,aAAa,QAAQ,QAAA,GAAW,CAAA;EACrD;AAED,QAAM,MAAM,WAA0B,CAAC,aAAa;AAClD,gBAAY,QAAA;AACZ,WAAO,MAAM;AACX,qBAAe,QAAA;IAChB;EACF,CAAA;AAED,MAAI,OAAO,CAACC,cAAsB;AAChC,QAAI,UAAU,UACZ;AAEF,YAAQ;AACR,eAAW,YAAY,aACrB,UAAS,KAAK,SAAA;EAEjB;AAED,MAAI,MAAM,MAAM;AAEhB,SAAO;AACR;;;AC7CD,SAAgB,YAIdC,MAG8C;AAC9C,SAAO,WAAW,CAAC,aAAa;AAC9B,aAAS,QAAQ,QAAQ,GAAG,KAAK,KAAK,IAAI;AACxC,YAAM,OAAO,KAAK,MAAM,KAAA;AACxB,UAAA,CAAK,KACH,OAAM,IAAI,MACR,kEAAA;AAGJ,YAAM,eAAe,KAAK;QACxB;QACA,KAAK,QAAQ;AACX,gBAAM,eAAe,QAAQ,QAAQ,GAAG,MAAA;AAExC,iBAAO;QACR;MACF,CAAA;AACD,aAAO;IACR;AAED,UAAM,OAAO,QAAA;AACb,WAAO,KAAK,UAAU,QAAA;EACvB,CAAA;AACF;AClCD,SAAS,QAAeC,OAAwB;AAC9C,SAAO,MAAM,QAAQ,KAAA,IAAS,QAAQ,CAAC,KAAM;AAC9C;AACD,SAAgB,UAAiDC,MAU3C;AACpB,SAAO,CAAC,YAAY;AAClB,UAAM,MAAM,QAAQ,KAAK,IAAA,EAAM,IAAI,CAAC,SAAS,KAAK,OAAA,CAAQ;AAC1D,UAAM,KAAK,QAAQ,KAAK,KAAA,EAAO,IAAI,CAAC,SAAS,KAAK,OAAA,CAAQ;AAC1D,WAAO,CAAC,UAAU;AAChB,aAAO,WAAW,CAAC,aAAa;AAC9B,cAAM,QAAQ,KAAK,UAAU,MAAM,EAAA,IAAM,MAAM;AAC/C,eAAO,YAAY;UAAE,IAAI,MAAM;UAAI;QAAO,CAAA,EAAE,UAAU,QAAA;MACvD,CAAA;IACF;EACF;AACF;;;ACnBD,IAAa,0BAA0B;EAKrC,aAAa;EAIb,aAAa;EAGb,uBAAuB;EACvB,iBAAiB;EACjB,aAAa;EACb,qBAAqB;EACrB,iBAAiB;EAGjB,cAAc;EACd,kBAAkB;EAClB,WAAW;EACX,WAAW;EACX,sBAAsB;EACtB,SAAS;EACT,UAAU;EACV,qBAAqB;EACrB,mBAAmB;EACnB,wBAAwB;EACxB,uBAAuB;EACvB,mBAAmB;EACnB,uBAAuB;AACxB;AA+BD,IAAaC,oBAA8C;EACzD,wBAAwB;EACxB,wBAAwB;EACxB,wBAAwB;EACxB,wBAAwB;AACzB;ACrED,SAAgB,sBACdC,SACG,MACI;AACP,QAAMC,SAAgB,OAAO,OAAO,uBAAO,OAAO,IAAA,GAAO,IAAA;AAEzD,aAAW,aAAa,KACtB,YAAW,OAAO,WAAW;AAC3B,QAAI,OAAO,UAAU,OAAO,GAAA,MAAS,UAAU,GAAA,EAC7C,OAAM,IAAI,MAAA,iBAAuB,GAAA,EAAI;AAEvC,WAAO,GAAA,IAAsB,UAAU,GAAA;EACxC;AAEH,SAAO;AACR;AAMD,SAAgB,SAASC,OAAkD;AACzE,SAAA,CAAA,CAAS,SAAA,CAAU,MAAM,QAAQ,KAAA,KAAM,OAAW,UAAU;AAC7D;AAGD,SAAgB,WAAWC,IAA0B;AACnD,SAAA,OAAc,OAAO;AACtB;AAMD,SAAgB,cACdC,KACM;AACN,SAAO,OAAO,OAAO,uBAAO,OAAO,IAAA,GAAO,GAAA;AAC3C;AAED,IAAM,0BAAA,OACG,WAAW,cAAA,CAAA,CAAgB,OAAO;AAE3C,SAAgB,gBACdF,OACgC;AAChC,SACE,2BAA2B,SAAS,KAAA,KAAU,OAAO,iBAAiB;AAEzE;AAKD,IAAa,MAAM,CAASG,OAA6B,GAAA;AAwBzD,SAAgB,MAAM,KAAK,GAAkB;AAC3C,SAAO,IAAI,QAAc,CAAC,QAAQ,WAAW,KAAK,EAAA,CAAG;AACtD;;;ACnFD,IAAM,OAAO,MAAM;AAElB;AAED,IAAM,oBAAoB,CAACC,QAAgB;AACzC,MAAI,OAAO,OACT,QAAO,OAAO,GAAA;AAEjB;AAED,SAAS,iBACPC,UACAC,MACAC,MACA;AACA,QAAM,WAAW,KAAK,KAAK,GAAA;AAE3B,sCAAmB,IAAI,MAAM,MAAM;IACjC,IAAI,MAAM,KAAK;AACb,UAAA,OAAW,QAAQ,YAAY,QAAQ,OAGrC,QAAA;AAEF,aAAO,iBAAiB,UAAU,CAAC,GAAG,MAAM,GAAI,GAAE,IAAA;IACnD;IACD,MAAM,IAAI,IAAI,MAAM;AAClB,YAAM,aAAa,KAAK,KAAK,SAAS,CAAA;AAEtC,UAAI,OAAO;QAAE;QAAM;MAAM;AAEzB,UAAI,eAAe,OACjB,QAAO;QACL,MAAM,KAAK,UAAU,IAAI,CAAC,KAAK,CAAA,CAAG,IAAG,CAAE;QACvC,MAAM,KAAK,MAAM,GAAG,EAAA;MACrB;eACQ,eAAe,QACxB,QAAO;QACL,MAAM,KAAK,UAAU,IAAI,KAAK,CAAA,IAAK,CAAE;QACrC,MAAM,KAAK,MAAM,GAAG,EAAA;MACrB;AAEH,wBAAkB,KAAK,IAAA;AACvB,wBAAkB,KAAK,IAAA;AACvB,aAAO,SAAS,IAAA;IACjB;EACF,CAAA;AAED,SAAO,KAAK,QAAA;AACb;AAOD,IAAa,uBAAuB,CAClCF,aACU,iBAAiB,UAAU,CAAE,GAAE,uBAAO,OAAO,IAAA,CAAK;AAO9D,IAAa,kBAAkB,CAC7BG,aACU;AACV,SAAO,IAAI,MAAM,MAAM,EACrB,IAAI,MAAM,MAAM;AACd,QAAI,SAAS,OAGX,QAAA;AAEF,WAAO,SAAS,IAAA;EACjB,EACF,CAAA;AACF;AC9ED,IAAaC,wBAGT;EACF,aAAa;EACb,aAAa;EACb,cAAc;EACd,kBAAkB;EAClB,WAAW;EACX,WAAW;EACX,sBAAsB;EACtB,SAAS;EACT,UAAU;EACV,qBAAqB;EACrB,mBAAmB;EACnB,wBAAwB;EACxB,uBAAuB;EACvB,mBAAmB;EACnB,uBAAuB;EACvB,uBAAuB;EACvB,iBAAiB;EACjB,aAAa;EACb,qBAAqB;EACrB,iBAAiB;AAClB;AA0BD,SAAgB,qBACdC,MACA;AACA,SAAO,sBAAsB,IAAA,KAAS;AACvC;AAiCD,SAAgB,2BAA2BC,OAAkB;AAC3D,SAAO,qBAAqB,MAAM,IAAA;AACnC;ACrFD,SAAgB,cAA0CC,MAOlC;AACtB,QAAM,EAAE,MAAM,OAAO,OAAA,IAAW;AAChC,QAAM,EAAE,KAAA,IAAS,KAAK;AACtB,QAAMC,QAA2B;IAC/B,SAAS,MAAM;IACf,MAAM,wBAAwB,IAAA;IAC9B,MAAM;MACJ;MACA,YAAY,2BAA2B,KAAA;IACxC;EACF;AACD,MAAI,OAAO,SAAA,OAAgB,KAAK,MAAM,UAAU,SAC9C,OAAM,KAAK,QAAQ,KAAK,MAAM;AAEhC,MAAA,OAAW,SAAS,SAClB,OAAM,KAAK,OAAO;AAEpB,SAAO,OAAO,eAAe;IAAE,GAAG;IAAM;EAAO,CAAA;AAChD;;;ACSD,IAAaC,mBAA6C,CAAC,EAAE,MAAA,MAAY;AACvE,SAAO;AACR;AC3CD,IAAM,oBAAN,cAAgC,MAAM;AAErC;AACD,SAAgB,oBAAoBC,OAAmC;AACrE,MAAI,iBAAiB,MACnB,QAAO;AAGT,QAAM,OAAA,OAAc;AACpB,MAAI,SAAS,eAAe,SAAS,cAAc,UAAU,KAC3D,QAAA;AAIF,MAAI,SAAS,SAEX,QAAO,IAAI,MAAM,OAAO,KAAA,CAAM;AAIhC,MAAI,SAAS,KAAA,EACX,QAAO,OAAO,OAAO,IAAI,kBAAA,GAAqB,KAAA;AAGhD,SAAA;AACD;AAED,SAAgB,wBAAwBA,OAA2B;AACjE,MAAI,iBAAiB,UACnB,QAAO;AAET,MAAI,iBAAiB,SAAS,MAAM,SAAS,YAE3C,QAAO;AAGT,QAAM,YAAY,IAAI,UAAU;IAC9B,MAAM;IACN;EACD,CAAA;AAGD,MAAI,iBAAiB,SAAS,MAAM,MAClC,WAAU,QAAQ,MAAM;AAG1B,SAAO;AACR;AAED,IAAa,YAAb,cAA+B,MAAM;EAMnC,YAAYC,MAIT;AACD,UAAM,QAAQ,oBAAoB,KAAK,KAAA;AACvC,UAAM,UAAU,KAAK,YAAW,+BAAO,YAAW,KAAK;AAIvD,UAAM,SAAS,EAAE,MAAO,CAAA;AAbD;AACT;AAcd,SAAK,OAAO,KAAK;AACjB,SAAK,OAAO;AACZ,SAAK,UAAL,KAAK,QAAU;EAChB;AACF;ACLD,SAAgB,mBACdC,aACyB;AACzB,MAAI,WAAW,YACb,QAAO;AAET,SAAO;IAAE,OAAO;IAAa,QAAQ;EAAa;AACnD;AAKD,IAAaC,qBAA8C;EACzD,OAAO;IAAE,WAAW,CAAC,QAAQ;IAAK,aAAa,CAAC,QAAQ;EAAK;EAC7D,QAAQ;IAAE,WAAW,CAAC,QAAQ;IAAK,aAAa,CAAC,QAAQ;EAAK;AAC/D;AA4CD,SAAS,qBACPC,UAGAC,aACA;AACA,MAAI,WAAW,UAAU;AACvB,UAAM,QAAQ,YAAY,YACxB,SAAS,KAAA;AAEX,WAAO;MACL,IAAI;MACJ,OAAO;QACL,GAAG;QACH;MACD;IACF;EACF;AAED,QAAM,SAAS;IACb,GAAG,SAAS;IACZ,IAAA,CAAM,SAAS,OAAO,QAAQ,SAAS,OAAO,SAAS,WAAW;MAChE,MAAM;MACN,MAAM,YAAY,YAAY,SAAS,OAAO,IAAA;IAC/C;EACF;AACD,SAAO;IAAE,IAAI;IAAM;EAAQ;AAC5B;AAED,IAAM,uBAAN,cAAmC,MAAM;EACvC,cAAc;AACZ,UAAM,0CAAA;EACP;AACF;AAMD,SAAgB,gBACdD,UAGAC,aACyC;AACzC,MAAIC;AACJ,MAAI;AAEF,aAAS,qBAAqB,UAAU,WAAA;EACzC,QAAO;AACN,UAAM,IAAI,qBAAA;EACX;AAGD,MAAA,CACG,OAAO,OAAA,CACN,SAAS,OAAO,MAAM,KAAA,KAAM,OACrB,OAAO,MAAM,MAAM,MAAA,MAAY,UAExC,OAAM,IAAI,qBAAA;AAEZ,MAAI,OAAO,MAAA,CAAO,SAAS,OAAO,MAAA,EAChC,OAAM,IAAI,qBAAA;AAEZ,SAAO;AACR;ACrHD,IAAM,aAAa,OAAO,MAAA;AAQ1B,SAAS,KAAQC,IAAsB;AACrC,QAAM,WAAW,OAAA;AACjB,MAAIC,SAA8B;AAClC,SAAO,MAAS;AACd,QAAI,WAAW,SACb,UAAS,GAAA;AAEX,WAAO;EACR;AACF;AAqCD,SAAS,OAAaC,OAAqC;AACzD,SAAA,OAAc,UAAU,cAAc,cAAc;AACrD;AAmDD,SAAS,SAASC,OAAoC;AACpD,SACE,SAAS,KAAA,KAAU,SAAS,MAAM,MAAA,CAAA,KAAY,YAAY,MAAM,MAAA;AAEnE;AAED,IAAM,cAAc;EAClB,MAAM;EACN,aAAa;EACb,OAAO;EACP,SAAS,CAAE;EACX,WAAW,CAAE;EACb,eAAe,CAAE;EACjB,gBAAgB;EAChB,aAAa;AACd;AAKD,IAAM,gBAAgB;EAKpB;EAIA;EACA;AACD;AA+BD,SAAgB,oBACdC,QACA;AACA,WAAS,kBACPC,OACyD;AACzD,UAAM,oBAAoB,IAAI,IAC5B,OAAO,KAAK,KAAA,EAAO,OAAO,CAAC,MAAM,cAAc,SAAS,CAAA,CAAE,CAAC;AAE7D,QAAI,kBAAkB,OAAO,EAC3B,OAAM,IAAI,MACR,+CACE,MAAM,KAAK,iBAAA,EAAmB,KAAK,IAAA,CAAK;AAI9C,UAAMC,aAA2C,cAAc,CAAE,CAAA;AACjE,UAAMC,SAA8C,cAAc,CAAE,CAAA;AAEpE,aAAS,iBAAiBC,MAKA;AACxB,aAAO;QACL,KAAK,KAAK;QACV,MAAM,KAAK,YAAY;AACrB,gBAAMC,WAAS,MAAM,KAAK,IAAA;AAC1B,gBAAM,WAAW,CAAC,GAAG,KAAK,MAAM,KAAK,GAAI;AACzC,gBAAM,UAAU,SAAS,KAAK,GAAA;AAE9B,eAAK,UAAU,KAAK,GAAA,IAAO,KAAKA,SAAO,KAAK,QAAQ,QAAA;AAEpD,iBAAOC,OAAK,OAAA;AAGZ,qBAAW,CAAC,WAAW,UAAA,KAAe,OAAO,QAC3CD,SAAO,KAAK,IAAA,GACX;AACD,kBAAM,kBAAkB,CAAC,GAAG,UAAU,SAAU,EAAC,KAAK,GAAA;AAGtD,mBAAK,eAAA,IAAmB,iBAAiB;cACvC,KAAK,WAAW;cAChB,MAAM;cACN,KAAK;cACL,WAAW,KAAK,UAAU,KAAK,GAAA;YAChC,CAAA;UACF;QACF,CAAA;MACF;IACF;AAED,aAAS,KAAKE,MAA2BC,OAA0B,CAAE,GAAE;AACrE,YAAMC,YAA0B,cAAc,CAAE,CAAA;AAChD,iBAAW,CAAC,KAAK,IAAA,KAAS,OAAO,QAAQ,QAAQ,CAAE,CAAA,GAAG;AACpD,YAAI,OAAO,IAAA,GAAO;AAChB,iBAAK,CAAC,GAAG,MAAM,GAAI,EAAC,KAAK,GAAA,CAAI,IAAI,iBAAiB;YAChD;YACA,KAAK;YACL;YACA;UACD,CAAA;AACD;QACD;AACD,YAAI,SAAS,IAAA,GAAO;AAClB,oBAAU,GAAA,IAAO,KAAK,KAAK,KAAK,QAAQ,CAAC,GAAG,MAAM,GAAI,CAAA;AACtD;QACD;AACD,YAAA,CAAK,YAAY,IAAA,GAAO;AAEtB,oBAAU,GAAA,IAAO,KAAK,MAAM,CAAC,GAAG,MAAM,GAAI,CAAA;AAC1C;QACD;AAED,cAAM,UAAU,CAAC,GAAG,MAAM,GAAI,EAAC,KAAK,GAAA;AAEpC,YAAI,WAAW,OAAA,EACb,OAAM,IAAI,MAAA,kBAAwB,OAAA,EAAQ;AAG5C,mBAAW,OAAA,IAAW;AACtB,kBAAU,GAAA,IAAO;MAClB;AAED,aAAO;IACR;AACD,UAAM,SAAS,KAAK,KAAA;AAEpB,UAAMC,OAA0B;MAC9B,SAAS;MACT,QAAQ;MACR;MACA,MAAA;MACA,GAAG;MACH;IACD;AAED,UAAMC,SAAiC;MACrC,GAAI;MACJ;MACA,cAAc,oBAAA,EAA6B,EACzC,KACD,CAAA;IACF;AACD,WAAO;EACR;AAED,SAAO;AACR;AAED,SAAS,YACPC,mBACmC;AACnC,SAAA,OAAc,sBAAsB;AACrC;AAKD,eAAsB,mBACpBC,QACAC,MAC8B;AAC9B,QAAM,EAAE,KAAA,IAAS;AACjB,MAAI,YAAY,KAAK,WAAW,IAAA;AAEhC,SAAA,CAAQ,WAAW;AACjB,UAAM,MAAM,OAAO,KAAK,KAAK,IAAA,EAAM,KAAK,CAACC,UAAQ,KAAK,WAAWA,KAAAA,CAAI;AAGrE,QAAA,CAAK,IACH,QAAO;AAIT,UAAM,aAAa,KAAK,KAAK,GAAA;AAC7B,UAAM,WAAW,KAAA;AAEjB,gBAAY,KAAK,WAAW,IAAA;EAC7B;AAED,SAAO;AACR;AAKD,eAAsB,cACpBC,MAIA;AACA,QAAM,EAAE,MAAM,KAAA,IAAS;AACvB,QAAM,OAAO,MAAM,mBAAmB,KAAK,QAAQ,IAAA;AACnD,MAAA,CACG,QAAA,CACA,YAAY,IAAA,KACZ,KAAK,KAAK,SAAS,QAAA,CAAS,KAAK,oBAElC,OAAM,IAAI,UAAU;IAClB,MAAM;IACN,SAAA,OAAgB,IAAA,wBAA4B,IAAA;EAC7C,CAAA;AAIH,MACE,KAAK,KAAK,SAAS,QACnB,KAAK,uBACL,KAAK,KAAK,SAAS,eAEnB,OAAM,IAAI,UAAU;IAClB,MAAM;IACN,SAAA;EACD,CAAA;AAGH,SAAO,KAAK,IAAA;AACb;AAQD,SAAgB,sBAEgB;AAC9B,SAAO,SAAS,kBACdC,QAC8B;AAC9B,UAAM,EAAE,KAAA,IAAS;AAGjB,WAAO,SAAS,aAAa,eAAe,MAAM;AAChD,aAAO,qBACL,OAAO,EAAE,MAAM,KAAA,MAAW;;AACxB,cAAM,WAAW,KAAK,KAAK,GAAA;AAE3B,YAAI,KAAK,WAAW,KAAK,KAAK,CAAA,MAAO,OACnC,QAAO;AAGT,cAAM,YAAY,MAAM,mBAAmB,QAAQ,QAAA;AAEnD,YAAIC,MAAAA;AACJ,YAAI;AACF,cAAA,CAAK,UACH,OAAM,IAAI,UAAU;YAClB,MAAM;YACN,SAAA,+BAAwC,IAAA;UACzC,CAAA;AAEH,gBAAM,WAAW,aAAA,IACb,MAAM,QAAQ,QAAQ,cAAA,CAAe,IACrC;AAEJ,iBAAO,MAAM,UAAU;YACrB,MAAM;YACN,aAAa,YAAY,KAAK,CAAA;YAC9B;YACA,MAAM,UAAU,KAAK;YACrB,QAAQ,6BAAM;UACf,CAAA;QACF,SAAQ,OAAO;AACd,WAAAC,MAAA,6BAAM,YAAN,gBAAAA,IAAA,WAAgB;YACd;YACA,OAAO,wBAAwB,KAAA;YAC/B,OAAO,KAAK,CAAA;YACZ,MAAM;YACN,OAAM,uCAAW,KAAK,SAAQ;UAC/B;AACD,gBAAM;QACP;MACF,CAAA;IAEJ;EACF;AACF;AAcD,SAAgB,gBACX,YACqB;;AACxB,QAAM,SAAS,sBACb,CAAE,GACF,GAAG,WAAW,IAAI,CAAC,MAAM,EAAE,KAAK,MAAA,CAAO;AAEzC,QAAM,iBAAiB,WAAW,OAChC,CAAC,uBAAuB,eAAe;AACrC,QACE,WAAW,KAAK,QAAQ,kBACxB,WAAW,KAAK,QAAQ,mBAAmB,kBAC3C;AACA,UACE,0BAA0B,oBAC1B,0BAA0B,WAAW,KAAK,QAAQ,eAElD,OAAM,IAAI,MAAM,2CAAA;AAElB,aAAO,WAAW,KAAK,QAAQ;IAChC;AACD,WAAO;EACR,GACD,gBAAA;AAGF,QAAM,cAAc,WAAW,OAAO,CAAC,MAAM,YAAY;AACvD,QACE,QAAQ,KAAK,QAAQ,eACrB,QAAQ,KAAK,QAAQ,gBAAgB,oBACrC;AACA,UACE,SAAS,sBACT,SAAS,QAAQ,KAAK,QAAQ,YAE9B,OAAM,IAAI,MAAM,uCAAA;AAElB,aAAO,QAAQ,KAAK,QAAQ;IAC7B;AACD,WAAO;EACR,GAAE,kBAAA;AAEH,QAAM,SAAS,oBAAoB;IACjC;IACA;IACA,OAAO,WAAW,MAAM,CAAC,MAAM,EAAE,KAAK,QAAQ,KAAA;IAC9C,sBAAsB,WAAW,MAC/B,CAAC,MAAM,EAAE,KAAK,QAAQ,oBAAA;IAExB,UAAU,WAAW,MAAM,CAAC,MAAM,EAAE,KAAK,QAAQ,QAAA;IACjD,SAAQA,MAAA,WAAW,CAAA,MAAX,gBAAAA,IAAe,KAAK,QAAQ;EACrC,CAAA,EAAE,MAAA;AAEH,SAAO;AACR;AC3iBD,IAAM,gBAAgB,OAAA;AAyBtB,SAAgB,kBACdC,OACiC;AACjC,SAAO,MAAM,QAAQ,KAAA,KAAU,MAAM,CAAA,MAAO;AAC7C;A;;;;;;;;;;;;;;;;;;;;;;;;;AG3BD,SAAgB,aACdC,OACwD;AACxD,SAAO,SAAS,KAAA,KAAU,MAAM,MAAA,MAAY;AAC7C;ACQD,IAAM,oBAAoB,oBAAI,QAAA;AAQ9B,IAAM,OAAO,MAAM;AAElB;;AAmCD,IAAa,aAkKD,YAAO,aAlKnB,WAAqD;EAwBzC,YAAYC,KAAuD;AApB1D;;;AAIT;;uCAA6D,CAAE;AAK/D;;;sCAA6C;AAqJvD;wBAAU,IAAsB;AAxI9B,QAAA,OAAW,QAAQ,WACjB,MAAK,UAAU,IAAI,QAAQ,GAAA;QAE3B,MAAK,UAAU;AAMjB,UAAM,aAAa,KAAK,QAAQ,KAAK,CAAC,UAAU;AAE9C,YAAM,EAAE,YAAA,IAAgB;AACxB,WAAK,cAAc;AACnB,WAAK,aAAa;QAChB,QAAQ;QACR;MACD;AAED,iDAAa,QAAQ,CAAC,EAAE,QAAA,MAAc;AACpC,gBAAQ,KAAA;MACT;IACF,CAAA;AAGD,QAAI,WAAW,WACb,YAAW,MAAM,CAAC,WAAW;AAE3B,YAAM,EAAE,YAAA,IAAgB;AACxB,WAAK,cAAc;AACnB,WAAK,aAAa;QAChB,QAAQ;QACR;MACD;AAED,iDAAa,QAAQ,CAAC,EAAE,OAAA,MAAa;AACnC,eAAO,MAAA;MACR;IACF,CAAA;EAEJ;;;;;;;;;;;;;;;;;;;EAoBD,YAAkC;AAEhC,QAAIC;AACJ,QAAIC;AAEJ,UAAM,EAAE,WAAA,IAAe;AACvB,QAAI,eAAe,MAAM;AAEvB,UAAI,KAAK,gBAAgB,KAEvB,OAAM,IAAI,MAAM,6CAAA;AAElB,YAAM,aAAa,cAAA;AACnB,WAAK,cAAc,eAAe,KAAK,aAAa,UAAA;AACpD,gBAAU,WAAW;AACrB,oBAAc,MAAM;AAClB,YAAI,KAAK,gBAAgB,KACvB,MAAK,cAAc,kBAAkB,KAAK,aAAa,UAAA;MAE1D;IACF,OAAM;AAEL,YAAM,EAAE,OAAA,IAAW;AACnB,UAAI,WAAW,YACb,WAAU,QAAQ,QAAQ,WAAW,KAAA;UAErC,WAAU,QAAQ,OAAO,WAAW,MAAA;AAEtC,oBAAc;IACf;AAGD,WAAO,OAAO,OAAO,SAAS,EAAE,YAAa,CAAA;EAC9C;;EAID,KACEC,aAIAC,YAIwC;AACxC,UAAM,aAAa,KAAK,UAAA;AACxB,UAAM,EAAE,YAAA,IAAgB;AACxB,WAAO,OAAO,OAAO,WAAW,KAAK,aAAa,UAAA,GAAa,EAC7D,YACD,CAAA;EACF;EAED,MACEC,YAIgC;AAChC,UAAM,aAAa,KAAK,UAAA;AACxB,UAAM,EAAE,YAAA,IAAgB;AACxB,WAAO,OAAO,OAAO,WAAW,MAAM,UAAA,GAAa,EACjD,YACD,CAAA;EACF;EAED,QAAQC,WAAyD;AAC/D,UAAM,aAAa,KAAK,UAAA;AACxB,UAAM,EAAE,YAAA,IAAgB;AACxB,WAAO,OAAO,OAAO,WAAW,QAAQ,SAAA,GAAY,EAClD,YACD,CAAA;EACF;;;;EAUD,OAAO,MAASC,SAA0C;AACxD,UAAM,SAAS,GAAU,uBAAuB,OAAA;AAChD,WAAA,OAAc,WAAW,cACrB,SACA,GAAU,0BAA0B,OAAA;EACzC;;EAGD,OAAiB,0BAA6BA,SAAyB;AACrE,UAAM,UAAU,IAAI,GAAa,OAAA;AACjC,sBAAkB,IAAI,SAAS,OAAA;AAC/B,sBAAkB,IAAI,SAAS,OAAA;AAC/B,WAAO;EACR;;EAGD,OAAiB,uBAA0BA,SAAyB;AAClE,WAAO,kBAAkB,IAAI,OAAA;EAC9B;;;;EAMD,OAAO,QAAWC,OAA2B;AAC3C,UAAMD,UAAAA,OACG,UAAU,YACjB,UAAU,QACV,UAAU,SAAA,OACH,MAAM,SAAS,aAClB,QACA,QAAQ,QAAQ,KAAA;AACtB,WAAO,GAAU,MAAM,OAAA,EAAS,UAAA;EAGjC;EAQD,aAAa,IACXE,QACqB;AACrB,UAAM,cAAc,MAAM,QAAQ,MAAA,IAAU,SAAS,CAAC,GAAG,MAAO;AAChE,UAAM,qBAAqB,YAAY,IAAI,GAAU,OAAA;AACrD,QAAI;AACF,aAAO,MAAM,QAAQ,IAAI,kBAAA;IAC1B,UAAA;AACC,yBAAmB,QAAQ,CAAC,EAAE,YAAA,MAAkB;AAC9C,oBAAA;MACD,CAAA;IACF;EACF;EAQD,aAAa,KACXA,QACqB;AACrB,UAAM,cAAc,MAAM,QAAQ,MAAA,IAAU,SAAS,CAAC,GAAG,MAAO;AAChE,UAAM,qBAAqB,YAAY,IAAI,GAAU,OAAA;AACrD,QAAI;AACF,aAAO,MAAM,QAAQ,KAAK,kBAAA;IAC3B,UAAA;AACC,yBAAmB,QAAQ,CAAC,EAAE,YAAA,MAAkB;AAC9C,oBAAA;MACD,CAAA;IACF;EACF;;;;;;;;;;;;;EAcD,aAAa,eACXC,UACA;AAEA,UAAM,eAAe,SAAS,IAAI,gBAAA;AAGlC,QAAI;AACF,aAAO,MAAM,QAAQ,KAAK,YAAA;IAC3B,UAAA;AACC,iBAAW,WAAW,aAEpB,SAAQ,YAAA;IAEX;EACF;AACF,GAjRD;AAyRA,SAAgB,iBACdC,SACwC;AACxC,SAAO,UAAU,MAAM,OAAA,EAAS,KAAK,MAAM,CAAC,OAAQ,CAAA;AACrD;AAKD,SAAS,gBAA4C;AACnD,MAAIC;AACJ,MAAIC;AACJ,QAAM,UAAU,IAAI,QAAW,CAAC,UAAU,YAAY;AACpD,cAAU;AACV,aAAS;EACV,CAAA;AACD,SAAO;IACL;IACA;IACA;EACD;AACF;AAID,SAAS,eAAkBC,KAAmBC,QAAyB;AACrE,SAAO,CAAC,GAAG,KAAK,MAAO;AACxB;AAED,SAAS,iBAAoBD,KAAmBE,OAAe;AAC7D,SAAO,CAAC,GAAG,IAAI,MAAM,GAAG,KAAA,GAAQ,GAAG,IAAI,MAAM,QAAQ,CAAA,CAAG;AACzD;AAED,SAAS,kBAAqBF,KAAmBG,QAAiB;AAChE,QAAM,QAAQ,IAAI,QAAQ,MAAA;AAC1B,MAAI,UAAU,GACZ,QAAO,iBAAiB,KAAK,KAAA;AAE/B,SAAO;AACR;ACzXD,OAAO,YAAP,OAAO,UAAY,OAAA;AAInB,OAAO,iBAAP,OAAO,eAAiB,OAAA;AASxB,SAAgB,aAAgBC,OAAUC,SAAqC;AAC7E,QAAM,KAAK;AAGX,QAAM,WAAW,GAAG,OAAO,OAAA;AAG3B,KAAG,OAAO,OAAA,IAAW,MAAM;AACzB,YAAA;AACA;EACD;AAED,SAAO;AACR;AASD,SAAgB,kBACdD,OACAE,SACqB;AACrB,QAAM,KAAK;AAGX,QAAM,WAAW,GAAG,OAAO,YAAA;AAG3B,KAAG,OAAO,YAAA,IAAgB,YAAY;AACpC,UAAM,QAAA;AACN,WAAM;EACP;AAED,SAAO;AACR;ACnDD,IAAa,+BAA+B,OAAA;AAE5C,SAAgB,cAAcC,IAAY;AACxC,MAAIC,QAA8C;AAElD,SAAO,aACL,EACE,QAAQ;AACN,QAAI,MACF,OAAM,IAAI,MAAM,uBAAA;AAGlB,UAAM,UAAU,IAAI,QAClB,CAAC,YAAY;AACX,cAAQ,WAAW,MAAM,QAAQ,4BAAA,GAA+B,EAAA;IACjE,CAAA;AAEH,WAAO;EACR,EACF,GACD,MAAM;AACJ,QAAI,MACF,cAAa,KAAA;EAEhB,CAAA;AAEJ;;AC5BD,WAAS,YAAY;AACnB,QAAI,IAAI,cAAA,OAAqB,kBAAkB,kBAAkB,SAAUC,KAAGC,KAAG;AAC7E,UAAIC,MAAI,MAAA;AACR,aAAOA,IAAE,OAAO,mBAAmBA,IAAE,QAAQF,KAAGE,IAAE,aAAaD,KAAGC;IACnE,GACD,IAAI,CAAE,GACN,IAAI,CAAE;AACR,aAAS,MAAMF,KAAGC,KAAG;AACnB,UAAI,QAAQA,KAAG;AACb,YAAI,OAAOA,GAAAA,MAAOA,IAAG,OAAM,IAAI,UAAU,kFAAA;AACzC,YAAID,IAAG,KAAI,IAAIC,IAAE,OAAO,gBAAgB,OAAO,KAAA,EAAO,qBAAA,CAAsB;AAC5E,YAAA,WAAe,MAAM,IAAIA,IAAE,OAAO,WAAW,OAAO,KAAA,EAAO,gBAAA,CAAiB,GAAGD,KAAI,KAAI,IAAI;AAC3F,YAAI,cAAA,OAAqB,EAAG,OAAM,IAAI,UAAU,2BAAA;AAChD,cAAM,IAAI,SAASG,MAAI;AACrB,cAAI;AACF,cAAE,KAAKF,GAAAA;UACR,SAAQD,KAAG;AACV,mBAAO,QAAQ,OAAOA,GAAAA;UACvB;QACF,IAAG,EAAE,KAAK;UACT,GAAGC;UACH,GAAG;UACH,GAAGD;QACJ,CAAA;MACF,MAAM,QAAK,EAAE,KAAK;QACjB,GAAGC;QACH,GAAGD;MACJ,CAAA;AACD,aAAOC;IACR;AACD,WAAO;MACF;MACH,GAAG,MAAM,KAAK,MAAA,KAAO;MACrB,GAAG,MAAM,KAAK,MAAA,IAAO;MACrB,GAAG,SAAS,IAAI;AACd,YAAI,GACF,IAAI,KAAK,GACT,IAAI;AACN,iBAAS,OAAO;AACd,iBAAO,IAAI,EAAE,IAAA,IAAQ,KAAI;AACvB,gBAAA,CAAK,EAAE,KAAK,MAAM,EAAG,QAAO,IAAI,GAAG,EAAE,KAAK,CAAA,GAAI,QAAQ,QAAA,EAAU,KAAK,IAAA;AACrE,gBAAI,EAAE,GAAG;AACP,kBAAID,MAAI,EAAE,EAAE,KAAK,EAAE,CAAA;AACnB,kBAAI,EAAE,EAAG,QAAO,KAAK,GAAG,QAAQ,QAAQA,GAAAA,EAAG,KAAK,MAAM,GAAA;YACvD,MAAM,MAAK;UACb,SAAQA,KAAG;AACV,mBAAO,IAAIA,GAAAA;UACZ;AACD,cAAI,MAAM,EAAG,QAAO,MAAM,IAAI,QAAQ,OAAO,CAAA,IAAK,QAAQ,QAAA;AAC1D,cAAI,MAAM,EAAG,OAAM;QACpB;AACD,iBAAS,IAAIE,KAAG;AACd,iBAAO,IAAI,MAAM,IAAI,IAAI,EAAEA,KAAG,CAAA,IAAKA,KAAG,KAAA;QACvC;AACD,eAAO,KAAA;MACR;IACF;EACF;AACD,SAAO,UAAU,WAAW,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,SAAA,IAAa,OAAO;;;ACrDjG,SAAgB,iBACdE,UACyD;AACzD,QAAM,WAAW,SAAS,OAAO,aAAA,EAAA;AAIjC,MAAI,SAAS,OAAO,YAAA,EAClB,QAAO;AAGT,SAAO,kBAAkB,UAAU,YAAY;;AAC7C,YAAMC,MAAA,SAAS,WAAT,gBAAAA,IAAA;EACP,CAAA;AACF;AClBD,SAAgB,iBAAgC;AAC9C,MAAIC;AACJ,MAAIC;AACJ,QAAM,UAAU,IAAI,QAAgB,CAAC,KAAK,QAAQ;AAChD,cAAU;AACV,aAAS;EACV,CAAA;AAED,SAAO;IAAE;IAAkB;IAAkB;EAAS;AACvD;;;AGND,IAAa,WAAW,OAAO,MAAA;A;ACyB/B,IAAM,2BAA2B;AAEjC,IAAM,kCAAkC;AAGxC,IAAM,2BAA2B;AAEjC,IAAM,0BAA0B;AAGhC,IAAM,+BAA+B;AAErC,IAAM,8BAA8B;AAEpC,IAAM,8BAA8B;AA4QpC,IAAM,aAAN,cAAyB,MAAM;EAC7B,YAA4BC,MAAe;AACzC,UAAM,4BAAA;AADoB,SAAA,OAAA;EAE3B;AACF;AAGD,IAAM,4BAA4B,CAACC,WAAsC;AACvE,SAAO,EACL,YAAY;AACV,UAAM,SAAS,IAAI,eAA2B,EAC5C,MAAM,YAAY;AAChB,aAAO,GAAG,QAAQ,CAAC,UAAU;AAC3B,mBAAW,QAAQ,KAAA;MACpB,CAAA;AACD,aAAO,GAAG,OAAO,MAAM;AACrB,mBAAW,MAAA;MACZ,CAAA;AACD,aAAO,GAAG,SAAS,CAAC,UAAU;AAC5B,mBAAW,MAAM,KAAA;MAClB,CAAA;IACF,EACF,CAAA;AACD,WAAO,OAAO,UAAA;EACf,EACF;AACF;AAED,SAAS,sBACPC,MACA;AACA,QAAM,SACJ,eAAe,OACX,KAAK,UAAA,IACL,0BAA0B,IAAA,EAAM,UAAA;AAEtC,MAAI,gBAAgB;AAEpB,SAAO,IAAI,eAAe;IACxB,MAAM,KAAK,YAAY;AACrB,YAAM,EAAE,MAAM,MAAA,IAAU,MAAM,OAAO,KAAA;AAErC,UAAI,KACF,YAAW,MAAA;UAEX,YAAW,QAAQ,KAAA;IAEtB;IACD,SAAS;AACP,aAAO,OAAO,OAAA;IACf;EACF,CAAA,EACE,YAAY,IAAI,kBAAA,CAAA,EAChB,YACC,IAAI,gBAAgC,EAClC,UAAU,OAAO,YAAY;AAC3B,qBAAiB;AACjB,UAAM,QAAQ,cAAc,MAAM,IAAA;AAClC,oBAAgB,MAAM,IAAA,KAAS;AAC/B,eAAW,QAAQ,MACjB,YAAW,QAAQ,IAAA;EAEtB,EACF,CAAA,CAAA;AAEN;AACD,SAAS,qBACPA,MACA;AACA,QAAM,SAAS,sBAAsB,IAAA;AAErC,MAAI,WAAW;AACf,SAAO,OAAO,YACZ,IAAI,gBAA2C,EAC7C,UAAU,MAAM,YAAY;AAC1B,QAAA,CAAK,UAAU;AACb,YAAM,OAAO,KAAK,MAAM,IAAA;AACxB,iBAAW,QAAQ,IAAA;AACnB,iBAAW;IACZ,OAAM;AACL,YAAMC,QAAmB,KAAK,MAAM,IAAA;AACpC,iBAAW,QAAQ,KAAA;IACpB;EACF,EACF,CAAA,CAAA;AAEJ;AAKD,SAAS,qBAAqBC,iBAAkC;AAC9D,QAAM,gBAAgB,oBAAI,IAAA;AAQ1B,WAAS,UAAU;AACjB,WAAO,MAAM,KAAK,cAAc,OAAA,CAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,MAAA;EAC1D;AAKD,WAAS,yBAAyB;AAChC,QAAIC;AACJ,UAAM,SAAS,IAAI,eAA0B,EAC3C,MAAM,YAAY;AAChB,2BAAqB;IACtB,EACF,CAAA;AAED,UAAM,mBAAmB;MACvB,SAAS,CAACC,MAAiB,mBAAmB,QAAQ,CAAA;MACtD,OAAO,MAAM;AACX,2BAAmB,MAAA;AAEnB,cAAA;AAEA,YAAI,QAAA,EACF,iBAAgB,MAAA;MAEnB;MACD,QAAQ;MACR,mBAAmB,MAAM;AACvB,cAAM,SAAS,OAAO,UAAA;AAEtB,eAAO,aAAa,QAAQ,MAAM;AAChC,iBAAO,YAAA;AACP,2BAAiB,MAAA;QAClB,CAAA;MACF;MACD,OAAO,CAACC,WAAoB;AAC1B,2BAAmB,MAAM,MAAA;AACzB,cAAA;MACD;IACF;AACD,aAAS,QAAQ;AACf,aAAO,OAAO,kBAAkB;QAC9B,QAAQ;QACR,OAAO,MAAM;QAEZ;QACD,SAAS,MAAM;QAEd;QACD,mBAAmB;QACnB,OAAO,MAAM;QAEZ;MACF,CAAA;IACF;AAED,WAAO;EACR;AAKD,WAAS,YAAYC,SAAqB;AACxC,QAAI,IAAI,cAAc,IAAI,OAAA;AAC1B,QAAA,CAAK,GAAG;AACN,UAAI,uBAAA;AACJ,oBAAc,IAAI,SAAS,CAAA;IAC5B;AACD,WAAO;EACR;AAKD,WAAS,UAAUD,QAAiB;AAClC,eAAW,cAAc,cAAc,OAAA,EACrC,YAAW,MAAM,MAAA;EAEpB;AAED,SAAO;IACL;IACA;IACA;EACD;AACF;AAMD,eAAsB,oBAA2BE,MAS9C;AACD,QAAM,EAAE,cAAc,CAAC,MAAM,EAAA,IAAM;AAEnC,MAAI,SAAS,qBAA2B,KAAK,IAAA;AAC7C,MAAI,YACF,UAAS,OAAO,YACd,IAAI,gBAAgB,EAClB,UAAU,OAAO,YAAY;AAC3B,eAAW,QAAQ,YAAY,KAAA,CAAM;EACtC,EACF,CAAA,CAAA;AAGL,MAAIC,eAAuC,eAAA;AAE3C,QAAM,gBAAgB,qBAAqB,KAAK,eAAA;AAEhD,WAAS,sBAAsBC,OAAwB;AACrD,UAAM,CAAC,OAAO,MAAM,OAAA,IAAW;AAE/B,UAAM,aAAa,cAAc,YAAY,OAAA;AAE7C,YAAQ,MAAR;MACE,KAAK;AACH,eAAO,IAAI,YAAY;;;;AACrB,kBAAM,SAAA,WAAA,EAAS,WAAW,kBAAA,CAAmB;AAE7C,kBAAM,EAAE,OAAA,QAAA,IAAU,MAAM,OAAO,KAAA;AAC/B,kBAAM,CAAC,UAAU,QAAQ,IAAA,IAAQC;AACjC,oBAAQ,QAAR;cACE,KAAK;AACH,uBAAO,OAAO,IAAA;cAChB,KAAK;AACH,wBAAMC,MAAA,KAAK,gBAAL,gBAAAA,IAAA,WAAmB,EAAE,OAAO,KAAM,OAAK,IAAI,WAAW,IAAA;YAC/D;;;;;;QACF,CAAA;MAEH,KAAK;AACH,eAAO,IAAI,mBAAmB;;;;AAC5B,kBAAM,SAAA,WAAA,EAAS,WAAW,kBAAA,CAAmB;AAE7C,mBAAO,MAAM;AACX,oBAAM,EAAE,OAAA,QAAA,IAAU,MAAM,OAAO,KAAA;AAE/B,oBAAM,CAAC,UAAU,QAAQ,IAAA,IAAQD;AAEjC,sBAAQ,QAAR;gBACE,KAAK;AACH,wBAAM,OAAO,IAAA;AACb;gBACF,KAAK;AACH,yBAAO,OAAO,IAAA;gBAChB,KAAK;AACH,0BACEC,MAAA,KAAK,gBAAL,gBAAAA,IAAA,WAAmB,EAAE,OAAO,KAAM,OAAK,IAAI,WAAW,IAAA;cAE3D;YACF;;;;;;QACF,CAAA;IAEJ;EACF;AAED,WAAS,OAAOC,OAA8B;AAC5C,UAAM,CAAC,CAAC,IAAA,GAAO,GAAG,UAAA,IAAc;AAEhC,eAAWF,WAAS,YAAY;AAC9B,YAAM,CAAC,GAAA,IAAOA;AACd,YAAM,UAAU,sBAAsBA,OAAAA;AAEtC,UAAI,QAAQ,KACV,QAAO;AAGR,WAAa,GAAA,IAAO;IACtB;AACD,WAAO;EACR;AAED,QAAM,eAAe,CAACL,WAAoB;AACxC,iDAAc,OAAO;AACrB,kBAAc,UAAU,MAAA;EACzB;AACD,SACG,OACC,IAAI,eAAe;IACjB,MAAM,aAAa;AACjB,UAAI,cAAc;AAChB,cAAM,OAAO;AAEb,mBAAW,CAAC,KAAK,KAAA,KAAU,OAAO,QAAQ,WAAA,GAAc;AACtD,gBAAM,SAAS,OAAO,KAAA;AACtB,eAAK,GAAA,IAAO;QACb;AACD,qBAAa,QAAQ,IAAA;AACrB,uBAAe;AAEf;MACD;AACD,YAAM,QAAQ;AACd,YAAM,CAAC,GAAA,IAAO;AAEd,YAAM,aAAa,cAAc,YAAY,GAAA;AAC7C,iBAAW,QAAQ,KAAA;IACpB;IACD,OAAO,MAAM,aAAa,IAAI,MAAM,eAAA,CAAA;IACpC,OAAO;EACR,CAAA,GACD,EACE,QAAQ,KAAK,gBAAgB,OAC9B,CAAA,EAEF,MAAM,CAAC,UAAU;;AAChB,KAAAM,MAAA,KAAK,YAAL,gBAAAA,IAAA,WAAe,EAAE,MAAO;AACxB,iBAAa,KAAA;EACd,CAAA;AAEH,SAAO,CAAC,MAAM,aAAa,SAAS,aAAc;AACnD;;AChjBD,IAAM,aAAa;AACnB,IAAM,yBAAyB;AAC/B,IAAM,kBAAkB;AACxB,IAAM,eAAe;AAuMrB,eAAe,YAAeE,MAIf;;;AACb,UAAM,iBAAA,YAAA,EAAiB,cAAc,KAAK,SAAA,CAAU;AACpD,UAAM,MAAM,MAAM,UAAU,KAAK,CAAC,KAAK,SAAS,eAAe,MAAA,CAAQ,CAAA;AAEvE,QAAI,QAAQ,6BACV,QAAO,MAAM,KAAK,UAAA;AAEpB,WAAO;;;;;;AACR;AAKD,SAAgB,kBACdC,MAC8C;AAC9C,QAAM,EAAE,cAAc,CAAC,MAAM,EAAA,IAAM;AAEnC,MAAIC,gBAAkC,CAAE;AAExC,QAAM,SAAS,KAAK;AAEpB,MAAIC,MAAmD;AAEvD,QAAM,eAAe,MACnB,IAAI,eAA8C;IAChD,MAAM,MAAM,YAAY;AACtB,YAAM,CAAC,KAAK,IAAA,IAAQ,MAAM,QAAQ,IAAI,CAAC,KAAK,IAAA,GAAO,KAAK,KAAA,CAAO,CAAA;AAC/D,YAAM,cAAe,MAAM,IAAI,KAAK,YAClC,KACA,IAAA;AAGF,iBAAW,QAAQ;QACjB,MAAM;QACN,aAAa;QACb,OAAO;MACR,CAAA;AAED,kBAAY,iBAAiB,iBAAiB,CAAC,SAAS;AACtD,cAAM,MAAM;AAEZ,cAAMC,UAA4B,KAAK,MAAM,IAAI,IAAA;AAEjD,wBAAgB;AAChB,mBAAW,QAAQ;UACjB,MAAM;UACN;UACA;QACD,CAAA;MACF,CAAA;AAED,kBAAY,iBAAiB,wBAAwB,CAAC,SAAS;AAC7D,cAAM,MAAM;AAEZ,mBAAW,QAAQ;UACjB,MAAM;UACN,OAAO,YAAY,KAAK,MAAM,IAAI,IAAA,CAAK;UACvC;QACD,CAAA;MACF,CAAA;AACD,kBAAY,iBAAiB,YAAY,MAAM;AAC7C,mBAAW,QAAQ;UACjB,MAAM;UACN;QACD,CAAA;MACF,CAAA;AACD,kBAAY,iBAAiB,cAAc,MAAM;AAC/C,oBAAY,MAAA;AACZ,mBAAW,MAAA;AACX,cAAM;MACP,CAAA;AACD,kBAAY,iBAAiB,SAAS,CAAC,UAAU;AAC/C,YAAI,YAAY,eAAe,YAAY,OACzC,YAAW,MAAM,KAAA;YAEjB,YAAW,QAAQ;UACjB,MAAM;UACN;UACA;QACD,CAAA;MAEJ,CAAA;AACD,kBAAY,iBAAiB,WAAW,CAAC,SAAS;AAChD,cAAM,MAAM;AAEZ,cAAM,QAAQ,YAAY,KAAK,MAAM,IAAI,IAAA,CAAK;AAE9C,cAAMC,MAAe,EACnB,MAAM,MACP;AACD,YAAI,IAAI,YACN,KAAI,KAAK,IAAI;AAEf,mBAAW,QAAQ;UACjB,MAAM;UACN,MAAM;UACN;QACD,CAAA;MACF,CAAA;AAED,YAAM,UAAU,MAAM;AACpB,YAAI;AACF,sBAAY,MAAA;AACZ,qBAAW,MAAA;QACZ,QAAO;QAEP;MACF;AACD,UAAI,OAAO,QACT,SAAA;UAEA,QAAO,iBAAiB,SAAS,OAAA;IAEpC;IACD,SAAS;AACP,iCAAK;IACN;EACF,CAAA;AAEH,QAAM,oBAAoB,MAAM;AAC9B,QAAI,SAAS,aAAA;AACb,QAAI,SAAS,OAAO,UAAA;AAEpB,mBAAe,UAAU;AACvB,YAAM,OAAO,OAAA;AACb,YAAM;IACP;AAED,WAAO,kBACL;MACE,OAAO;AACL,eAAO,OAAO,KAAA;MACf;MACD,MAAM,WAAW;AACf,cAAM,QAAA;AAEN,iBAAS,aAAA;AACT,iBAAS,OAAO,UAAA;MACjB;IACF,GACD,OAAA;EAEH;AAED,SAAO,IAAI,mBAAmB;;;AAC5B,YAAY,SAAA,WAAA,EAAS,kBAAA,CAAmB;AAExC,aAAO,MAAM;AACX,YAAI,UAAU,OAAO,KAAA;AAErB,cAAM,YAAY,cAAc;AAChC,YAAI,UACF,WAAU,YAAY;UACpB;UACA;UACA,WAAW,YAAY;AACrB,kBAAMC,MAA+B;cACnC,OAAO;gBACL,MAAM;gBACN,IAAI;gBACJ,aAAa;cACd;cACD,MAAM;YACP;AAED,kBAAM,OAAO,SAAA;AAEb,mBAAO;UACR;QACF,CAAA;AAGH,cAAM,SAAS,MAAM;AAErB,YAAI,OAAO,KACT,QAAO,OAAO;AAEhB,cAAM,OAAO;MACd;;;;;;EACF,CAAA;AACF;;;AEjcD,IAAa,mBAAmB;AAmHhC,SAAgB,0BAIZ;AACF,WAAS,sBACPC,aACsB;AACtB,WAAO;MACL,cAAc;MACd,cAAc,uBAAuB;AACnC,cAAM,kBACJ,kBAAkB,wBACd,sBAAsB,eACtB,CAAC,qBAAsB;AAE7B,eAAO,sBAAsB,CAAC,GAAG,aAAa,GAAG,eAAgB,CAAA;MAClE;IACF;EACF;AAED,WAAS,iBACPC,IAOkE;AAClE,WAAO,sBAAsB,CAAC,EAAG,CAAA;EAClC;AAED,SAAO;AACR;AAyBD,SAAgB,sBAA8BC,OAAwB;AACpE,QAAMC,kBACJ,eAAe,yBAAyB,MAAM;AAC5C,QAAIC;AAEJ,UAAM,WAAW,MAAM,KAAK,YAAA;AAC5B,QAAI;AACF,oBAAc,MAAM,MAAM,QAAA;IAC3B,SAAQ,OAAO;AACd,YAAM,IAAI,UAAU;QAClB,MAAM;QACN;MACD,CAAA;IACF;AAGD,UAAM,gBACJ,SAAS,KAAK,KAAA,KAAU,SAAS,WAAA,IAC7B;MACE,GAAG,KAAK;MACR,GAAG;IACJ,IACD;AAEN,WAAO,KAAK,KAAK,EAAE,OAAO,cAAe,CAAA;EAC1C;AACH,kBAAgB,QAAQ;AACxB,SAAO;AACR;AAKD,SAAgB,uBAAgCC,OAAyB;AACvE,QAAMC,mBACJ,eAAe,0BAA0B,EAAE,KAAA,GAAQ;AACjD,UAAM,SAAS,MAAM,KAAA;AACrB,QAAA,CAAK,OAAO,GAEV,QAAO;AAET,QAAI;AACF,YAAM,OAAO,MAAM,MAAM,OAAO,IAAA;AAChC,aAAO;QACL,GAAG;QACH;MACD;IACF,SAAQ,OAAO;AACd,YAAM,IAAI,UAAU;QAClB,SAAS;QACT,MAAM;QACN;MACD,CAAA;IACF;EACF;AACH,mBAAiB,QAAQ;AACzB,SAAO;AACR;AC1OD,IAAa,wBAAb,cAA2C,MAAM;;;;;;EAS/C,YAAYC,QAA+C;;AACzD,WAAMC,MAAA,OAAO,CAAA,MAAP,gBAAAA,IAAW,OAAA;AARH;;AASd,SAAK,OAAO;AACZ,SAAK,SAAS;EACf;AACF;AC4DD,SAAgB,WAAkBC,iBAAyC;AACzE,QAAM,SAAS;AACf,QAAM,mBAAmB,eAAe;AAExC,MAAA,OAAW,WAAW,cAAA,OAAqB,OAAO,WAAW,WAE3D,QAAO,OAAO,OAAO,KAAK,MAAA;AAG5B,MAAA,OAAW,WAAW,cAAA,CAAe,iBAGnC,QAAO;AAGT,MAAA,OAAW,OAAO,eAAe,WAE/B,QAAO,OAAO,WAAW,KAAK,MAAA;AAGhC,MAAA,OAAW,OAAO,UAAU,WAG1B,QAAO,OAAO,MAAM,KAAK,MAAA;AAG3B,MAAA,OAAW,OAAO,iBAAiB,WAEjC,QAAO,OAAO,aAAa,KAAK,MAAA;AAGlC,MAAA,OAAW,OAAO,WAAW,WAE3B,QAAO,OAAO,OAAO,KAAK,MAAA;AAG5B,MAAA,OAAW,OAAO,WAAW,WAE3B,QAAO,CAAC,UAAU;AAChB,WAAO,OAAO,KAAA;AACd,WAAO;EACR;AAGH,MAAI,iBAEF,QAAO,OAAO,UAAU;AACtB,UAAM,SAAS,MAAM,OAAO,WAAA,EAAa,SAAS,KAAA;AAClD,QAAI,OAAO,OACT,OAAM,IAAI,sBAAsB,OAAO,MAAA;AAEzC,WAAO,OAAO;EACf;AAGH,QAAM,IAAI,MAAM,+BAAA;AACjB;ACuUD,SAAS,iBACPC,MACAC,MACqB;AACrB,QAAM,EAAE,cAAc,CAAE,GAAE,QAAQ,MAAM,GAAG,KAAA,IAAS;AAGpD,SAAO,cAAc;IACnB,GAAG,sBAAsB,MAAM,IAAA;IAC/B,QAAQ,CAAC,GAAG,KAAK,QAAQ,GAAI,UAAU,CAAE,CAAE;IAC3C,aAAa,CAAC,GAAG,KAAK,aAAa,GAAG,WAAY;IAClD,MAAM,KAAK,QAAQ,OAAO;MAAE,GAAG,KAAK;MAAM,GAAG;IAAM,IAAI,QAAQ,KAAK;EACrE,CAAA;AACF;AAED,SAAgB,cACdC,UAA2C,CAAE,GAU7C;AACA,QAAMC,OAA+B;IACnC,WAAW;IACX,QAAQ,CAAE;IACV,aAAa,CAAE;IACf,GAAG;EACJ;AAED,QAAMC,UAA+B;IACnC;IACA,MAAM,OAAO;AACX,YAAM,SAAS,WAAW,KAAA;AAC1B,aAAO,iBAAiB,MAAM;QAC5B,QAAQ,CAAC,KAAgB;QACzB,aAAa,CAAC,sBAAsB,MAAA,CAAQ;MAC7C,CAAA;IACF;IACD,OAAOC,QAAgB;AACrB,YAAM,SAAS,WAAW,MAAA;AAC1B,aAAO,iBAAiB,MAAM;QAC5B;QACA,aAAa,CAAC,uBAAuB,MAAA,CAAQ;MAC9C,CAAA;IACF;IACD,KAAK,MAAM;AACT,aAAO,iBAAiB,MAAM,EAC5B,KACD,CAAA;IACF;IACD,IAAI,uBAAuB;AAEzB,YAAM,cACJ,kBAAkB,wBACd,sBAAsB,eACtB,CAAC,qBAAsB;AAE7B,aAAO,iBAAiB,MAAM,EACf,YACd,CAAA;IACF;IACD,gBAAgBC,WAAS;AACvB,aAAO,iBAAiB,MAAOA,UAAgC,IAAA;IAChE;IACD,OAAOA,WAAS;AACd,aAAO,iBAAiB,MAAOA,UAAgC,IAAA;IAChE;IACD,MAAM,UAAU;AACd,aAAO,eACL;QAAE,GAAG;QAAM,MAAM;MAAS,GAC1B,QAAA;IAEH;IACD,SAAS,UAAU;AACjB,aAAO,eACL;QAAE,GAAG;QAAM,MAAM;MAAY,GAC7B,QAAA;IAEH;IACD,aAAaC,UAA2D;AACtE,aAAO,eAAe;QAAE,GAAG;QAAM,MAAM;MAAgB,GAAE,QAAA;IAC1D;IACD,oBAAoB,QAAQ;AAC1B,aAAO,iBAAiB,MAAM,EAC5B,OACD,CAAA;IACF;EACF;AAED,SAAO;AACR;AAED,SAAS,eACPC,QACAC,UACA;AACA,QAAM,eAAe,iBAAiB,QAAQ;IAC5C;IACA,aAAa,CACX,eAAe,kBAAkB,MAAM;AACrC,YAAM,OAAO,MAAM,SAAS,IAAA;AAC5B,aAAO;QACL,QAAQ;QACR,IAAI;QACJ;QACA,KAAK,KAAK;MACX;IACF,CACF;EACF,CAAA;AACD,QAAMC,OAA6B;IACjC,GAAG,aAAa;IAChB,MAAM,OAAO;IACb,qBAAqB,QAAQ,aAAa,KAAK,MAAA;IAC/C,MAAM,aAAa,KAAK;IACxB,QAAQ;EACT;AAED,QAAM,SAAS,sBAAsB,aAAa,IAAA;AAClD,QAAM,iBAAiB,aAAa,KAAK;AACzC,MAAA,CAAK,eACH,QAAO;AAET,QAAM,gBAAgB,UAAU,SAAoB;AAClD,WAAO,MAAM,eAAe;MAC1B;MACA;MACM;IACP,CAAA;EACF;AAED,gBAAc,OAAO;AAErB,SAAO;AACR;AAcD,IAAM,YAAY;;;EAGhB,KAAA;AAGF,eAAe,cACbC,OACAR,MACAS,MACgC;AAChC,MAAI;AAEF,UAAM,aAAa,KAAK,YAAY,KAAA;AACpC,UAAM,SAAS,MAAM,WAAW;MAC9B,GAAG;MACH,MAAM,KAAK;MACX,OAAO,KAAK;MACZ,KAAKC,WAAiB;AACpB,cAAM,WAAW;AAQjB,eAAO,cAAc,QAAQ,GAAG,MAAM;UACpC,GAAG;UACH,MAAK,qCAAU,OAAM;YAAE,GAAG,KAAK;YAAK,GAAG,SAAS;UAAK,IAAG,KAAK;UAC7D,OAAO,YAAY,WAAW,WAAW,SAAS,QAAQ,KAAK;UAC/D,cAAa,qCAAU,gBAAe,KAAK;QAC5C,CAAA;MACF;IACF,CAAA;AAED,WAAO;EACR,SAAQ,OAAO;AACd,WAAO;MACL,IAAI;MACJ,OAAO,wBAAwB,KAAA;MAC/B,QAAQ;IACT;EACF;AACF;AAED,SAAS,sBAAsBV,MAA4C;AACzE,iBAAe,UAAUW,MAAqC;AAE5D,QAAA,CAAK,QAAA,EAAU,iBAAiB,MAC9B,OAAM,IAAI,MAAM,SAAA;AAIlB,UAAM,SAAS,MAAM,cAAc,GAAG,MAAM,IAAA;AAE5C,QAAA,CAAK,OACH,OAAM,IAAI,UAAU;MAClB,MAAM;MACN,SACE;IACH,CAAA;AAEH,QAAA,CAAK,OAAO,GAEV,OAAM,OAAO;AAEf,WAAO,OAAO;EACf;AAED,YAAU,OAAO;AACjB,YAAU,YAAY;AACtB,YAAU,OAAO,KAAK;AAGtB,SAAO;AACR;;AC/pBD,IAAaC,kBAAAA,OACJ,WAAW,eAClB,UAAU,YAEVC,OAAAlB,MAAA,WAAW,YAAX,gBAAAA,IAAoB,QAApB,gBAAAkB,IAA0B,iBAAgB,UAAA,CAAA,GACxC,sBAAW,YAAX,mBAAoB,QAApB,mBAA0B,sBAAA,CAAA,GAC1B,sBAAW,YAAX,mBAAoB,QAApB,mBAA0B;AC2F9B,IAAM,cAAN,MAAMC,aAA2D;;;;;EAK/D,UAAwD;AACtD,WAAO,IAAIA,aAAA;EAIZ;;;;;EAMD,OAAgC;AAC9B,WAAO,IAAIA,aAAA;EACZ;;;;;EAMD,OACEC,MAC2C;;AAU3C,UAAMC,SAA4B;MAChC,GAAG;MACH,aAAa,oBAAmB,6BAAM,gBAAe,kBAAA;MACrD,QACE,6BAAM,YAENrB,MAAA,WAAW,YAAX,gBAAAA,IAAoB,IAAI,iBAAgB;MAC1C,uBAAsB,6BAAM,yBAAwB;MACpD,iBAAgB,6BAAM,mBAAkB;MACxC,WAAU,6BAAM,aAAY;MAK5B,QAAQ;IACT;AAED;AAEE,YAAMsB,YAAoB,6BAAM,aAAY;AAE5C,UAAA,CAAK,aAAY,6BAAM,0BAAyB,KAC9C,OAAM,IAAI,MAAA,kGACP;IAGN;AACD,WAAO;MAKL,SAAS;MAKT,WAAW,cAA2C,EACpD,MAAM,6BAAM,YACb,CAAA;MAKD,YAAY,wBAAA;MAKZ,QAAQ,oBAA2B,MAAA;MAKnC;MAKA,qBAAqB,oBAAA;IACtB;EACF;AACF;AAMD,IAAa,WAAW,IAAI,YAAA;;;ACvM5B,SAAgB,kBACdC,OACuC;AACvC,SAAO,iBAAiB;AACzB;AAED,SAAS,oBAAoBC,KAA6C;AACxE,SACE,SAAS,GAAA,KACT,SAAS,IAAI,OAAA,CAAA,KAAS,OACf,IAAI,OAAA,EAAS,MAAA,MAAY,YAAA,OACzB,IAAI,OAAA,EAAS,SAAA,MAAe;AAEtC;AAED,SAAS,2BAA2BC,KAAcC,UAA0B;AAC1E,MAAA,OAAW,QAAQ,SACjB,QAAO;AAET,MAAI,SAAS,GAAA,KAAI,OAAW,IAAI,SAAA,MAAe,SAC7C,QAAO,IAAI,SAAA;AAEb,SAAO;AACR;AAED,IAAa,kBAAb,MAAaC,yBACH,MAEV;EAaE,YACEC,SACAC,MAKA;;AACA,UAAM,QAAQ,6BAAM;AAIpB,UAAM,SAAS,EAAE,MAAO,CAAA;AAtBD;AACT;AACA;AAMT;;;;;AAgBL,SAAK,OAAO,6BAAM;AAElB,SAAK,QAAQ;AACb,SAAK,SAAQC,MAAA,6BAAM,WAAN,gBAAAA,IAAc;AAC3B,SAAK,QAAOC,MAAA,6BAAM,WAAN,gBAAAA,IAAc,MAAM;AAChC,SAAK,OAAO;AAEZ,WAAO,eAAe,MAAMJ,iBAAgB,SAAA;EAC7C;EAED,OAAc,KACZK,QACAC,OAA2C,CAAE,GACR;AACrC,UAAM,QAAQ;AAEd,QAAI,kBAAkB,KAAA,GAAQ;AAC5B,UAAI,KAAK,KAEP,OAAM,OAAO;QACX,GAAG,MAAM;QACT,GAAG,KAAK;MACT;AAEH,aAAO;IACR;AACD,QAAI,oBAAoB,KAAA,EACtB,QAAO,IAAIN,iBAAgB,MAAM,MAAM,SAAS;MAC9C,GAAG;MACH,QAAQ;IACT,CAAA;AAEH,WAAO,IAAIA,iBACT,2BAA2B,OAAO,eAAA,GAClC;MACE,GAAG;MACI;IACR,CAAA;EAEJ;AACF;;;ACpED,SAAgB,eACdO,aAIyB;AACzB,QAAM,eACJ;AACF,MAAA,CAAK,aACH,QAAO;IACL,OAAO;MACL,WAAW,CAAC,SAAS;MACrB,aAAa,CAAC,SAAS;IACxB;IACD,QAAQ;MACN,WAAW,CAAC,SAAS;MACrB,aAAa,CAAC,SAAS;IACxB;EACF;AAEH,MAAI,WAAW,aACb,QAAO;AAET,SAAO;IACL,OAAO;IACP,QAAQ;EACT;AACF;;;ACvED,IAAMC,cAAa,CAACC,OAAAA,OAAoC,OAAO;AAE/D,SAAgB,SACdC,iBACY;AACZ,MAAI,gBACF,QAAO;AAGT,MAAA,OAAW,WAAW,eAAeF,YAAW,OAAO,KAAA,EACrD,QAAO,OAAO;AAGhB,MAAA,OAAW,eAAe,eAAeA,YAAW,WAAW,KAAA,EAC7D,QAAO,WAAW;AAGpB,QAAM,IAAI,MAAM,+BAAA;AACjB;ACsBD,SAAgB,uBACdG,MACyB;AACzB,SAAO;IACL,KAAK,KAAK,IAAI,SAAA;IACd,OAAO,KAAK;IACZ,aAAa,eAAe,KAAK,WAAA;IACjC,gBAAgB,KAAK;EACtB;AACF;AAGD,SAAS,YAAYC,OAAkB;AACrC,QAAMC,OAAgC,CAAE;AACxC,WAAS,QAAQ,GAAG,QAAQ,MAAM,QAAQ,SAAS;AACjD,UAAM,UAAU,MAAM,KAAA;AACtB,SAAK,KAAA,IAAS;EACf;AACD,SAAO;AACR;AAED,IAAM,SAAS;EACb,OAAO;EACP,UAAU;EACV,cAAc;AACf;AAcD,SAAgB,SAASC,MAAuB;AAC9C,SAAO,WAAW,OACd,KAAK,YAAY,MAAM,UAAU,KAAK,KAAA,IACtC,YACE,KAAK,OAAO,IAAI,CAAC,WAAW,KAAK,YAAY,MAAM,UAAU,MAAA,CAAO,CAAC;AAE5E;AAmBD,IAAaC,SAAiB,CAAC,SAAS;AACtC,QAAM,QAAQ,KAAK,IAAI,MAAM,GAAA;AAC7B,QAAM,OAAO,MAAM,CAAA,EAAG,QAAQ,OAAO,EAAA;AAErC,MAAI,MAAM,OAAO,MAAM,KAAK;AAC5B,QAAMC,aAAuB,CAAE;AAE/B,MAAI,MAAM,CAAA,EACR,YAAW,KAAK,MAAM,CAAA,CAAA;AAExB,MAAI,YAAY,KACd,YAAW,KAAK,SAAA;AAElB,MAAI,KAAK,SAAS,WAAW,KAAK,SAAS,gBAAgB;AACzD,UAAM,QAAQ,SAAS,IAAA;AACvB,QAAI,UAAA,UAAuB,KAAK,mBAAmB,OACjD,YAAW,KAAA,SAAc,mBAAmB,KAAK,UAAU,KAAA,CAAM,CAAC,EAAC;EAEtE;AACD,MAAI,WAAW,OACb,QAAO,MAAM,WAAW,KAAK,GAAA;AAE/B,SAAO;AACR;AAED,IAAaC,UAAmB,CAAC,SAAS;AACxC,MAAI,KAAK,SAAS,WAAW,KAAK,mBAAmB,OACnD,QAAA;AAEF,QAAM,QAAQ,SAAS,IAAA;AACvB,SAAO,UAAA,SAAsB,KAAK,UAAU,KAAA,IAAM;AACnD;AAQD,IAAaC,oBAA+B,CAAC,SAAS;AACpD,SAAO,YAAY;IACjB,GAAG;IACH,mBAAmB;IACnB;IACA;EACD,CAAA;AACF;AAKD,IAAM,aAAN,cAAyB,MAAM;EAC7B,cAAc;AACZ,UAAM,OAAO;AACb,UAAM,IAAA;AACN,SAAK,OAAO;AACZ,SAAK,UAAU;EAChB;AACF;AAYD,IAAM,iBAAiB,CAACC,WAA+B;;AACrD,MAAA,EAAK,iCAAQ,SACX;AAGF,GAAAC,MAAA,OAAO,mBAAP,gBAAAA,IAAA;AAGA,MAAA,OAAW,iBAAiB,YAC1B,OAAM,IAAI,aAAa,cAAc,YAAA;AAIvC,QAAM,IAAI,WAAA;AACX;AAED,eAAsB,kBAAkBC,MAA0B;AAChE,iBAAe,KAAK,MAAA;AAEpB,QAAM,MAAM,KAAK,OAAO,IAAA;AACxB,QAAM,OAAO,KAAK,QAAQ,IAAA;AAC1B,QAAM,EAAE,KAAA,IAAS;AACjB,QAAM,kBAAkB,OAAO,YAAY;AACzC,UAAM,QAAQ,MAAM,KAAK,QAAA;AACzB,QAAI,OAAO,YAAY,MACrB,QAAO,OAAO,YAAY,KAAA;AAE5B,WAAO;EACR,GAAA;AACD,QAAM,UAAU;IACd,GAAI,KAAK,oBACL,EAAE,gBAAgB,KAAK,kBAAmB,IAC1C,CAAE;IACN,GAAI,KAAK,mBACL,EAAE,eAAe,KAAK,iBAAkB,IAAA;IAE5C,GAAG;EACJ;AAED,SAAO,SAAS,KAAK,KAAA,EAAO,KAAK;IAC/B,QAAQ,KAAK,kBAAkB,OAAO,IAAA;IACtC,QAAQ,KAAK;IACb;IACA;EACD,CAAA;AACF;AAED,eAAsB,YACpBA,MACqB;AACrB,QAAM,OAAO,CAAE;AAEf,QAAM,MAAM,MAAM,kBAAkB,IAAA;AACpC,OAAK,WAAW;AAEhB,QAAM,OAAO,MAAM,IAAI,KAAA;AAEvB,OAAK,eAAe;AAEpB,SAAO;IACC;IACN;EACD;AACF;;;ACjPD,SAAgB,YAAYC,OAAgB;AAC1C,SACE,iBAAiB,cAEjB,iBAAiB;AAEpB;AAED,SAAgB,WAAWA,OAAgB;AACzC,SAAO,iBAAiB;AACzB;AAED,SAAgB,sBAAsBA,OAAgB;AACpD,SAAO,YAAY,KAAA,KAAU,WAAW,KAAA;AACzC;ACuBD,IAAMC,qBAAgC,CAAC,SAAS;AAC9C,MAAI,WAAW,MAAM;AACnB,UAAM,EAAE,MAAA,IAAU;AAClB,QAAI,WAAW,KAAA,GAAQ;AACrB,UAAI,KAAK,SAAS,cAAc,KAAK,mBAAmB,OACtD,OAAM,IAAI,MAAM,0CAAA;AAGlB,aAAO,YAAY;QACjB,GAAG;QAEH,mBAAA;QACA;QACA,SAAS,MAAM;MAChB,CAAA;IACF;AAED,QAAI,YAAY,KAAA,GAAQ;AACtB,UAAI,KAAK,SAAS,cAAc,KAAK,mBAAmB,OACtD,OAAM,IAAI,MAAM,kDAAA;AAGlB,aAAO,YAAY;QACjB,GAAG;QACH,mBAAmB;QACnB;QACA,SAAS,MAAM;MAChB,CAAA;IACF;EACF;AAED,SAAO,kBAAkB,IAAA;AAC1B;AAKD,SAAgB,SACdC,MACmB;AACnB,QAAM,eAAe,uBAAuB,IAAA;AAC5C,SAAO,MAAM;AACX,WAAO,CAAC,EAAE,GAAA,MAAS;AACjB,aAAO,WAAW,CAAC,aAAa;AAC9B,cAAM,EAAE,MAAM,OAAO,KAAA,IAAS;AAE9B,YAAI,SAAS,eACX,OAAM,IAAI,MACR,sFAAA;AAIJ,cAAM,UAAU,mBAAmB;UACjC,GAAG;UACH;UACA;UACA;UACA,QAAQ,GAAG;UACX,UAAU;AACR,gBAAA,CAAK,KAAK,QACR,QAAO,CAAE;AAEX,gBAAA,OAAW,KAAK,YAAY,WAC1B,QAAO,KAAK,QAAQ,EAClB,GACD,CAAA;AAEH,mBAAO,KAAK;UACb;QACF,CAAA;AACD,YAAIC,OAAAA;AACJ,gBACG,KAAK,CAAC,QAAQ;AACb,iBAAO,IAAI;AACX,gBAAM,cAAc,gBAClB,IAAI,MACJ,aAAa,YAAY,MAAA;AAG3B,cAAA,CAAK,YAAY,IAAI;AACnB,qBAAS,MACP,gBAAgB,KAAK,YAAY,OAAO,EACtC,KACD,CAAA,CAAC;AAEJ;UACD;AACD,mBAAS,KAAK;YACZ,SAAS,IAAI;YACb,QAAQ,YAAY;UACrB,CAAA;AACD,mBAAS,SAAA;QACV,CAAA,EACA,MAAM,CAAC,UAAU;AAChB,mBAAS,MAAM,gBAAgB,KAAK,OAAO,EAAE,KAAM,CAAA,CAAC;QACrD,CAAA;AAEH,eAAO,MAAM;QAEZ;MACF,CAAA;IACF;EACF;AACF;;;ACxHD,IAAM,kBAAkB,MAAM;AAC5B,QAAM,IAAI,MACR,yFAAA;AAEH;AAOD,SAAgB,WACdC,aACA;AACA,MAAIC,eAAiD;AACrD,MAAIC,gBAAsD;AAE1D,QAAM,8BAA8B,MAAM;AACxC,iBAAa,aAAA;AACb,oBAAgB;AAChB,mBAAe;EAChB;AAKD,WAAS,WAAWC,OAAkC;;AACpD,UAAMC,eAA4C,CAAC,CAAE,CAAC;AACtD,QAAI,QAAQ;AACZ,WAAO,MAAM;AACX,YAAM,OAAO,MAAM,KAAA;AACnB,UAAA,CAAK,KAEH;AAEF,YAAM,YAAY,aAAa,aAAa,SAAS,CAAA;AAErD,UAAI,KAAK,SAAS;AAEhB,SAAAC,MAAA,KAAK,WAAL,gBAAAA,IAAA,WAAc,IAAI,MAAM,SAAA;AACxB;AACA;MACD;AAED,YAAM,UAAU,YAAY,SAC1B,UAAU,OAAO,IAAA,EAAM,IAAI,CAAC,OAAO,GAAG,GAAA,CAAI;AAG5C,UAAI,SAAS;AACX,kBAAU,KAAK,IAAA;AACf;AACA;MACD;AAED,UAAI,UAAU,WAAW,GAAG;AAC1B,SAAAC,MAAA,KAAK,WAAL,gBAAAA,IAAA,WAAc,IAAI,MAAM,wCAAA;AACxB;AACA;MACD;AAED,mBAAa,KAAK,CAAE,CAAA;IACrB;AACD,WAAO;EACR;AAED,WAAS,WAAW;AAClB,UAAM,eAAe,WAAW,YAAA;AAChC,gCAAA;AAGA,eAAW,SAAS,cAAc;AAChC,UAAA,CAAK,MAAM,OACT;AAEF,YAAMC,QAA6B,EACjC,MACD;AACD,iBAAW,QAAQ,MACjB,MAAK,QAAQ;AAEf,YAAM,UAAU,YAAY,MAAM,MAAM,MAAM,IAAI,CAAC,UAAU,MAAM,GAAA,CAAI;AAEvE,cACG,KAAK,OAAO,WAAW;;AACtB,cAAM,QAAQ,IACZ,OAAO,IAAI,OAAO,gBAAgB,UAAU;;AAC1C,gBAAM,OAAO,MAAM,MAAM,KAAA;AACzB,cAAI;AACF,kBAAM,QAAQ,MAAM,QAAQ,QAAQ,cAAA;AAEpC,aAAAF,MAAA,KAAK,YAAL,gBAAAA,IAAA,WAAe;UAChB,SAAQ,OAAO;AACd,aAAAC,MAAA,KAAK,WAAL,gBAAAA,IAAA,WAAc;UACf;AAED,eAAK,QAAQ;AACb,eAAK,SAAS;AACd,eAAK,UAAU;QAChB,CAAA,CAAC;AAGJ,mBAAW,QAAQ,MAAM,OAAO;AAC9B,WAAAD,MAAA,KAAK,WAAL,gBAAAA,IAAA,WAAc,IAAI,MAAM,gBAAA;AACxB,eAAK,QAAQ;QACd;MACF,CAAA,EACA,MAAM,CAAC,UAAU;;AAChB,mBAAW,QAAQ,MAAM,OAAO;AAC9B,WAAAA,MAAA,KAAK,WAAL,gBAAAA,IAAA,WAAc;AACd,eAAK,QAAQ;QACd;MACF,CAAA;IACJ;EACF;AACD,WAAS,KAAKG,KAA4B;AACxC,UAAMC,OAAgC;MACpC,SAAS;MACT;MACA,OAAO;MACP,SAAS;MACT,QAAQ;IACT;AAED,UAAM,UAAU,IAAI,QAAgB,CAAC,SAAS,WAAW;AACvD,WAAK,SAAS;AACd,WAAK,UAAU;AAEf,sCAAiB,CAAE;AACnB,mBAAa,KAAK,IAAA;IACnB,CAAA;AAED,sCAAkB,WAAW,QAAA;AAE7B,WAAO;EACR;AAED,SAAO,EACL,KACD;AACF;ACxJD,SAAgB,mBAAmB,SAA4C;AAC7E,QAAM,KAAK,IAAI,gBAAA;AAEf,QAAM,QAAQ,QAAQ;AAEtB,MAAI,eAAe;AAEnB,QAAM,UAAU,MAAM;AACpB,QAAI,EAAE,iBAAiB,MACrB,IAAG,MAAA;EAEN;AAED,aAAW,UAAU,QACnB,KAAI,iCAAQ,QACV,SAAA;MAEA,kCAAQ,iBAAiB,SAAS,SAAS,EACzC,MAAM,KACP;AAIL,SAAO,GAAG;AACX;AAQD,SAAgB,oBACX,SACU;AACb,QAAM,KAAK,IAAI,gBAAA;AAEf,aAAW,UAAU,QACnB,KAAI,iCAAQ,QACV,IAAG,MAAA;MAEH,kCAAQ,iBAAiB,SAAS,MAAM,GAAG,MAAA,GAAS,EAAE,MAAM,KAAM;AAItE,SAAO,GAAG;AACX;AAED,SAAgB,qBAAqBC,QAAqC;AACxE,SAAO,IAAI,QAAQ,CAAC,GAAG,WAAW;AAChC,QAAI,OAAO,SAAS;AAClB,aAAO,OAAO,MAAA;AACd;IACD;AACD,WAAO,iBACL,SACA,MAAM;AACJ,aAAO,OAAO,MAAA;IACf,GACD,EAAE,MAAM,KAAM,CAAA;EAEjB,CAAA;AACF;ACjDD,SAAgB,cACdC,MACmB;AACnB,QAAM,eAAe,uBAAuB,IAAA;AAC5C,QAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAM,WAAW,KAAK,YAAY;AAElC,SAAO,MAAM;AACX,UAAM,cAAc,CAClBC,SACuC;AACvC,aAAO;QACL,SAAS,UAAU;AACjB,cAAI,iBAAiB,YAAY,aAAa,SAE5C,QAAO;AAET,cAAI,SAAS,SAAS,SACpB,QAAO;AAET,gBAAM,OAAO,SAAS,IAAI,CAAC,OAAO,GAAG,IAAA,EAAM,KAAK,GAAA;AAChD,gBAAM,SAAS,SAAS,IAAI,CAAC,OAAO,GAAG,KAAA;AAEvC,gBAAM,MAAM,OAAO;YACjB,GAAG;YACH;YACA;YACA;YACA,QAAQ;UACT,CAAA;AAED,iBAAO,IAAI,UAAU;QACtB;QACD,MAAM,MAAM,UAAU;AACpB,gBAAM,OAAO,SAAS,IAAI,CAAC,OAAO,GAAG,IAAA,EAAM,KAAK,GAAA;AAChD,gBAAM,SAAS,SAAS,IAAI,CAAC,OAAO,GAAG,KAAA;AACvC,gBAAM,SAAS,gBAAgB,GAAG,SAAS,IAAI,CAAC,OAAO,GAAG,MAAA,CAAO;AAEjE,gBAAM,MAAM,MAAM,kBAAkB;YAClC,GAAG;YACH;YACA;YACA;YACA,UAAU;AACR,kBAAA,CAAK,KAAK,QACR,QAAO,CAAE;AAEX,kBAAA,OAAW,KAAK,YAAY,WAC1B,QAAO,KAAK,QAAQ,EAClB,QAAQ,SACT,CAAA;AAEH,qBAAO,KAAK;YACb;YACD;UACD,CAAA;AACD,gBAAM,UAAU,MAAM,QAAQ,IAAI,IAAA,IAC9B,IAAI,OACJ,SAAS,IAAI,MAAM,IAAI,IAAA;AAC3B,gBAAM,SAAS,QAAQ,IAAI,CAAC,UAAU;YACpC,MAAM,IAAI;YACV,MAAM;UACP,EAAA;AACD,iBAAO;QACR;MACF;IACF;AAED,UAAM,QAAQ,WAAW,YAAY,OAAA,CAAQ;AAC7C,UAAM,WAAW,WAAW,YAAY,UAAA,CAAW;AAEnD,UAAM,UAAU;MAAE;MAAO;IAAU;AACnC,WAAO,CAAC,EAAE,GAAA,MAAS;AACjB,aAAO,WAAW,CAAC,aAAa;AAE9B,YAAI,GAAG,SAAS,eACd,OAAM,IAAI,MACR,sFAAA;AAGJ,cAAM,SAAS,QAAQ,GAAG,IAAA;AAC1B,cAAM,UAAU,OAAO,KAAK,EAAA;AAE5B,YAAI,OAAA;AACJ,gBACG,KAAK,CAAC,QAAQ;AACb,iBAAO;AACP,gBAAM,cAAc,gBAClB,IAAI,MACJ,aAAa,YAAY,MAAA;AAG3B,cAAA,CAAK,YAAY,IAAI;AACnB,qBAAS,MACP,gBAAgB,KAAK,YAAY,OAAO,EACtC,MAAM,IAAI,KACX,CAAA,CAAC;AAEJ;UACD;AACD,mBAAS,KAAK;YACZ,SAAS,IAAI;YACb,QAAQ,YAAY;UACrB,CAAA;AACD,mBAAS,SAAA;QACV,CAAA,EACA,MAAM,CAAC,QAAQ;AACd,mBAAS,MACP,gBAAgB,KAAK,KAAK,EACxB,MAAM,6BAAM,KACb,CAAA,CAAC;QAEL,CAAA;AAEH,eAAO,MAAM;QAEZ;MACF,CAAA;IACF;EACF;AACF;;;AC7DD,SAASC,YAAWC,OAAmC;AACrD,MAAA,OAAW,aAAa,YAEtB,QAAO;AAET,SAAO,iBAAiB;AACzB;AAED,IAAM,WAAW;EACf,KAAK;IACH,OAAO,CAAC,UAAU,QAAS;IAC3B,UAAU,CAAC,UAAU,QAAS;IAC9B,cAAc,CAAC,UAAU,QAAS;EACnC;EACD,MAAM;IACJ,SAAS;MAEP,OAAO,CAAC,eAAe,aAAc;MAErC,UAAU,CAAC,eAAe,aAAc;MAExC,cAAc,CAAC,eAAe,aAAc;IAC7C;IACD,MAAM;MACJ,OAAO,CAAC,iBAAiB,eAAgB;MACzC,UAAU,CAAC,iBAAiB,eAAgB;MAC5C,cAAc,CAAC,iBAAiB,eAAgB;IACjD;EACF;AACF;AAED,SAAS,sBACPC,MAIA;AACA,QAAM,EAAE,WAAW,MAAM,aAAa,MAAM,IAAI,MAAA,IAAU;AAE1D,QAAMC,QAAkB,CAAE;AAC1B,QAAMC,OAAc,CAAE;AAEtB,MAAI,KAAK,cAAc,OACrB,OAAM,KAAK,cAAc,OAAO,OAAO,MAAM,MAAA,IAAU,EAAA,IAAM,IAAA;WACpD,KAAK,cAAc,QAAQ;AACpC,UAAM,CAAC,cAAc,WAAA,IAAe,SAAS,KAAK,QAAQ,IAAA;AAC1D,UAAM,CAAC,WAAW,QAAA,IAAY,SAAS,KAAK,KAAK,IAAA;AACjD,UAAM,QAAQ;AAEd,UAAM,KACJ,cAAc,OAAO,eAAe,aACpC,cAAc,OAAO,OAAO,MAC5B,MACA,cAAc,OAAO,YAAY,UAAA,IAC7B,EAAA,IACJ,MACA,KAAA;EAEH,OAAM;AAEL,UAAM,CAAC,OAAO,IAAA,IAAQ,SAAS,IAAI,IAAA;AACnC,UAAM,MAAA;yBACe,cAAc,OAAO,QAAQ,IAAA;aACzC,cAAc,OAAO,UAAU,OAAA;;;AAIxC,UAAM,KACJ,MACA,cAAc,OAAO,OAAO,MAC5B,MAAA,IACI,EAAA,IAAG,KACF,IAAA,MACL,IAAA;AAEF,SAAK,KACH,KAAA,GACG,GAAA,wBAAI,GACJ,GAAA,wBAAI;EAEV;AAED,MAAI,cAAc,KAChB,MAAK,KAAK,cAAc;IAAE;IAAO,SAAS,KAAK;EAAS,IAAG,EAAE,MAAO,CAAA;MAEpE,MAAK,KAAK;IACR;IACA,QAAQ,KAAK;IACb,WAAW,KAAK;IAChB,GAAI,eAAe,EAAE,SAAS,KAAK,QAAS;EAC7C,CAAA;AAGH,SAAO;IAAE;IAAO;EAAM;AACvB;AAGD,IAAM,gBACJ,CAA4B,EAC1B,IAAI,SACJ,YAAY,OACZ,YAAA,MAMF,CAAC,UAAU;AACT,QAAM,WAAW,MAAM;AACvB,QAAM,QAAQJ,YAAW,QAAA,IACrB,OAAO,YAAY,QAAA,IACnB;AAEJ,QAAM,EAAE,OAAO,KAAA,IAAS,sBAAsB;IAC5C,GAAG;IACH;IACA;IACA;EACD,CAAA;AAED,QAAMK,KACJ,MAAM,cAAc,UACpB,MAAM,WACL,MAAM,kBAAkB,SACtB,WAAW,MAAM,OAAO,UAAU,MAAM,OAAO,OAAO,SACrD,UACA;AAEN,IAAE,EAAA,EAAI,MAAM,MAAM,CAAC,MAAM,KAAK,GAAA,CAAK,EAAC,OAAO,IAAA,CAAK;AACjD;AAKH,SAAgB,WACdC,OAAmC,CAAE,GAClB;AACnB,QAAM,EAAE,UAAU,MAAM,KAAA,IAAS;AAEjC,QAAM,YACJ,KAAK,cAAA,OAAqB,WAAW,cAAc,SAAS;AAC9D,QAAM,cAAc,KAAK,eAAe,cAAc;AACtD,QAAM,EACJ,SAAS,cAAc;IAAE,GAAG,KAAK;IAAS;IAAW;EAAa,CAAA,EAAC,IACjE;AAEJ,SAAO,MAAM;AACX,WAAO,CAAC,EAAE,IAAI,KAAA,MAAW;AACvB,aAAO,WAAW,CAAC,aAAa;AAE9B,YAAI,QAAQ;UAAE,GAAG;UAAI,WAAW;QAAM,CAAA,EACpC,QAAO;UACL,GAAG;UACH,WAAW;QACZ,CAAA;AAEH,cAAM,mBAAmB,KAAK,IAAA;AAC9B,iBAAS,UACPC,QAGA;AACA,gBAAM,YAAY,KAAK,IAAA,IAAQ;AAE/B,cAAI,QAAQ;YAAE,GAAG;YAAI,WAAW;YAAQ;UAAQ,CAAA,EAC9C,QAAO;YACL,GAAG;YACH,WAAW;YACX;YACA;UACD,CAAA;QAEJ;AACD,eAAO,KAAK,EAAA,EACT,KACC,IAAI;UACF,KAAK,QAAQ;AACX,sBAAU,MAAA;UACX;UACD,MAAM,QAAQ;AACZ,sBAAU,MAAA;UACX;QACF,CAAA,CAAC,EAEH,UAAU,QAAA;MACd,CAAA;IACF;EACF;AACF;;;AC1MD,IAAaC,eAA4B;EACvC,SAAS;EACT,SAAS;AACV;AASD,IAAaC,oBAAsC;EACjD,SAAS;EACT,eAAe;EACf,YAAY;AACb;AAOD,IAAa,qBAAqB,CAACC,iBAAyB;AAC1D,SAAO,iBAAiB,IAAI,IAAI,KAAK,IAAI,MAAO,KAAK,cAAc,GAAA;AACpE;ACpFD,IAAa,WAAW,CACtBC,UACG,SACG;AACN,SAAA,OAAc,UAAU,aACnB,MAAgC,GAAG,IAAA,IACpC;AACL;ACHD,IAAa,2BAAb,MAAaC,kCAAiC,MAAM;EAClD,YAAYC,MAA4C;AACtD,UAAM,KAAK,SAAS,EAClB,OAAO,KAAK,MACb,CAAA;AACD,SAAK,OAAO;AACZ,WAAO,eAAe,MAAMD,0BAAyB,SAAA;EACtD;AACF;AAMD,IAAa,oBAAb,MAA+B;EAG7B,YACmBE,WACAC,WACjB;AALM;AAGW,SAAA,YAAA;AACA,SAAA,YAAA;EACf;;;;;EAMG,QAAQ;AACb,QAAA,CAAK,KAAK,QAAS;AAEnB,iBAAa,KAAK,OAAA;AAClB,SAAK,UAAU,WAAW,KAAK,WAAW,KAAK,SAAA;EAChD;EAEM,QAAQ;AACb,iBAAa,KAAK,OAAA;AAClB,SAAK,UAAU,WAAW,KAAK,WAAW,KAAK,SAAA;EAChD;EAEM,OAAO;AACZ,iBAAa,KAAK,OAAA;AAClB,SAAK,UAAA;EACN;AACF;AAGD,SAAgBC,iBAAmB;AACjC,MAAIC;AACJ,MAAIC;AACJ,QAAM,UAAU,IAAI,QAAW,CAAC,KAAK,QAAQ;AAC3C,cAAU;AACV,aAAS;EACV,CAAA;AAGD,SAAO;IAAE;IAAkB;IAAkB;EAAS;AACvD;AAOD,eAAsB,WAAWC,YAA4C;AAC3E,QAAM,MAAM,MAAM,SAAS,WAAW,GAAA;AAEtC,MAAA,CAAK,WAAW,iBAAkB,QAAO;AAGzC,QAAM,SAAS,IAAI,SAAS,GAAA,IAAO,MAAM;AACzC,QAAM,mBAAA,GAAsB,MAAA;AAE5B,SAAO,MAAM;AACd;AAED,eAAsB,uBACpBC,kBACA;AACA,QAAMC,UAAuC;IAC3C,QAAQ;IACR,MAAM,MAAM,SAAS,gBAAA;EACtB;AAED,SAAO,KAAK,UAAU,OAAA;AACvB;ACzDD,IAAa,iBAAb,MAA4B;EAA5B;AAIU;;;4CAAmB,IAAI,MAAA;AAOvB;;;;;2CAA8C,CAAE;;;;;;;;;;EAUjD,SAASC,SAAoCC,WAAuB;AACzE,UAAM,EAAE,SAAS,KAAK,QAAA,IAAYP,eAAA;AAElC,SAAK,iBAAiB,KAAK;MACzB,IAAI,OAAO,QAAQ,EAAA;MACnB;MACA;MACA,WAAW;QACT,MAAM,UAAU;QAChB,UAAU,MAAM;AACd,oBAAU,SAAA;AACV,kBAAA;QACD;QACD,OAAO,CAAC,MAAM;AACZ,oBAAU,MAAM,CAAA;AAChB,kBAAA;QACD;MACF;IACF,CAAA;AAED,WAAO,MAAM;AACX,WAAK,OAAO,QAAQ,EAAA;AACpB,gBAAU,SAAA;AACV,cAAA;IACD;EACF;;;;EAKM,OAAOQ,WAA0B;AACtC,QAAI,cAAc,KAAM;AAExB,SAAK,mBAAmB,KAAK,iBAAiB,OAC5C,CAAC,EAAE,GAAA,MAAS,OAAO,OAAO,SAAA,CAAU;AAEtC,WAAO,KAAK,gBAAgB,OAAO,SAAA,CAAU;EAC9C;;;;;;;;;EAUM,QAAQ;AACb,UAAM,WAAW,KAAK;AACtB,SAAK,mBAAmB,CAAE;AAE1B,eAAW,WAAW,SACpB,MAAK,gBAAgB,QAAQ,EAAA,IAAM;AAErC,WAAO;EACR;;;;;EAMM,qBAAqB;AAC1B,WAAO,OAAO,OAAO,KAAK,eAAA;EAC3B;;;;EAKM,kBAAkBA,WAA0B;AACjD,QAAI,cAAc,KAAM,QAAO;AAE/B,WAAO,KAAK,gBAAgB,OAAO,SAAA,CAAU;EAC9C;;;;EAKM,sBAAsB;AAC3B,WAAO,KAAK;EACb;;;;;;EAOM,cAAc;AACnB,WAAO,CACL,GAAG,KAAK,oBAAA,EAAsB,IAAI,CAAC,aAAa;MAC9C,OAAO;MACP,SAAS,QAAQ;MACjB,KAAK,QAAQ;MACb,WAAW,QAAQ;IACpB,EAAA,GACD,GAAG,KAAK,mBAAA,EAAqB,IAAI,CAAC,aAAa;MAC7C,OAAO;MACP,SAAS,QAAQ;MACjB,KAAK,QAAQ;MACb,WAAW,QAAQ;IACpB,EAAA,CACF;EACF;;;;EAKM,qBAAqB;AAC1B,WAAO,KAAK,mBAAA,EAAqB,SAAS;EAC3C;;;;EAKM,0BAA0B;AAC/B,WAAO,KAAK,mBAAA,EAAqB,KAC/B,CAAC,YAAY,QAAQ,QAAQ,WAAW,cAAA;EAE3C;;;;EAKM,sBAAsB;AAC3B,WAAO,KAAK,iBAAiB,SAAS;EACvC;AACF;AC7KD,SAAS,YAAYC,IAAe;AAClC,QAAM,EAAE,SAAS,SAAS,OAAA,IAAWT,eAAA;AAErC,KAAG,iBAAiB,QAAQ,MAAM;AAChC,OAAG,oBAAoB,SAAS,MAAA;AAChC,YAAA;EACD,CAAA;AACD,KAAG,iBAAiB,SAAS,MAAA;AAE7B,SAAO;AACR;AA0BD,SAAS,kBACPS,IACA,EAAE,YAAY,cAAA,GACd;AACA,MAAIC;AACJ,MAAIC;AAEJ,WAAS,QAAQ;AACf,kBAAc,WAAW,MAAM;AAC7B,SAAG,KAAK,MAAA;AACR,oBAAc,WAAW,MAAM;AAC7B,WAAG,MAAA;MACJ,GAAE,aAAA;IACJ,GAAE,UAAA;EACJ;AAED,WAAS,QAAQ;AACf,iBAAa,WAAA;AACb,UAAA;EACD;AAED,WAAS,OAAO;AACd,iBAAa,WAAA;AACb,UAAA;EACD;AAED,KAAG,iBAAiB,QAAQ,KAAA;AAC5B,KAAG,iBAAiB,WAAW,CAAC,EAAE,KAAA,MAAW;AAC3C,iBAAa,WAAA;AACb,UAAA;AAEA,QAAI,SAAS,OACX,MAAA;EAEH,CAAA;AACD,KAAG,iBAAiB,SAAS,MAAM;AACjC,iBAAa,WAAA;AACb,iBAAa,WAAA;EACd,CAAA;AACF;;AAcD,IAAa,gBAAbC,MAAA,MAA0B;EASxB,YAAYC,MAAkC;AAPvC,8BAAK,EAAED,IAAa;AAEV;AACA;AACA;AACD,wCAAe,gBAAkC,IAAA;AAqDzD;;;;;;;;;uCAAoC;AAlD1C,SAAK,oBAAoB,KAAK,qBAAqB;AACnD,QAAA,CAAK,KAAK,kBACR,OAAM,IAAI,MACR,8IAAA;AAIJ,SAAK,aAAa,KAAK;AACvB,SAAK,gBAAgB,KAAK;EAC3B;EAED,IAAW,KAAK;AACd,WAAO,KAAK,aAAa,IAAA;EAC1B;EAED,IAAY,GAAG,IAAI;AACjB,SAAK,aAAa,KAAK,EAAA;EACxB;;;;EAKM,SAAoC;AACzC,WAAA,CAAA,CACI,KAAK,MACP,KAAK,GAAG,eAAe,KAAK,kBAAkB,QAAA,CAC7C,KAAK;EAET;;;;EAKM,WAAsC;AAC3C,WAAA,CAAA,CACI,KAAK,OACN,KAAK,GAAG,eAAe,KAAK,kBAAkB,WAC7C,KAAK,GAAG,eAAe,KAAK,kBAAkB;EAEnD;EAYD,MAAa,OAAO;AAClB,QAAI,KAAK,YAAa,QAAO,KAAK;AAElC,SAAK,KAAK,EAAEA,IAAa;AACzB,UAAM,YAAY,WAAW,KAAK,UAAA,EAAY,KAC5C,CAAC,QAAQ,IAAI,KAAK,kBAAkB,GAAA,CAAA;AAEtC,SAAK,cAAc,UAAU,KAAK,OAAO,OAAO;AAC9C,WAAK,KAAK;AAGV,SAAG,iBAAiB,WAAW,SAAU,EAAE,KAAA,GAAQ;AACjD,YAAI,SAAS,OACX,MAAK,KAAK,MAAA;MAEb,CAAA;AAED,UAAI,KAAK,cAAc,QACrB,mBAAkB,IAAI,KAAK,aAAA;AAG7B,SAAG,iBAAiB,SAAS,MAAM;AACjC,YAAI,KAAK,OAAO,GACd,MAAK,KAAK;MAEb,CAAA;AAED,YAAM,YAAY,EAAA;AAElB,UAAI,KAAK,WAAW,iBAClB,IAAG,KAAK,MAAM,uBAAuB,KAAK,WAAW,gBAAA,CAAiB;IAEzE,CAAA;AAED,QAAI;AACF,YAAM,KAAK;IACZ,UAAA;AACC,WAAK,cAAc;IACpB;EACF;;;;;EAMD,MAAa,QAAQ;;AACnB,QAAI;AACF,YAAM,KAAK;IACZ,UAAA;AACC,OAAAA,MAAA,KAAK,OAAL,gBAAAA,IAAS;IACV;EACF;AACF,GAhHC,cADFA,KACS,gBAAe,IADxBA;AAsHA,SAAgB,sBAAsBE,YAA0B;AAC9D,MAAI,WAAW,OAAA,EACb,QAAO;IACL,IAAI,WAAW;IACf,OAAO;IACP,IAAI,WAAW;EAChB;AAGH,MAAI,WAAW,SAAA,EACb,QAAO;IACL,IAAI,WAAW;IACf,OAAO;IACP,IAAI,WAAW;EAChB;AAGH,MAAA,CAAK,WAAW,GACd,QAAO;AAGT,SAAO;IACL,IAAI,WAAW;IACf,OAAO;IACP,IAAI,WAAW;EAChB;AACF;ACrND,IAAa,WAAb,MAAsB;EAmBpB,YAAYC,MAA8B;AAf1B;;;;AAIR,0CAAiB;AACjB,0CAAiB,IAAI,eAAA;AACZ;AACA;AACT;AACS;AAIA;AAqMT;;;;;wCAAqC;AAjM3C,SAAK,YAAY;MACf,QAAQ,KAAK;MACb,SAAS,KAAK;MACd,SAAS,KAAK;IACf;AAED,UAAM,cAAc;MAClB,GAAG;MACH,GAAG,KAAK;IACT;AAGD,SAAK,oBAAoB,IAAI,kBAAkB,MAAM;AACnD,UACE,KAAK,eAAe,oBAAA,KACpB,KAAK,eAAe,mBAAA,GACpB;AACA,aAAK,kBAAkB,MAAA;AACvB;MACD;AAED,WAAK,MAAA,EAAQ,MAAM,MAAM,IAAA;IAC1B,GAAE,YAAY,OAAA;AAGf,SAAK,mBAAmB,IAAI,aAAa;MACvC,mBAAmB,KAAK;MACxB,YAAY;MACZ,WAAW;QACT,GAAG;QACH,GAAG,KAAK;MACT;IACF,CAAA;AACD,SAAK,iBAAiB,aAAa,UAAU,EAC3C,MAAM,CAAC,OAAO;AACZ,UAAA,CAAK,GAAI;AACT,WAAK,wBAAwB,EAAA;IAC9B,EACF,CAAA;AACD,SAAK,sBAAsB,KAAK,gBAAgB;AAEhD,SAAK,WAAW,YAAY;AAE5B,SAAK,kBAAkB,gBAErB;MACA,MAAM;MACN,OAAO,YAAY,UAAU,SAAS;MACtC,OAAO;IACR,CAAA;AAGD,QAAA,CAAK,KAAK,SACR,MAAK,KAAA,EAAO,MAAM,MAAM,IAAA;EAE3B;;;;;EAMD,MAAc,OAAO;AACnB,SAAK,iBAAiB;AACtB,QAAI,KAAK,gBAAgB,IAAA,EAAM,UAAU,aACvC,MAAK,gBAAgB,KAAK;MACxB,MAAM;MACN,OAAO;MACP,OAAO;IACR,CAAA;AAGH,QAAI;AACF,YAAM,KAAK,iBAAiB,KAAA;IAC7B,SAAQ,OAAO;AACd,WAAK,UACH,IAAI,yBAAyB;QAC3B,SAAS;QACT,OAAO;MACR,CAAA,CAAA;AAEH,aAAO,KAAK;IACb;EACF;;;;;EAMD,MAAa,QAAQ;AACnB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,KAAA;AAEvB,UAAMC,kBAAmC,CAAE;AAC3C,eAAW,WAAW,KAAK,eAAe,YAAA,EACxC,KAAI,QAAQ,QAAQ,WAAW,eAC7B,SAAQ,UAAU,SAAA;aACT,QAAQ,UAAU,WAC3B,SAAQ,UAAU,MAChB,gBAAgB,KACd,IAAI,yBAAyB,EAC3B,SAAS,2CACV,CAAA,CAAA,CACF;QAGH,iBAAgB,KAAK,QAAQ,GAAA;AAIjC,UAAM,QAAQ,IAAI,eAAA,EAAiB,MAAM,MAAM,IAAA;AAC/C,UAAM,KAAK,iBAAiB,MAAA,EAAQ,MAAM,MAAM,IAAA;AAEhD,SAAK,gBAAgB,KAAK;MACxB,MAAM;MACN,OAAO;MACP,OAAO;IACR,CAAA;EACF;;;;;;;;;;;EAYM,QAAQ,EACb,IAAI,EAAE,IAAI,MAAM,MAAM,OAAO,OAAA,GAC7B,aACA,YAAA,GAKC;AACD,WAAO,WAGL,CAAC,aAAa;AACd,YAAM,QAAQ,KAAK,UACjB;QACE;QACA,QAAQ;QACR,QAAQ;UACN,OAAO,YAAY,MAAM,UAAU,KAAA;UACnC;UACA;QACD;MACF,GACD;QACE,GAAG;QACH,KAAK,OAAO;AACV,gBAAM,cAAc,gBAAgB,OAAO,YAAY,MAAA;AAEvD,cAAA,CAAK,YAAY,IAAI;AACnB,qBAAS,MAAM,gBAAgB,KAAK,YAAY,KAAA,CAAM;AACtD;UACD;AAED,mBAAS,KAAK,EACZ,QAAQ,YAAY,OACrB,CAAA;QACF;MACF,CAAA;AAGH,aAAO,MAAM;AACX,cAAA;AAEA,YAAI,SAAS,kBAAkB,KAAK,iBAAiB,OAAA,EACnD,MAAK,KAAK;UACR;UACA,QAAQ;QACT,CAAA;AAGH,yCAAQ,oBAAoB,SAAS;MACtC;IACF,CAAA;EACF;EAED,IAAW,aAAa;AACtB,WAAO,sBAAsB,KAAK,gBAAA;EACnC;EAQO,UAAUC,aAAuC;AACvD,SAAK,gBAAgB,KAAK;MACxB,MAAM;MACN,OAAO;MACP,OAAO,gBAAgB,KAAK,WAAA;IAC7B,CAAA;AACD,QAAI,KAAK,aAAc;AAEvB,UAAM,eAAe,OAAOvB,iBAAyB;AACnD,UAAI;AACF,cAAM,MAAM,KAAK,oBAAoB,YAAA,CAAa;AAClD,YAAI,KAAK,gBAAgB;AACvB,gBAAM,KAAK,iBAAiB,MAAA;AAC5B,gBAAM,KAAK,iBAAiB,KAAA;AAE5B,cAAI,KAAK,eAAe,mBAAA,EACtB,MAAK,KACH,KAAK,eACF,mBAAA,EACA,IAAI,CAAC,EAAE,QAAA,MAAc,OAAA,CAAQ;QAGrC;AACD,aAAK,eAAe;MACrB,QAAO;AACN,cAAM,aAAa,eAAe,CAAA;MACnC;IACF;AAED,SAAK,eAAe,aAAa,CAAA;EAClC;EAEO,wBAAwBe,IAAe;AAC7C,UAAM,qBAAqB,CAACS,UAAmB;AAC7C,YAAM,OAAO,KAAK,eAAe,mBAAA;AACjC,iBAAW,EAAE,SAAS,UAAA,KAAe,MAAM;AACzC,YAAI,QAAQ,WAAW,eAAgB;AAEvC,kBAAU,MACR,gBAAgB,KACd,SACE,IAAI,yBAAyB;UAC3B,SAAS;UACT;QACD,CAAA,CAAA,CACJ;AAEH,aAAK,eAAe,OAAO,QAAQ,EAAA;MACpC;IACF;AAED,OAAG,iBAAiB,QAAQ,MAAM;AAChC,UAAI,YAAY;;AACd,YAAI,KAAK,SACP,MAAK,kBAAkB,MAAA;AAGzB,SAAAC,OAAAP,MAAA,KAAK,WAAU,WAAf,gBAAAO,IAAA,KAAAP;AAEA,aAAK,gBAAgB,KAAK;UACxB,MAAM;UACN,OAAO;UACP,OAAO;QACR,CAAA;MACF,CAAA,EAAE,MAAM,CAAC,UAAU;AAClB,WAAG,MAAM,GAAA;AACT,2BAAmB,KAAA;MACpB,CAAA;IACF,CAAA;AAED,OAAG,iBAAiB,WAAW,CAAC,EAAE,KAAA,MAAW;AAC3C,WAAK,kBAAkB,MAAA;AAEvB,UAAA,OAAW,SAAS,YAAY,CAAC,QAAQ,MAAO,EAAC,SAAS,IAAA,EAAO;AAEjE,YAAM,kBAAkB,KAAK,MAAM,IAAA;AACnC,UAAI,YAAY,iBAAiB;AAC/B,aAAK,sBAAsB,eAAA;AAC3B;MACD;AAED,WAAK,sBAAsB,eAAA;IAC5B,CAAA;AAED,OAAG,iBAAiB,SAAS,CAAC,UAAU;;AACtC,yBAAmB,KAAA;AACnB,OAAAO,OAAAP,MAAA,KAAK,WAAU,YAAf,gBAAAO,IAAA,KAAAP,KAAyB;AAEzB,UAAA,CAAK,KAAK,YAAY,KAAK,eAAe,wBAAA,EACxC,MAAK,UACH,IAAI,yBAAyB;QAC3B,SAAS;QACT,OAAO;MACR,CAAA,CAAA;IAGN,CAAA;AAED,OAAG,iBAAiB,SAAS,CAAC,UAAU;;AACtC,yBAAmB,KAAA;AACnB,OAAAO,OAAAP,MAAA,KAAK,WAAU,YAAf,gBAAAO,IAAA,KAAAP,KAAyB;AAEzB,WAAK,UACH,IAAI,yBAAyB;QAC3B,SAAS;QACT,OAAO;MACR,CAAA,CAAA;IAEJ,CAAA;EACF;EAEO,sBAAsBQ,SAA8B;AAC1D,UAAM,UAAU,KAAK,eAAe,kBAAkB,QAAQ,EAAA;AAC9D,QAAA,CAAK,QAAS;AAEd,YAAQ,UAAU,KAAK,OAAA;AAEvB,QAAI,YAAY;AAChB,QAAI,YAAY,WAAW,QAAQ,QAAQ,WAAW,gBAAgB;AACpE,UAAI,QAAQ,OAAO,SAAS,OAC1B,SAAQ,QAAQ,OAAO,cAAc,QAAQ,OAAO;AAGtD,UAAI,QAAQ,OAAO,SAAS,UAC1B,aAAY;IAEf;AAED,QAAI,WAAW;AACb,cAAQ,UAAU,SAAA;AAClB,WAAK,eAAe,OAAO,QAAQ,EAAA;IACpC;EACF;EAEO,sBAAsBC,SAAoC;AAChE,QAAI,QAAQ,WAAW,YACrB,MAAK,UACH,IAAI,yBAAyB,EAC3B,SAAS,6BACV,CAAA,CAAA;EAGN;;;;EAKO,KACNC,mBACA;AACA,QAAA,CAAK,KAAK,iBAAiB,OAAA,EACzB,OAAM,IAAI,MAAM,+BAAA;AAGlB,UAAM,WACJ,6BAA6B,QACzB,oBACA,CAAC,iBAAkB;AACzB,SAAK,iBAAiB,GAAG,KACvB,KAAK,UAAU,SAAS,WAAW,IAAI,SAAS,CAAA,IAAK,QAAA,CAAS;EAEjE;;;;;;EAOO,UAAUhB,SAAoCC,WAAuB;AAC3E,SAAK,kBAAkB,MAAA;AAEvB,QAAI,YAAY;AACd,UAAA,CAAK,KAAK,iBAAiB,OAAA,EACzB,OAAM,KAAK,KAAA;AAEb,YAAM,MAAM,CAAA;AAEZ,UAAA,CAAK,KAAK,eAAe,oBAAA,EAAuB;AAEhD,WAAK,KAAK,KAAK,eAAe,MAAA,EAAQ,IAAI,CAAC,EAAE,SAAA,UAAA,MAAcgB,SAAAA,CAAQ;IACpE,CAAA,EAAE,MAAM,CAAC,QAAQ;AAChB,WAAK,eAAe,OAAO,QAAQ,EAAA;AACnC,gBAAU,MAAM,gBAAgB,KAAK,GAAA,CAAI;IAC1C,CAAA;AAED,WAAO,KAAK,eAAe,SAAS,SAAS,SAAA;EAC9C;AACF;AC5aD,SAAgB,eAAeR,MAA8B;AAC3D,SAAO,IAAI,SAAS,IAAA;AACrB;ACaD,SAAgB,OACdS,MACmB;AACnB,QAAM,EAAE,OAAA,IAAW;AACnB,QAAM,cAAc,eAAe,KAAK,WAAA;AACxC,SAAO,MAAM;AACX,WAAO,CAAC,EAAE,GAAA,MAAS;AACjB,aAAO,WAAW,CAAC,aAAa;AAC9B,cAAM,wBACJ,GAAG,SAAS,iBACR,OAAO,gBAAgB,UAAU,EAC/B,KAAK,QAAQ;AACX,mBAAS,KAAK;YACZ;YACA,SAAS,GAAG;UACb,CAAA;QACF,EACF,CAAA,IACD;AAEN,cAAM,sBAAsB,OACzB,QAAQ;UACP;UACA;QACD,CAAA,EACA,UAAU,QAAA;AAEb,eAAO,MAAM;AACX,8BAAoB,YAAA;AACpB,yEAAuB;QACxB;MACF,CAAA;IACF;EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;ACND,IAAa,oBAAb,MAA0E;EAKxE,YAAYC,MAA4C;AAJvC;AACD;AACR;AAGN,SAAK,YAAY;AAEjB,SAAK,UAAU,CAAE;AAGjB,SAAK,QAAQ,KAAK,MAAM,IAAI,CAAC,SAAS,KAAK,KAAK,OAAA,CAAQ;EACzD;EAEO,SAA8CC,MAMnD;AACD,UAAM,SAAS,YAAwC;MACrD,OAAO,KAAK;MACZ,IAAI;QACF,GAAG;QACH,SAAS,KAAK,WAAW,CAAE;QAC3B,IAAI,EAAE,KAAK;MACZ;IACF,CAAA;AACD,WAAO,OAAO,KAAK,MAAA,CAAO;EAC3B;EAED,MAAc,iBAAsDA,MAM/C;AACnB,QAAI;AACF,YAAM,OAAO,KAAK,SAA0B,IAAA;AAG5C,YAAM,WAAW,MAAM,oBAA4B,IAAA;AACnD,YAAM,OAAQ,SAAS,OAAe;AACtC,aAAO;IACR,SAAQ,KAAK;AACZ,YAAM,gBAAgB,KAAK,GAAA;IAC5B;EACF;EACM,MAAMC,MAAcC,OAAiBC,MAA2B;AACrE,WAAO,KAAK,iBAAmC;MAC7C,MAAM;MACN;MACA;MACA,SAAS,6BAAM;MACf,QAAQ,6BAAM;IACf,CAAA;EACF;EACM,SAASF,MAAcC,OAAiBC,MAA2B;AACxE,WAAO,KAAK,iBAAmC;MAC7C,MAAM;MACN;MACA;MACA,SAAS,6BAAM;MACf,QAAQ,6BAAM;IACf,CAAA;EACF;EACM,aACLF,MACAG,OACAC,MAIgB;AAChB,UAAM,cAAc,KAAK,SAAS;MAChC,MAAM;MACN;MACA;MACA,SAAS,KAAK;MACd,QAAQ,KAAK;IACd,CAAA;AACD,WAAO,YAAY,UAAU;MAC3B,KAAK,UAAU;;AACb,gBAAQ,SAAS,OAAO,MAAxB;UACE,KAAK,SAAS;AACZ,aAAAC,MAAA,KAAK,4BAAL,gBAAAA,IAAA,WAA+B,SAAS;AACxC;UACD;UACD,KAAK,WAAW;AACd,aAAAC,MAAA,KAAK,cAAL,gBAAAA,IAAA,WAAiB,EACf,SAAS,SAAS,QACnB;AACD;UACD;UACD,KAAK,WAAW;AACd,aAAAC,MAAA,KAAK,cAAL,gBAAAA,IAAA;AACA;UACD;UACD,KAAK;UACL,KAAA,QAAgB;AACd,aAAAC,MAAA,KAAK,WAAL,gBAAAA,IAAA,WAAc,SAAS,OAAO;AAC9B;UACD;QACF;MACF;MACD,MAAM,KAAK;;AACT,SAAAH,MAAA,KAAK,YAAL,gBAAAA,IAAA,WAAe;MAChB;MACD,WAAW;;AACT,SAAAA,MAAA,KAAK,eAAL,gBAAAA,IAAA;MACD;IACF,CAAA;EACF;AACF;AC7JD,SAAgB,wBACdI,MAC4B;AAC5B,SAAO,IAAI,kBAAkB,IAAA;AAC9B;AC0BD,IAAM,sBAAsB,OAAO,IAAI,oBAAA;AAqFvC,IAAMC,oBAGF;EACF,OAAO;EACP,QAAQ;EACR,WAAW;AACZ;AAGD,IAAa,gCAAgC,CAC3CC,mBACkB;AAClB,SAAO,kBAAkB,cAAA;AAC1B;AAKD,SAAgB,sBACdC,QACqB;AACrB,QAAM,QAAQ,qBAA0C,CAAC,EAAE,MAAM,KAAA,MAAW;AAC1E,UAAM,WAAW,CAAC,GAAG,IAAK;AAC1B,UAAM,gBAAgB,8BAA8B,SAAS,IAAA,CAAK;AAElE,UAAM,WAAW,SAAS,KAAK,GAAA;AAE/B,WAAQ,OAAO,aAAA,EAAuB,UAAU,GAAI,IAAA;EACrD,CAAA;AACD,SAAO,gBAAqC,CAAC,QAAQ;AACnD,QAAI,QAAQ,oBACV,QAAO;AAET,WAAO,MAAM,GAAA;EACd,CAAA;AACF;AAED,SAAgB,iBACdH,MACqB;AACrB,QAAM,SAAS,IAAI,kBAAkB,IAAA;AACrC,QAAM,QAAQ,sBAA+B,MAAA;AAC7C,SAAO;AACR;AAMD,SAAgB,iBACdI,QAC4B;AAC5B,SAAO,OAAO,mBAAA;AACf;ACvJD,SAAgB,oBACdC,MACmB;AACnB,QAAM,eAAe,uBAAuB,IAAA;AAC5C,QAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAM,WAAW,KAAK,YAAY;AAElC,SAAO,MAAM;AACX,UAAM,cAAc,CAClBC,SACuC;AACvC,aAAO;QACL,SAAS,UAAU;AACjB,cAAI,iBAAiB,YAAY,aAAa,SAE5C,QAAO;AAET,cAAI,SAAS,SAAS,SACpB,QAAO;AAET,gBAAM,OAAO,SAAS,IAAI,CAAC,OAAO,GAAG,IAAA,EAAM,KAAK,GAAA;AAChD,gBAAM,SAAS,SAAS,IAAI,CAAC,OAAO,GAAG,KAAA;AAEvC,gBAAM,MAAM,OAAO;YACjB,GAAG;YACH;YACA;YACA;YACA,QAAQ;UACT,CAAA;AAED,iBAAO,IAAI,UAAU;QACtB;QACD,MAAM,MAAM,UAAU;AACpB,gBAAM,OAAO,SAAS,IAAI,CAAC,OAAO,GAAG,IAAA,EAAM,KAAK,GAAA;AAChD,gBAAM,SAAS,SAAS,IAAI,CAAC,OAAO,GAAG,KAAA;AAEvC,gBAAM,eAAe,gBACnB,GAAG,SAAS,IAAI,CAAC,OAAO,GAAG,MAAA,CAAO;AAEpC,gBAAM,kBAAkB,IAAI,gBAAA;AAE5B,gBAAM,kBAAkB,kBAAkB;YACxC,GAAG;YACH,QAAQ,iBAAiB,cAAc,gBAAgB,MAAA;YACvD;YACA,mBAAmB;YACnB,kBAAkB;YAClB;YACA;YACA;YACA;YACA,UAAU;AACR,kBAAA,CAAK,KAAK,QACR,QAAO,CAAE;AAEX,kBAAA,OAAW,KAAK,YAAY,WAC1B,QAAO,KAAK,QAAQ,EAClB,QAAQ,SACT,CAAA;AAEH,qBAAO,KAAK;YACb;UACF,CAAA;AAED,gBAAM,MAAM,MAAM;AAClB,gBAAM,CAAC,IAAA,IAAQ,MAAM,oBAEnB;YAEA,MAAM,IAAI;YACV,aAAa,aAAa,YAAY,OAAO;YAE7C,YAAYC,QAAM;AAChB,oBAAM,QAAQA,OAAK;AACnB,qBAAO,gBAAgB,KAAK,EAC1B,MACD,CAAA;YACF;YACD;UACD,CAAA;AACD,gBAAM,WAAW,OAAO,KAAK,QAAA,EAAU,IACrC,OAAO,QAA6B;AAClC,gBAAIC,OAAqB,MAAM,QAAQ,QAAQ,KAAK,GAAA,CAAA;AAEpD,gBAAI,YAAY,MAAM;AAKpB,oBAAM,SAAS,MAAM,QAAQ,QAAQ,KAAK,MAAA;AAC1C,qBAAO,EACL,QAAQ,EACN,MAAM,MAAM,QAAQ,QAAQ,OAAO,IAAA,EACpC,EACF;YACF;AAED,mBAAO;cACL;cACA,MAAM,EACJ,UAAU,IACX;YACF;UACF,CAAA;AAEH,iBAAO;QACR;MACF;IACF;AAED,UAAM,QAAQ,WAAW,YAAY,OAAA,CAAQ;AAC7C,UAAM,WAAW,WAAW,YAAY,UAAA,CAAW;AAEnD,UAAM,UAAU;MAAE;MAAO;IAAU;AACnC,WAAO,CAAC,EAAE,GAAA,MAAS;AACjB,aAAO,WAAW,CAAC,aAAa;AAE9B,YAAI,GAAG,SAAS,eACd,OAAM,IAAI,MACR,iGAAA;AAGJ,cAAM,SAAS,QAAQ,GAAG,IAAA;AAC1B,cAAM,UAAU,OAAO,KAAK,EAAA;AAE5B,YAAI,OAAA;AACJ,gBACG,KAAK,CAAC,QAAQ;AACb,iBAAO;AACP,cAAI,WAAW,IAAI,MAAM;AACvB,qBAAS,MACP,gBAAgB,KAAK,IAAI,MAAM,EAC7B,MAAM,IAAI,KACX,CAAA,CAAC;AAEJ;UACD,WAAU,YAAY,IAAI,MAAM;AAC/B,qBAAS,KAAK;cACZ,SAAS,IAAI;cACb,QAAQ,IAAI,KAAK;YAClB,CAAA;AACD,qBAAS,SAAA;AACT;UACD;AAED,mBAAS,SAAA;QACV,CAAA,EACA,MAAM,CAAC,QAAQ;AACd,mBAAS,MACP,gBAAgB,KAAK,KAAK,EACxB,MAAM,6BAAM,KACb,CAAA,CAAC;QAEL,CAAA;AAEH,eAAO,MAAM;QAEZ;MACF,CAAA;IACF;EACF;AACF;AAKD,IAAa,+BAA+B;AC7L5C,SAAgB,wBACdd,OACAe,aACA;AACA,MAAA,CAAK,YACH,QAAO;AAET,MAAI,SAAS,QAAA,OAAe,UAAU,SACpC,QAAO;AAET,SAAO;IACL,GAAI,SAAS,CAAE;IACf;EACD;AACF;ACWD,eAAe,wBACbC,MACiB;AACjB,MAAI,MAAM,MAAM,SAAS,KAAK,GAAA;AAC9B,MAAI,KAAK,kBAAkB;AACzB,UAAM,SAAS,MAAM,SAAS,KAAK,gBAAA;AAEnC,UAAM,SAAS,IAAI,SAAS,GAAA,IAAO,MAAM;AACzC,WACE,SAAS,sBAAsB,mBAAmB,KAAK,UAAU,MAAA,CAAO;EAC3E;AAED,SAAO;AACR;AA0BD,SAAgB,qBAIdC,MAIuB;AACvB,QAAM,cAAc,eAAe,KAAK,WAAA;AAExC,SAAO,MAAM;AACX,WAAO,CAAC,EAAE,GAAA,MAAS;AACjB,aAAO,WAAW,CAAC,aAAa;AAC9B,cAAM,EAAE,MAAM,MAAM,MAAA,IAAU;AAG9B,YAAI,SAAS,eACX,OAAM,IAAI,MAAM,kDAAA;AAGlB,YAAIF,cAAAA;AACJ,cAAM,KAAK,IAAI,gBAAA;AACf,cAAM,SAAS,iBAAiB,GAAG,QAAQ,GAAG,MAAA;AAC9C,cAAM,oBAAoB,kBAOvB;UACD,KAAK,YACH,OAAO;YACL;YACA,KAAK,MAAM,wBAAwB,IAAA;YACnC,OAAO,wBAAwB,OAAO,WAAA;YACtC;YACA;YACA,QAAQ;UACT,CAAA;UACH,MAAM,MAAM,SAAS,KAAK,oBAAoB,EAAE,GAAI,CAAA;UACpD;UACA,aAAa,YAAY,OAAO;UAChC,aACE,KAAK,eACJ,WAAW;QACf,CAAA;AAED,cAAM,kBAAkB,gBAEtB;UACA,MAAM;UACN,OAAO;UACP,OAAO;QACR,CAAA;AAED,cAAM,gBAAgB,gBAAgB,UAAU,EAC9C,KAAK,OAAO;AACV,mBAAS,KAAK,EACZ,QAAQ,MACT,CAAA;QACF,EACF,CAAA;AACD,YAAI,YAAY;AACd,2BAAiB,SAAS,kBACxB,SAAQ,MAAM,MAAd;YACE,KAAK;AAEH;YACF,KAAK;AACH,oBAAM,YAAY,MAAM;AAExB,kBAAIG;AACJ,kBAAI,UAAU,IAAI;AAEhB,8BAAc,UAAU;AACxB,yBAAS;kBACP,IAAI,UAAU;kBACd,MAAM;gBACP;cACF,MACC,UAAS,EACP,MAAM,UAAU,KACjB;AAGH,uBAAS,KAAK;gBACZ;gBACA,SAAS,EACP,aAAa,MAAM,YACpB;cACF,CAAA;AACD;YACF,KAAK,aAAa;AAChB,uBAAS,KAAK;gBACZ,QAAQ,EACN,MAAM,UACP;gBACD,SAAS,EACP,aAAa,MAAM,YACpB;cACF,CAAA;AACD,8BAAgB,KAAK;gBACnB,MAAM;gBACN,OAAO;gBACP,OAAO;cACR,CAAA;AACD;YACD;YACD,KAAK,oBAAoB;AACvB,oBAAM,QAAQ,gBAAgB,KAAK,EAAE,OAAO,MAAM,MAAO,CAAA;AAEzD,kBAAI,kBAAkB,SAAS,MAAM,MAAM,IAAA,GAAO;AAEhD,gCAAgB,KAAK;kBACnB,MAAM;kBACN,OAAO;kBACP;gBACD,CAAA;AACD;cACD;AAGD,oBAAM;YACP;YACD,KAAK,cAAc;AACjB,oBAAM,YAAY,gBAAgB,IAAA;AAElC,oBAAM,QAAQ,MAAM,SAAS,gBAAgB,KAAK,MAAM,KAAA;AACxD,kBAAA,CAAK,SAAS,UAAU,UAAU,aAChC;AAGF,8BAAgB,KAAK;gBACnB,MAAM;gBACN,OAAO;gBACP;cACD,CAAA;AACD;YACD;YACD,KAAK;AACH,8BAAgB,KAAK;gBACnB,MAAM;gBACN,OAAO;gBACP,OAAO,IAAI,gBAAA,cACK,MAAM,EAAA,yCAAG;cAE1B,CAAA;UAEJ;AAEH,mBAAS,KAAK,EACZ,QAAQ,EACN,MAAM,UACP,EACF,CAAA;AACD,0BAAgB,KAAK;YACnB,MAAM;YACN,OAAO;YACP,OAAO;UACR,CAAA;AACD,mBAAS,SAAA;QACV,CAAA,EAAE,MAAM,CAAC,UAAU;AAClB,mBAAS,MAAM,gBAAgB,KAAK,KAAA,CAAM;QAC3C,CAAA;AAED,eAAO,MAAM;AACX,mBAAS,SAAA;AACT,aAAG,MAAA;AACH,wBAAc,YAAA;QACf;MACF,CAAA;IACF;EACF;AACF;AAKD,IAAa,gCAAgC;AC9M7C,SAAgB,UACdC,MACuB;AAEvB,SAAO,MAAM;AAEX,WAAO,CAAC,aAAa;AAEnB,aAAO,WAAW,CAAC,aAAa;AAC9B,YAAIC;AACJ,YAAIC,kBAAAA;AAGJ,YAAIN,cAAAA;AAEJ,gBAAQ,CAAA;AAER,iBAAS,oBAAoB;AAC3B,gBAAM,KAAK,SAAS;AACpB,cAAA,CAAK,YACH,QAAO;AAGT,iBAAO;YACL,GAAG;YACH,OAAO,wBAAwB,GAAG,OAAO,WAAA;UAC1C;QACF;AAED,iBAAS,QAAQO,UAAkB;AACjC,gBAAM,KAAK,kBAAA;AAEX,kBAAQ,SAAS,KAAK,EAAA,EAAI,UAAU;YAClC,MAAM,OAAO;;AACX,oBAAM,cAAc,KAAK,MAAM;gBAC7B;gBACA;gBACA;cACD,CAAA;AACD,kBAAA,CAAK,aAAa;AAChB,yBAAS,MAAM,KAAA;AACf;cACD;AACD,oBAAM,YAAUpB,MAAA,KAAK,iBAAL,gBAAAA,IAAA,WAAoB,cAAa;AAEjD,kBAAI,WAAW,GAAG;AAChB,wBAAQ,WAAW,CAAA;AACnB;cACD;AACD,gCAAkB,WAChB,MAAM,QAAQ,WAAW,CAAA,GACzB,OAAA;YAEH;YACD,KAAK,UAAU;AAEb,mBAAA,CACI,SAAS,OAAO,QAAQ,SAAS,OAAO,SAAS,WACnD,SAAS,OAAO,GAGhB,eAAc,SAAS,OAAO;AAGhC,uBAAS,KAAK,QAAA;YACf;YACD,WAAW;AACT,uBAAS,SAAA;YACV;UACF,CAAA;QACF;AACD,eAAO,MAAM;AACX,gBAAM,YAAA;AACN,uBAAa,eAAA;QACd;MACF,CAAA;IACF;EACF;AACF;;ACpHD,WAAS,YAAY;AACnB,QAAI,IAAI,cAAA,OAAqB,kBAAkB,kBAAkB,SAAUqB,KAAGC,KAAG;AAC7E,UAAIC,MAAI,MAAA;AACR,aAAOA,IAAE,OAAO,mBAAmBA,IAAE,QAAQF,KAAGE,IAAE,aAAaD,KAAGC;IACnE,GACD,IAAI,CAAE,GACN,IAAI,CAAE;AACR,aAAS,MAAMF,KAAGC,KAAG;AACnB,UAAI,QAAQA,KAAG;AACb,YAAI,OAAOA,GAAAA,MAAOA,IAAG,OAAM,IAAI,UAAU,kFAAA;AACzC,YAAID,IAAG,KAAI,IAAIC,IAAE,OAAO,gBAAgB,OAAO,KAAA,EAAO,qBAAA,CAAsB;AAC5E,YAAA,WAAe,MAAM,IAAIA,IAAE,OAAO,WAAW,OAAO,KAAA,EAAO,gBAAA,CAAiB,GAAGD,KAAI,KAAI,IAAI;AAC3F,YAAI,cAAA,OAAqB,EAAG,OAAM,IAAI,UAAU,2BAAA;AAChD,cAAM,IAAI,SAASG,MAAI;AACrB,cAAI;AACF,cAAE,KAAKF,GAAAA;UACR,SAAQD,KAAG;AACV,mBAAO,QAAQ,OAAOA,GAAAA;UACvB;QACF,IAAG,EAAE,KAAK;UACT,GAAGC;UACH,GAAG;UACH,GAAGD;QACJ,CAAA;MACF,MAAM,QAAK,EAAE,KAAK;QACjB,GAAGC;QACH,GAAGD;MACJ,CAAA;AACD,aAAOC;IACR;AACD,WAAO;MACF;MACH,GAAG,MAAM,KAAK,MAAA,KAAO;MACrB,GAAG,MAAM,KAAK,MAAA,IAAO;MACrB,GAAG,SAAS,IAAI;AACd,YAAI,GACF,IAAI,KAAK,GACT,IAAI;AACN,iBAAS,OAAO;AACd,iBAAO,IAAI,EAAE,IAAA,IAAQ,KAAI;AACvB,gBAAA,CAAK,EAAE,KAAK,MAAM,EAAG,QAAO,IAAI,GAAG,EAAE,KAAK,CAAA,GAAI,QAAQ,QAAA,EAAU,KAAK,IAAA;AACrE,gBAAI,EAAE,GAAG;AACP,kBAAID,MAAI,EAAE,EAAE,KAAK,EAAE,CAAA;AACnB,kBAAI,EAAE,EAAG,QAAO,KAAK,GAAG,QAAQ,QAAQA,GAAAA,EAAG,KAAK,MAAM,GAAA;YACvD,MAAM,MAAK;UACb,SAAQA,KAAG;AACV,mBAAO,IAAIA,GAAAA;UACZ;AACD,cAAI,MAAM,EAAG,QAAO,MAAM,IAAI,QAAQ,OAAO,CAAA,IAAK,QAAQ,QAAA;AAC1D,cAAI,MAAM,EAAG,OAAM;QACpB;AACD,iBAAS,IAAIE,KAAG;AACd,iBAAO,IAAI,MAAM,IAAI,IAAI,EAAEA,KAAG,CAAA,IAAKA,KAAG,KAAA;QACvC;AACD,eAAO,KAAA;MACR;IACF;EACF;AACD,SAAO,UAAU,WAAW,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,SAAA,IAAa,OAAO;;;ACnBjG,SAAgB,mBACdE,MACmB;AACnB,QAAM,cAAc,eAAe,KAAK,WAAA;AAExC,QAAM,iBAAiB,CAACC,UAAmB;AACzC,QAAI,KAAK,YAEP,QAAO;AAGT,QAAI,UAAA,OACF,QAAO;AAET,UAAM,aAAa,KAAK,UAAU,YAAY,MAAM,UAAU,KAAA,CAAM;AACpE,UAAM,eAAe,KAAK,MAAM,YAAY,OAAO,YAAY,UAAA,CAAW;AAC1E,WAAO;EACR;AAED,SAAO,MACL,CAAC,EAAE,GAAA,MACD,WAAW,CAAC,aAAa;AACvB,QAAIC,MAAAA;AACJ,UAAM,KAAK,IAAI,gBAAA;AAEf,UAAM,SAAS,iBAAiB,GAAG,QAAQ,GAAG,MAAA;AAC9C,UAAM,gBAAgB,qBAAqB,MAAA;AAE3C,kBAAc,MAAM,MAAM;IAEzB,CAAA;AAED,QAAI,QAAQ,GAAG;AACf,mBAAe,aAAaC,UAAqC;AAC/D,cAAQ;AAER,YAAM,MAAM,KAAK,cAAA;AAEjB,aAAO,cAAc;QACnB,QAAQ,KAAK;QACb,MAAM,GAAG;QACT,aAAa,YAAY;QACzB;QACA,MAAM,GAAG;QACT;MACD,CAAA;IACF;AAED,aAAS,gBAAgBC,OAAgB;;AACvC,UAAI,aAAa,KAAA,EACf;AAEF,OAAA7B,MAAA,KAAK,YAAL,gBAAAA,IAAA,WAAe;QACb,OAAO,wBAAwB,KAAA;QAC/B,MAAM,GAAG;QACT,MAAM,GAAG;QACT;QACA;MACD;IACF;AAED,aAAS,wBAAwB6B,OAAgB;AAC/C,UAAI,kBAA2B,KAAA,EAC7B,QAAO;AAET,YAAM,QAAQ,wBAAwB,KAAA;AAEtC,YAAM,QAAQ,cAAkB;QAC9B,QAAQ,KAAK,OAAO,KAAK;QACzB;QACA;QACA;QACA,MAAM,GAAG;QACT,MAAM,GAAG;MACV,CAAA;AACD,aAAO,gBAAgB,KAAK,EAC1B,OAAO,eAAe,KAAA,EACvB,CAAA;IACF;AAED,QAAI,YAAY;AACd,cAAQ,GAAG,MAAX;QACE,KAAK;QACL,KAAK,YAAY;AACf,gBAAM,SAAS,MAAM,aAAa,GAAG,KAAA;AACrC,cAAA,CAAK,gBAAgB,MAAA,GAAS;AAC5B,qBAAS,KAAK,EACZ,QAAQ,EAAE,MAAM,eAAe,MAAA,EAAS,EACzC,CAAA;AACD,qBAAS,SAAA;AACT;UACD;AAED,mBAAS,KAAK,EACZ,QAAQ,EACN,MAAO,mBAAmB;;;AACxB,oBAAY,WAAA,YAAA,EAAW,iBAAiB,MAAA,CAAO;AAC/C,oBAAM,WAAA,YAAA,EAAW,aAAa,CAAE,GAAE,MAAM;AACtC,yBAAS,SAAA;cACV,CAAA,CAAC;AACF,kBAAI;AACF,uBAAO,MAAM;AACX,wBAAM,MAAM,MAAM,QAAQ,KAAK,CAC7B,SAAS,KAAA,GACT,aACD,CAAA;AACD,sBAAI,IAAI,KACN,QAAO,eAAe,IAAI,KAAA;AAE5B,wBAAM,eAAe,IAAI,KAAA;gBAC1B;cACF,SAAQ,OAAO;AACd,gCAAgB,KAAA;AAChB,sBAAM,wBAAwB,KAAA;cAC/B;;;;;;UACF,EAAA,EACF,EACF,CAAA;AACD;QACD;QACD,KAAK;AAAA,cAAA;;AACH,kBAAM,kBAAkB,gBAEtB;cACA,MAAM;cACN,OAAO;cACP,OAAO;YACR,CAAA;AAED,kBAAM,gBAAgB,gBAAgB,UAAU,EAC9C,KAAK,OAAO;AACV,uBAAS,KAAK,EACZ,QAAQ,MACT,CAAA;YACF,EACF,CAAA;AACD,gBAAIhB,cAAAA;AAEJ,kBAAM,WAAA,WAAA,EAAW,aAAa,CAAE,GAAE,YAAY;AAC5C,uBAAS,SAAA;AAET,8BAAgB,KAAK;gBACnB,MAAM;gBACN,OAAO;gBACP,OAAO;cACR,CAAA;AACD,4BAAc,YAAA;YACf,CAAA,CAAC;AACF,mBAAO,KAAA,KAAA;;AACL,oBAAM,SAAS,MAAM,aACnB,wBAAwB,GAAG,OAAO,WAAA,CAAY;AAEhD,kBAAA,CAAK,gBAAgB,MAAA,EACnB,OAAM,IAAI,MAAM,4BAAA;AAElB,oBAAY,WAAA,WAAA,EAAW,iBAAiB,MAAA,CAAO;AAE/C,uBAAS,KAAK,EACZ,QAAQ,EACN,MAAM,UACP,EACF,CAAA;AACD,8BAAgB,KAAK;gBACnB,MAAM;gBACN,OAAO;gBACP,OAAO;cACR,CAAA;AAGD,qBAAO,MAAM;AACX,oBAAI;AACJ,oBAAI;AACF,wBAAM,MAAM,QAAQ,KAAK,CAAC,SAAS,KAAA,GAAQ,aAAc,CAAA;gBAC1D,SAAQ,OAAO;AACd,sBAAI,aAAa,KAAA,EACf;AAEF,wBAAM,QAAQ,wBAAwB,KAAA;AAEtC,sBAAA,CACG,kBAAkB,SACjB,wBAAwB,MAAM,IAAA,CAAA,EAGhC,OAAM,wBAAwB,KAAA;AAGhC,kCAAgB,KAAA;AAChB,kCAAgB,KAAK;oBACnB,MAAM;oBACN,OAAO;oBACP,OAAO,wBAAwB,KAAA;kBAChC,CAAA;AAED;gBACD;AAED,oBAAI,IAAI,KACN;AAEF,oBAAIiB;AACJ,oBAAI,kBAAkB,IAAI,KAAA,GAAQ;AAChC,gCAAc,IAAI,MAAM,CAAA;AAExB,0BAAQ;oBACN,IAAI,IAAI,MAAM,CAAA;oBACd,MAAM;sBACJ,IAAI,IAAI,MAAM,CAAA;sBACd,MAAM,IAAI,MAAM,CAAA;oBACjB;kBACF;gBACF,MACC,SAAQ,EACN,MAAM,IAAI,MACX;AAGH,yBAAS,KAAK,EACZ,QAAQ;kBACN,GAAG;kBACH,MAAM,eAAe,MAAM,IAAA;gBAC5B,EACF,CAAA;cACF;;;;;;AAEH;;;;;;MAEH;IACF,CAAA,EAAE,MAAM,CAAC,UAAU;AAClB,sBAAgB,KAAA;AAChB,eAAS,MAAM,wBAAwB,KAAA,CAAM;IAC9C,CAAA;AAED,WAAO,MAAM;AACX,SAAG,MAAA;IACJ;EACF,CAAA;AACN;AAID,IAAaC,yBACX;", "names": ["subscribe: (observer: Observer<TValue, TError>) => TeardownLogic", "self: Observable<TV<PERSON><PERSON>, TError>", "teardownRef: TeardownLogic | null", "_a", "prev: any", "fn: UnaryFunction<any, any>", "observable: Observable<TValue, unknown>", "_opts?: ShareConfig", "subscription: Unsubscribable | null", "observers: <PERSON><PERSON><Observer<TV<PERSON>ue, TError>>[]", "_a", "observer: <PERSON><PERSON><Observer<TValue, TError>>", "initialValue: TValue", "value: TValue", "observerList: Observer<TValue, never>[]", "observer: Observer<TValue, never>", "nextValue: TValue", "opts: {\n  links: OperationLink<TRouter, TInput, TOutput>[];\n  op: Operation<TInput>;\n}", "value: TType | TType[]", "opts: {\n  condition: (op: Operation) => boolean;\n  /**\n   * The link to execute next if the test function returns `true`.\n   */\n  true: TRPCLink<TRouter> | TRPCLink<TRouter>[];\n  /**\n   * The link to execute next if the test function returns `false`.\n   */\n  false: TRPCLink<TRouter> | TRPCLink<TRouter>[];\n}", "retryableRpcCodes: TRPC_ERROR_CODE_NUMBER[]", "obj1: TType", "newObj: TType", "value: unknown", "fn: unknown", "obj: TObj", "fn: () => TValue", "obj: object", "callback: ProxyC<PERSON>back", "path: readonly string[]", "memo: Record<string, unknown>", "callback: (path: keyof TFaux) => any", "JSONRPC2_TO_HTTP_CODE: Record<\n  keyof typeof TRPC_ERROR_CODES_BY_KEY,\n  number\n>", "code: keyof typeof TRPC_ERROR_CODES_BY_KEY", "error: TRPCError", "opts: {\n  config: RootConfig<TRoot>;\n  error: TRPCError;\n  type: ProcedureType | 'unknown';\n  path: string | undefined;\n  input: unknown;\n  ctx: TRoot['ctx'] | undefined;\n}", "shape: DefaultErrorShape", "defaultFormatter: ErrorFormatter<any, any>", "cause: unknown", "opts: {\n    message?: string;\n    code: TRPC_ERROR_CODE_KEY;\n    cause?: unknown;\n  }", "transformer: DataTransformerOptions", "defaultTransformer: CombinedDataTransformer", "response:\n    | TRPCResponse<TOutput, inferRouterError<TRouter>>\n    | TRPCResponseMessage<TOutput, inferRouterError<TRouter>>", "transformer: DataTransformer", "result: ReturnType<typeof transformResultInner>", "fn: () => T", "result: T | typeof uncalled", "input: unknown", "value: unknown", "config: RootConfig<TRoot>", "input: TInput", "procedures: Record<string, AnyProcedure>", "lazy: Record<string, <PERSON><PERSON><PERSON><PERSON>der<AnyRouter>>", "opts: {\n      ref: <PERSON><PERSON><AnyRouter>;\n      path: readonly string[];\n      key: string;\n      aggregate: RouterRecord;\n    }", "router", "lazy", "from: CreateRouterOptions", "path: readonly string[]", "aggregate: RouterRecord", "_def: <PERSON><PERSON><PERSON><PERSON>['_def']", "router: BuiltRouter<TRoot, {}>", "procedureOrRouter: ValueOf<CreateRouterOptions>", "router: Pick<Router<any, any>, '_def'>", "path: string", "key", "opts: ProcedureCallOptions<unknown> & {\n    router: AnyRouter;\n    allowMethodOverride?: boolean;\n  }", "router: Pick<Router<TRoot, TRecord>, '_def'>", "ctx: Context | undefined", "_a", "value: unknown", "error: unknown", "arg: Promise<T> | PromiseLike<T> | PromiseExecutor<T>", "promise: Promise<T>", "unsubscribe: () => void", "onfulfilled?:\n      | ((value: T) => TResult1 | PromiseLike<TResult1>)\n      | null", "onrejected?:\n      | ((reason: any) => TResult2 | PromiseLike<TResult2>)\n      | null", "onrejected?:\n      | ((reason: any) => TResult | PromiseLike<TResult>)\n      | null", "onfinally?: (() => void) | null", "promise: Promise<PERSON>ike<T>", "value: T | PromiseLike<T>", "values: Iterable<T | PromiseLike<T>>", "promises: readonly <PERSON><PERSON><PERSON><PERSON>[]", "promise: T<PERSON><PERSON><PERSON>", "resolve!: PromiseWithResolvers<T>[\"resolve\"]", "reject!: PromiseWithResolvers<T>[\"reject\"]", "arr: readonly T[]", "member: T", "index: number", "member: unknown", "thing: T", "dispose: () => void", "dispose: () => Promise<void>", "ms: number", "timer: ReturnType<typeof setTimeout> | null", "r", "e", "n", "o", "iterable: AsyncIterable<T<PERSON>ield, TReturn, TNext>", "_a", "resolve: (value: TValue) => void", "reject: (error: unknown) => void", "data: unknown", "source: NodeJSReadableStreamEsque", "from: NodeJSReadableStreamEsque | WebReadableStreamEsque", "chunk: ChunkData", "abortController: AbortController", "originalController: ReadableStreamDefaultController<ChunkData>", "v: ChunkData", "reason: unknown", "chunkId: ChunkIndex", "opts: {\n  from: NodeJSReadableStreamEsque | WebReadableStreamEsque;\n  deserialize?: Deserialize;\n  onError?: ConsumerOnError;\n  formatError?: (opts: { error: unknown }) => Error;\n  /**\n   * This `AbortController` will be triggered when there are no more listeners to the stream.\n   */\n  abortController: AbortController;\n}", "headDeferred: null | Deferred<THead>", "value: ChunkDefinition", "value", "_a", "value: EncodedValue", "opts: {\n  promise: Promise<T>;\n  timeoutMs: number;\n  onTimeout: () => Promise<NoInfer<T>>;\n}", "opts: SSEStreamConsumerOptions<TConfig>", "clientOptions: SSEClientOptions", "_es: InstanceType<TConfig['EventSource']> | null", "options: SSEClientOptions", "def: <PERSON><PERSON><PERSON>", "res: Awaited<typeof promise>", "middlewares: AnyMiddlewareFunction[]", "fn: MiddlewareFunction<\n      TContext,\n      TMeta,\n      object,\n      $ContextOverrides,\n      TInputOut\n    >", "parse: ParseFn<TInput>", "inputMiddleware: AnyMiddlewareFunction", "parsedInput: ReturnType<typeof parse>", "parse: ParseFn<TOutput>", "outputMiddleware: AnyMiddlewareFunction", "issues: ReadonlyArray<StandardSchemaV1.Issue>", "_a", "procedureParser: <PERSON><PERSON><PERSON>", "def1: AnyProcedureBuilderDef", "def2: Partial<AnyProcedureBuilderDef>", "initDef: Partial<AnyProcedureBuilderDef>", "_def: AnyProcedureBuilderDef", "builder: AnyProcedureBuilder", "output: <PERSON><PERSON><PERSON>", "builder", "resolver: ProcedureResolver<any, any, any, any, any, any>", "_defIn: AnyProcedureBuilderDef & { type: ProcedureType }", "resolver: AnyResolver", "_def: AnyProcedure['_def']", "index: number", "opts: ProcedureCallOptions<any>", "_nextOpts?: any", "opts: ProcedureCallOptions<unknown>", "isServerDefault: boolean", "_b", "TRPCBuilder", "opts?: ValidateShape<TOptions, RuntimeConfigOptions<TContext, TMeta>>", "config: RootConfig<$Root>", "isServer: boolean", "cause: unknown", "obj: unknown", "err: unknown", "fallback: string", "TRPCClientError", "message: string", "opts?: {\n      result?: Maybe<TRPCErrorResponse<inferErrorShape<TRouterOrProcedure>>>;\n      cause?: Error;\n      meta?: Record<string, unknown>;\n    }", "_a", "_b", "_cause: Error | TRPCErrorResponse<any> | object", "opts: { meta?: Record<string, unknown> }", "transformer:\n    | TransformerOptions<{ transformer: false }>['transformer']\n    | TransformerOptions<{ transformer: true }>['transformer']\n    | undefined", "isFunction", "fn: unknown", "customFetchImpl?: FetchEsque | NativeFetchEsque", "opts: HTTPLinkBaseOptions<AnyClientTypes>", "array: unknown[]", "dict: Record<number, unknown>", "opts: GetInputOptions", "getUrl: GetUrl", "queryParts: string[]", "getBody: GetBody", "jsonHttpRequester: Requester", "signal: Maybe<AbortSignal>", "_a", "opts: HTTPRequestOptions", "input: unknown", "universalRequester: Requester", "opts: HTTPLinkOptions<TRouter['_def']['_config']['$types']>", "meta: HTTPResult['meta'] | undefined", "batchLoader: <PERSON><PERSON><PERSON><PERSON><PERSON><TKey, TValue>", "pendingItems: BatchItem<T<PERSON>ey, TValue>[] | null", "dispatchTimer: ReturnType<typeof setTimeout> | null", "items: BatchItem<TKey, TValue>[]", "groupedItems: BatchItem<TKey, TValue>[][]", "_a", "_b", "batch: <PERSON><PERSON><T<PERSON><PERSON>, TValue>", "key: <PERSON><PERSON><PERSON>", "item: BatchItem<TKey, TValue>", "signal: AbortSignal", "opts: HTTPBatchLinkOptions<TRouter['_def']['_config']['$types']>", "type: ProcedureType", "isFormData", "value: unknown", "opts: LoggerLinkFnOptions<any> & {\n    colorMode: ColorMode;\n    withContext?: boolean;\n  }", "parts: string[]", "args: any[]", "fn: 'error' | 'log'", "opts: LoggerLinkOptions<TRouter>", "result:\n            | OperationResultEnvelope<unknown, TRPCClientError<TRouter>>\n            | TRPCClientError<TRouter>", "lazyDefaults: LazyOptions", "keepAliveDefaults: KeepAliveOptions", "attemptIndex: number", "value: T | ((...args: TArgs) => T)", "TRPCWebSocketClosedError", "opts: { message: string; cause?: unknown }", "onTimeout: () => void", "timeoutMs: number", "withResolvers", "resolve: (value: T | PromiseLike<T>) => void", "reject: (reason?: any) => void", "urlOptions: UrlOptionsWithConnectionParams", "connectionParams: CallbackOrValue<TRPCRequestInfo['connectionParams']>", "message: TRPCConnectionParamsMessage", "message: TRPCClientOutgoingMessage", "callbacks: T<PERSON>allbacks", "messageId: MessageIdLike", "ws: WebSocket", "pingTimeout: ReturnType<typeof setTimeout> | undefined", "pongTimeout: ReturnType<typeof setTimeout> | undefined", "_a", "opts: WebSocketConnectionOptions", "connection: WsConnection", "opts: WebSocketClientOptions", "requestsToAwait: Promise<void>[]", "closedError: TRPCWebSocketClosedError", "cause: unknown", "_b", "message: TRPCResponseMessage", "message: TRPCClientIncomingRequest", "messageOrMessages: TRPCClientOutgoingMessage | TRPCClientOutgoingMessage[]", "message", "opts: WebSocketLinkOptions<TRouter>", "opts: CreateTRPCClientOptions<TInferrable>", "opts: {\n    type: TRPCType;\n    input: TInput;\n    path: string;\n    context?: OperationContext;\n    signal: Maybe<AbortSignal>;\n  }", "path: string", "input?: unknown", "opts?: TRPCRequestOptions", "input: unknown", "opts: Partial<\n      TRPCSubscriptionObserver<unknown, TRPCClientError<AnyRouter>>\n    > &\n      TRPCRequestOptions", "_a", "_b", "_c", "_d", "opts: CreateTRPCClientOptions<TRouter>", "clientCallTypeMap: Record<\n  keyof DecorateProcedure<any, any>,\n  ProcedureType\n>", "clientCallType: string", "client: TRPCUntypedClient<TRouter>", "client: TRPCClient<TRouter>", "opts: HTTPBatchLinkOptions<TRouter['_def']['_config']['$types']>", "type: ProcedureType", "opts", "json: TRPCResponse", "lastEventId: string | undefined", "opts: UrlOptionsWithConnectionParams", "opts: HTTPSubscriptionLinkOptions<\n    inferClientTypes<TInferrable>,\n    TEventSource\n  >", "result: TRPCResult<unknown>", "opts: RetryLinkOptions<TInferrable>", "next$: Unsubscribable", "callNextTimeout: ReturnType<typeof setTimeout> | undefined", "attempts: number", "r", "e", "n", "o", "opts: LocalLinkOptions<TRouter>", "chunk: unknown", "ctx: inferRouterContext<TRouter> | undefined", "newInput: unknown", "cause: unknown", "chunk: TRPCResult<unknown>", "experimental_localLink: typeof unstable_localLink"]}