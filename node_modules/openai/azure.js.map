{"version": 3, "file": "azure.js", "sourceRoot": "", "sources": ["src/azure.ts"], "names": [], "mappings": ";;;;AACA,2DAAkC;AAElC,+CAAkD;AAClD,wCAAiD;AACjD,mDAAmE;AAgCnE,4DAA4D;AAC5D,MAAa,WAAY,SAAQ,eAAM;IAKrC;;;;;;;;;;;;;;;;OAgBG;IACH,YAAY,EACV,OAAO,GAAG,IAAA,eAAO,EAAC,iBAAiB,CAAC,EACpC,MAAM,GAAG,IAAA,eAAO,EAAC,sBAAsB,CAAC,EACxC,UAAU,GAAG,IAAA,eAAO,EAAC,oBAAoB,CAAC,EAC1C,QAAQ,EACR,UAAU,EACV,oBAAoB,EACpB,uBAAuB,EACvB,GAAG,IAAI,KACe,EAAE;QACxB,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,MAAM,CAAC,WAAW,CAC1B,8MAA8M,CAC/M,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,oBAAoB,KAAK,UAAU,EAAE,CAAC;YAC/C,uBAAuB,GAAG,IAAI,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,oBAAoB,IAAI,CAAC,MAAM,EAAE,CAAC;YACrC,MAAM,IAAI,MAAM,CAAC,WAAW,CAC1B,sIAAsI,CACvI,CAAC;QACJ,CAAC;QAED,IAAI,oBAAoB,IAAI,MAAM,EAAE,CAAC;YACnC,MAAM,IAAI,MAAM,CAAC,WAAW,CAC1B,6GAA6G,CAC9G,CAAC;QACJ,CAAC;QAED,qDAAqD;QACrD,MAAM,KAAN,MAAM,GAAK,gBAAgB,EAAC;QAE5B,IAAI,CAAC,YAAY,GAAG,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;QAExE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YAClD,CAAC;YAED,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,MAAM,CAAC,WAAW,CAC1B,gHAAgH,CACjH,CAAC;YACJ,CAAC;YAED,OAAO,GAAG,GAAG,QAAQ,SAAS,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,IAAI,MAAM,CAAC,WAAW,CAAC,6CAA6C,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;QAED,KAAK,CAAC;YACJ,MAAM;YACN,OAAO;YACP,GAAG,IAAI;YACP,GAAG,CAAC,uBAAuB,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,uBAAuB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SAC9E,CAAC,CAAC;QA/EL,eAAU,GAAW,EAAE,CAAC;QAiFtB,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;QAClD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC;IACnC,CAAC;IAEQ,YAAY,CACnB,OAA4B,EAC5B,QAAiC,EAAE;QAEnC,IAAI,sBAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACxG,IAAI,CAAC,IAAA,aAAK,EAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;YAC3D,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC;YAC5F,IAAI,KAAK,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBAClE,OAAO,CAAC,IAAI,GAAG,gBAAgB,KAAK,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YACxD,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,IAAI,OAAO,IAAI,CAAC,qBAAqB,KAAK,UAAU,EAAE,CAAC;YACrD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACjD,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACxC,MAAM,IAAI,MAAM,CAAC,WAAW,CAC1B,+EAA+E,KAAK,EAAE,CACvF,CAAC;YACJ,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEkB,WAAW,CAAC,IAAyB;QACtD,OAAO;IACT,CAAC;IAEkB,KAAK,CAAC,cAAc,CAAC,IAAyB;QAC/D,IAAI,CAAC,OAAO,GAAG,IAAA,sBAAY,EAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAE5C;;;;;WAKG;QACH,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACnF,OAAO,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5C,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC,CAAC;QAC9D,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,gBAAgB,EAAE,CAAC;YAC5C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,MAAM,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC;QACxD,CAAC;QACD,OAAO,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;CACF;AAjJD,kCAiJC;AAED,MAAM,sBAAsB,GAAG,IAAI,GAAG,CAAC;IACrC,cAAc;IACd,mBAAmB;IACnB,aAAa;IACb,uBAAuB;IACvB,qBAAqB;IACrB,eAAe;IACf,qBAAqB;IACrB,UAAU;IACV,eAAe;CAChB,CAAC,CAAC;AAEH,MAAM,gBAAgB,GAAG,eAAe,CAAC"}